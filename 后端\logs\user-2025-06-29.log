[2025-06-29T08:33:17.108Z] 接收到验证重置码请求: 邮箱=<EMAIL>, 验证码=93****
[2025-06-29T08:33:17.108Z] 当前验证码Map中的邮箱: 
[2025-06-29T08:33:17.109Z] 未找到该邮箱的验证码: <EMAIL>
[2025-06-29T08:33:19.844Z] 接收到发送验证码请求: <EMAIL>
[2025-06-29T08:33:19.845Z] 为 <EMAIL> 生成验证码: 32****
[2025-06-29T08:33:19.845Z] 尝试发送验证码到: <EMAIL>
[2025-06-29T08:33:20.443Z] 验证码发送成功: {"RequestId":"EAA6A322-621F-5EA3-BAF0-6CD0BEADE9E0","EnvId":"600000202798617192"}
[2025-06-29T08:33:20.443Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-29T08:34:10.276Z] 接收到验证重置码请求: 邮箱=<EMAIL>, 验证码=32****
[2025-06-29T08:34:10.280Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-29T08:34:10.280Z] 找到验证码: <EMAIL>, 存储的验证码=32****, 提交的验证码=32****
[2025-06-29T08:34:10.281Z] 密码重置验证码验证成功: <EMAIL>, 用户ID=1751184219073
[2025-06-29T08:34:20.307Z] 接收到重置密码请求: 邮箱=<EMAIL>
[2025-06-29T08:34:20.308Z] 找到验证码: <EMAIL>, 存储的验证码=32****, 提交的验证码=32****
[2025-06-29T08:34:20.358Z] 用户成功重置密码: <EMAIL>, 用户ID=1751184219073
[2025-06-29T08:35:15.470Z] 登录失败: 密码错误 - <EMAIL>
[2025-06-29T08:35:20.492Z] 用户登录成功: <EMAIL>
[2025-06-29T11:12:01.639Z] 用户登录成功: 22222
