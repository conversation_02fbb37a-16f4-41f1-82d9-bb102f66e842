/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

a {
    text-decoration: none;
    color: #0c4da2;
}

ul {
    list-style: none;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    background-color: #0c4da2;
    color: #fff;
    border-radius: 5px;
    font-weight: bold;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #083778;
}

/* Header */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
}

header.scrolled {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    transition: box-shadow 0.3s ease;
}

.logo h1 {
    color: #0c4da2;
    font-size: 28px;
    margin-bottom: 5px;
}

.logo p {
    color: #666;
    font-size: 14px;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 30px;
}

nav ul li a {
    color: #333;
    font-weight: bold;
    transition: color 0.3s;
}

nav ul li a:hover {
    color: #0c4da2;
}

/* Hero Section */
#hero {
    background: linear-gradient(rgba(12, 77, 162, 0.8), rgba(8, 55, 120, 0.9));
    color: #fff;
    text-align: center;
    padding: 150px 0;
    margin-top: 80px;
}

#hero h2 {
    font-size: 36px;
    margin-bottom: 20px;
}

#hero p {
    font-size: 18px;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* About Section */
#about {
    padding: 80px 0;
    text-align: center;
    background-color: #f8f9fa;
}

#about h2 {
    font-size: 32px;
    margin-bottom: 20px;
    color: #0c4da2;
}

#about p {
    max-width: 800px;
    margin: 0 auto 40px;
}

.about-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.about-box {
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    width: calc(25% - 30px);
    min-width: 200px;
    transition: transform 0.3s;
}

.about-box:hover {
    transform: translateY(-10px);
}

.about-box i {
    font-size: 40px;
    color: #0c4da2;
    margin-bottom: 15px;
}

/* Services Section */
#services {
    padding: 80px 0;
}

#services h2 {
    font-size: 32px;
    margin-bottom: 40px;
    text-align: center;
    color: #0c4da2;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.service-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 30px;
    text-align: center;
    transition: transform 0.3s;
}

.service-card:hover {
    transform: translateY(-10px);
}

.service-card i {
    font-size: 40px;
    color: #0c4da2;
    margin-bottom: 20px;
}

.service-card h3 {
    margin-bottom: 15px;
    color: #333;
}

/* Contact Section */
#contact {
    padding: 80px 0;
    background-color: #f8f9fa;
}

#contact h2 {
    font-size: 32px;
    margin-bottom: 40px;
    text-align: center;
    color: #0c4da2;
}

.contact-flex {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 30px;
}

.contact-info {
    flex: 1;
    min-width: 300px;
}

.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.info-item i {
    font-size: 24px;
    color: #0c4da2;
    margin-right: 15px;
    min-width: 30px;
}

.contact-form {
    flex: 1;
    min-width: 300px;
}

.contact-form form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.contact-form input,
.contact-form textarea {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    outline: none;
}

.contact-form input.error,
.contact-form textarea.error {
    border-color: #ff3333;
    background-color: #ffeeee;
}

.contact-form textarea {
    resize: vertical;
}

/* Footer */
footer {
    background-color: #333;
    color: #fff;
    text-align: center;
    padding: 30px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    header .container {
        flex-direction: column;
    }

    nav ul {
        margin-top: 20px;
    }

    nav ul li {
        margin: 0 15px;
    }

    .about-box {
        width: calc(50% - 30px);
    }
}

@media (max-width: 576px) {
    nav ul {
        flex-direction: column;
        align-items: center;
    }

    nav ul li {
        margin: 10px 0;
    }

    .about-box {
        width: 100%;
    }
} 