<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>热销商品排行榜 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="js/blacklist-handler.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* 背景装饰 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 1;
        }

        /* Header */
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
        }

        .header-title {
            text-align: center;
            flex: 1;
        }

        .header-title h1 {
            font-size: 32px;
            font-weight: 700;
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .header-title h1::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
            border-radius: 2px;
        }

        .header-title p {
            color: #666;
            font-size: 15px;
            font-weight: 500;
        }

        .back-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 30px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .back-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .back-button:hover::before {
            left: 100%;
        }

        .back-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* Main Content */
        main {
            padding: 30px 0;
            min-height: calc(100vh - 150px);
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-header h2 {
            color: white;
            font-size: 32px;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-header p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
        }

        /* Hot Sales List */
        .hot-sales-list {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .hot-sales-list::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2, #ff6b6b, #667eea);
            background-size: 200% 100%;
            animation: gradientMove 3s ease-in-out infinite;
        }

        @keyframes gradientMove {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* 页面加载动画 */
        .page-header {
            animation: fadeInDown 0.8s ease-out;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 浮动效果 */
        .rank-number.top-3 {
            animation: goldGlow 2s ease-in-out infinite alternate, float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        /* 鼠标悬停时的特殊效果 */
        .rank-item:hover .rank-number.top-3 {
            animation: goldGlow 2s ease-in-out infinite alternate, float 3s ease-in-out infinite, spin 0.5s ease-in-out;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .rank-item {
                padding: 20px;
                margin-bottom: 15px;
            }

            .product-image {
                width: 70px;
                height: 70px;
                margin-right: 20px;
            }

            .rank-number {
                width: 50px;
                height: 50px;
                font-size: 18px;
                margin-right: 20px;
            }
        }

        .rank-item {
            display: flex;
            align-items: center;
            padding: 25px;
            border-radius: 20px;
            margin-bottom: 20px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .rank-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s;
        }

        .rank-item:hover::before {
            left: 100%;
        }

        .rank-item:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
            background: rgba(255, 255, 255, 1);
            border-color: rgba(102, 126, 234, 0.2);
        }

        .rank-item:active {
            transform: translateY(-2px) scale(1.01);
        }

        .rank-number {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 20px;
            margin-right: 25px;
            flex-shrink: 0;
            position: relative;
            transition: all 0.3s ease;
        }

        .rank-number.top-3 {
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            color: white;
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
            animation: goldGlow 2s ease-in-out infinite alternate;
        }

        @keyframes goldGlow {
            0% { box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4); }
            100% { box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6); }
        }

        .rank-number.top-3::before {
            content: '👑';
            position: absolute;
            top: -8px;
            right: -8px;
            font-size: 16px;
            animation: crown 2s ease-in-out infinite;
        }

        @keyframes crown {
            0%, 100% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(10deg) scale(1.1); }
        }

        .rank-number.top-10 {
            background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(192, 192, 192, 0.3);
        }

        .rank-number.others {
            background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
            color: #666;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .rank-item:hover .rank-number {
            transform: scale(1.1) rotate(5deg);
        }

        .product-image {
            width: 90px;
            height: 90px;
            border-radius: 15px;
            object-fit: cover;
            margin-right: 25px;
            flex-shrink: 0;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.5);
        }

        .rank-item:hover .product-image {
            transform: scale(1.05) rotate(2deg);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .product-info {
            flex: 1;
            min-width: 0;
        }

        .product-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.4;
            transition: color 0.3s ease;
        }

        .rank-item:hover .product-name {
            color: #667eea;
        }

        .product-price {
            font-size: 20px;
            font-weight: bold;
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            position: relative;
        }

        .product-price::before {
            content: '¥';
            position: absolute;
            left: -12px;
            top: 0;
            font-size: 16px;
            color: #ff6b6b;
        }

        .product-sales {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .sales-count {
            font-size: 15px;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .sales-count i {
            color: #667eea;
            font-size: 16px;
        }

        .rank-item:hover .sales-count {
            color: #333;
            transform: translateX(5px);
        }

        .sales-badge {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 6px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 3px 10px rgba(255, 107, 107, 0.3);
            animation: pulse 2s ease-in-out infinite;
            position: relative;
            overflow: hidden;
        }

        .sales-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Loading */
        .loading {
            text-align: center;
            padding: 80px 20px;
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            margin: 40px 0;
        }

        .loading i {
            font-size: 60px;
            margin-bottom: 25px;
            animation: spin 1s linear infinite;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .loading h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* No Data */
        .no-data {
            text-align: center;
            padding: 80px 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            margin: 40px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .no-data i {
            font-size: 80px;
            margin-bottom: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: #ddd;
        }

        /* Footer */
        footer {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 40px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        }

        .gold {
            color: #ffd700;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-title h1 {
                font-size: 24px;
            }

            .page-header h2 {
                font-size: 28px;
            }

            .hot-sales-list {
                padding: 20px;
            }

            .rank-item {
                padding: 15px;
            }

            .rank-number {
                width: 40px;
                height: 40px;
                font-size: 16px;
                margin-right: 15px;
            }

            .product-image {
                width: 60px;
                height: 60px;
                margin-right: 15px;
            }

            .product-name {
                font-size: 14px;
            }

            .product-price {
                font-size: 16px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 15px;
            }

            .header-title h1 {
                font-size: 20px;
            }

            .page-header h2 {
                font-size: 24px;
            }

            .hot-sales-list {
                padding: 15px;
            }

            .rank-item {
                padding: 12px;
                flex-direction: column;
                text-align: center;
            }

            .rank-number {
                margin-right: 0;
                margin-bottom: 10px;
            }

            .product-image {
                margin-right: 0;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="recommend.html" class="back-button">
                    <i class="fas fa-arrow-left"></i>返回商品页
                </a>

                <div class="header-title">
                    <h1><i class="fas fa-fire"></i>热销商品排行榜</h1>
                    <p>根据商品销量实时排序，为您推荐最受欢迎的商品</p>
                </div>

                <div style="width: 120px;"></div> <!-- 占位元素保持居中 -->
            </div>
        </div>
    </header>

    <main>
        <div class="container">


            <!-- 加载状态 -->
            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                <h3>正在加载热销商品排行榜...</h3>
            </div>

            <!-- 热销榜列表 -->
            <div class="hot-sales-list" id="hotSalesList" style="display: none;">
                <!-- 商品列表将通过JavaScript动态添加 -->
            </div>

            <!-- 无数据提示 -->
            <div class="no-data" id="noData" style="display: none;">
                <i class="fas fa-box-open"></i>
                <h3>暂无热销商品</h3>
                <p>目前还没有足够的销售数据，请稍后再来查看。</p>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2023 <span class="gold">金舟</span>国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </div>
    </footer>

    <script>
        // 热销榜管理器
        class HotSalesManager {
            constructor() {
                this.products = [];
                this.init();
            }

            init() {
                this.loadHotSalesProducts();
            }

            async loadHotSalesProducts() {
                try {
                    // 从服务器获取热销商品列表
                    const response = await fetch('/api/hot-sales');
                    const data = await response.json();

                    if (data.success) {
                        const serverProducts = data.products || [];

                        // 转换服务器数据格式
                        this.products = serverProducts.map(product => {
                            // 获取商品图片，优先使用images数组中的第一张图片，然后是mainImage，最后是默认图片
                            let productImage = 'https://via.placeholder.com/250x250/f0f0f0/999999?text=暂无图片';

                            if (product.images && product.images.length > 0) {
                                productImage = product.images[0].url;
                            } else if (product.mainImage) {
                                productImage = product.mainImage;
                            }

                            return {
                                id: product.id,
                                name: product.name,
                                price: parseFloat(product.price),
                                image: productImage,
                                // 移除评分功能
                                sales: product.sales || 0 // 使用服务器返回的销量数据
                            };
                        });

                        this.renderHotSalesList();
                    } else {
                        console.error('加载热销商品失败:', data.message);
                        this.showError('加载热销商品失败，请稍后重试');
                    }
                } catch (error) {
                    console.error('加载热销商品失败:', error);
                    this.showError('网络错误，请检查网络连接');
                }
            }

            renderHotSalesList() {
                const loading = document.getElementById('loading');
                const hotSalesList = document.getElementById('hotSalesList');
                const noData = document.getElementById('noData');

                loading.style.display = 'none';

                if (this.products.length === 0) {
                    noData.style.display = 'block';
                    return;
                }

                hotSalesList.style.display = 'block';
                hotSalesList.innerHTML = this.products.map((product, index) => {
                    const rank = index + 1;
                    let rankClass = 'others';
                    if (rank <= 3) rankClass = 'top-3';
                    else if (rank <= 10) rankClass = 'top-10';

                    return `
                        <div class="rank-item" onclick="viewProductDetail('${product.id}')" style="animation-delay: ${index * 0.1}s">
                            <div class="rank-number ${rankClass}">
                                ${rank}
                            </div>
                            <img src="${product.image}" alt="${product.name}" class="product-image"
                                 onerror="this.src='https://via.placeholder.com/90x90/f0f0f0/999999?text=暂无图片'">
                            <div class="product-info">
                                <div class="product-name">${product.name}</div>
                                <div class="product-price">${product.price.toFixed(2)}</div>
                                <div class="product-sales">
                                    <div class="sales-count">
                                        <i class="fas fa-shopping-cart"></i>
                                        已售 ${product.sales} 件
                                    </div>
                                    ${rank <= 10 ? '<div class="sales-badge">热销</div>' : ''}
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                // 添加入场动画
                this.addEntranceAnimation();
            }

            addEntranceAnimation() {
                const rankItems = document.querySelectorAll('.rank-item');
                rankItems.forEach((item, index) => {
                    item.style.opacity = '0';
                    item.style.transform = 'translateY(30px)';

                    setTimeout(() => {
                        item.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }

            showError(message) {
                const loading = document.getElementById('loading');
                const noData = document.getElementById('noData');

                loading.style.display = 'none';
                noData.style.display = 'block';
                noData.innerHTML = `
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>加载失败</h3>
                    <p>${message}</p>
                `;
            }
        }

        // 查看商品详情
        function viewProductDetail(productId) {
            window.open(`product-detail.html?id=${productId}&from=hotsales`, '_blank');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            new HotSalesManager();
        });
    </script>
</body>
</html>
