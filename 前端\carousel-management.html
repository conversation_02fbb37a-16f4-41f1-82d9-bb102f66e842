<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图管理 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .page-title {
            color: #0c4da2;
            font-size: 32px;
            margin-bottom: 0;
        }
        
        .back-button {
            display: inline-block;
            color: #0c4da2;
            text-decoration: none;
            font-size: 16px;
            background-color: #fff;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background-color: #f5f5f5;
        }

        .admin-info {
            display: flex;
            align-items: center;
        }
        
        .admin-avatar {
            width: 40px;
            height: 40px;
            background-color: #0c4da2;
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .logout-btn {
            color: #0c4da2;
            text-decoration: none;
            margin-left: 15px;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        
        .logout-btn:hover {
            background-color: #f0f0f0;
        }
        
        .card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .card-title {
            color: #0c4da2;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background-color: #0c4da2;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0a3d82;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }
        
        .btn-danger:hover {
            background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        }
        
        .carousel-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .carousel-item {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .carousel-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .carousel-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .carousel-content {
            padding: 20px;
        }
        
        .carousel-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #0c4da2;
        }
        
        .carousel-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .carousel-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 14px;
        }
        
        .no-carousels {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .no-carousels i {
            font-size: 48px;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }
            
            .header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .admin-info {
                margin-top: 15px;
            }
            
            .carousel-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo-container">
                <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="page-title">轮播图管理</h1>
            </div>
            <div class="admin-info">
                <div class="admin-avatar" id="adminInitial">A</div>
                <span id="adminName">管理员</span>
                <a href="#" class="logout-btn" id="logoutBtn">
                    <i class="fas fa-sign-out-alt"></i> 退出
                </a>
                <a href="admin-dashboard.html" class="back-button">
                    <i class="fas fa-arrow-left"></i> 返回控制台
                </a>
            </div>
        </header>
        
        <div class="card">
            <h2 class="card-title">轮播图管理</h2>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <p>管理首页轮播图，设置图片、标题和关联商品</p>
                <button class="btn btn-success" id="addCarouselBtn">
                    <i class="fas fa-plus"></i> 添加轮播图
                </button>
            </div>
            
            <div class="carousel-grid" id="carouselGrid">
                <!-- 轮播图列表将通过JavaScript动态添加 -->
            </div>
            
            <div class="no-carousels" id="noCarousels" style="display: none;">
                <i class="fas fa-images"></i>
                <h3>暂无轮播图</h3>
                <p>点击上方"添加轮播图"按钮开始创建您的第一个轮播图</p>
            </div>
        </div>
    </div>

    <script>
        // 轮播图管理类
        class CarouselManager {
            constructor() {
                this.carousels = [];
                this.init();
            }

            init() {
                this.checkAdminAuth();
                this.bindEvents();
                this.loadCarousels();
            }

            checkAdminAuth() {
                const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');
                
                if (!adminData.isAdmin) {
                    window.location.href = 'admin-login.html';
                    return null;
                }
                
                // 设置管理员信息
                const adminInitial = document.getElementById('adminInitial');
                const adminName = document.getElementById('adminName');
                
                if (adminData.username && adminInitial && adminName) {
                    adminName.textContent = adminData.username;
                    adminInitial.textContent = adminData.username.charAt(0).toUpperCase();
                }
                
                return adminData;
            }

            bindEvents() {
                // 退出登录
                document.getElementById('logoutBtn').addEventListener('click', (e) => {
                    e.preventDefault();
                    sessionStorage.removeItem('loggedInAdmin');
                    sessionStorage.removeItem('welcomeShown');
                    window.location.href = 'admin-login.html';
                });

                // 添加轮播图
                document.getElementById('addCarouselBtn').addEventListener('click', () => {
                    this.showCarouselEditor();
                });
            }

            loadCarousels() {
                // 从服务器加载轮播图数据
                fetch('/api/carousels')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.carousels = data.carousels || [];
                        this.renderCarousels();
                    } else {
                        console.error('加载轮播图失败:', data.message);
                        this.showError('加载轮播图失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('加载轮播图失败:', error);
                    this.showError('网络错误，无法连接到服务器');
                });
            }



            renderCarousels() {
                const grid = document.getElementById('carouselGrid');
                const noCarousels = document.getElementById('noCarousels');

                if (this.carousels.length === 0) {
                    grid.style.display = 'none';
                    noCarousels.style.display = 'block';
                    return;
                }

                grid.style.display = 'grid';
                noCarousels.style.display = 'none';
                
                grid.innerHTML = this.carousels.map(carousel => this.createCarouselCard(carousel)).join('');
            }

            createCarouselCard(carousel) {
                const imageDisplay = carousel.image 
                    ? `<img src="${carousel.image}" alt="${carousel.title}" class="carousel-image">`
                    : `<div class="carousel-image"><i class="fas fa-image"></i> 暂无图片</div>`;

                return `
                    <div class="carousel-item">
                        ${imageDisplay}
                        <div class="carousel-content">
                            <div class="carousel-title">${carousel.title}</div>
                            <div class="carousel-actions">
                                <button class="btn btn-primary btn-sm" onclick="carouselManager.editCarousel('${carousel.id}')">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="carouselManager.deleteCarousel('${carousel.id}')">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }

            showCarouselEditor(carouselId = null) {
                // 跳转到轮播图编辑页面
                const url = carouselId ? `carousel-editor.html?id=${carouselId}` : 'carousel-editor.html';
                window.location.href = url;
            }

            editCarousel(carouselId) {
                this.showCarouselEditor(carouselId);
            }

            deleteCarousel(carouselId) {
                if (confirm('确定要删除这个轮播图吗？此操作不可恢复。')) {
                    // 显示删除中状态
                    const deleteBtn = document.querySelector(`button[onclick="carouselManager.deleteCarousel('${carouselId}')"]`);
                    const originalText = deleteBtn.textContent;
                    deleteBtn.textContent = '删除中...';
                    deleteBtn.disabled = true;

                    // 发送删除请求到服务器
                    fetch(`/api/carousels/${carouselId}`, {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 从本地数组中移除
                            this.carousels = this.carousels.filter(c => c.id !== carouselId);
                            // 重新渲染轮播图列表
                            this.renderCarousels();
                            this.showToast('轮播图删除成功', 'success');
                        } else {
                            throw new Error(data.message || '删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('删除轮播图失败:', error);
                        this.showToast('删除失败：' + error.message, 'error');
                        // 恢复按钮状态
                        deleteBtn.textContent = originalText;
                        deleteBtn.disabled = false;
                    });
                }
            }

            showError(message) {
                const grid = document.getElementById('carouselGrid');
                grid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #e74c3c;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i><br>
                        ${message}
                        <br><br>
                        <button onclick="location.reload()" class="btn btn-primary">重新加载</button>
                    </div>
                `;
            }

            showToast(message, type = 'info') {
                // 简单的提示实现
                alert(message);
            }
        }

        // 初始化轮播图管理器
        let carouselManager;
        document.addEventListener('DOMContentLoaded', function() {
            carouselManager = new CarouselManager();
        });
    </script>
</body>
</html>
