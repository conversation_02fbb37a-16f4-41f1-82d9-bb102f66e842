<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>找回密码 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/blacklist-handler.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .page-title {
            color: #0c4da2;
            font-size: 32px;
            margin-bottom: 0;
        }
        
        .back-button {
            display: inline-block;
            color: #0c4da2;
            text-decoration: none;
            font-size: 16px;
            background-color: #fff;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background-color: #f5f5f5;
        }
        
        .reset-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .reset-tabs {
            display: flex;
            margin-bottom: 25px;
            border-bottom: 1px solid #eee;
        }
        
        .reset-tab {
            flex: 1;
            text-align: center;
            padding: 10px 0;
            font-size: 16px;
            font-weight: bold;
            color: #666;
            cursor: pointer;
            position: relative;
        }
        
        .reset-tab.active {
            color: #0c4da2;
        }
        
        .reset-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #0c4da2;
        }
        
        .reset-methods {
            display: none;
        }
        
        .reset-methods.active {
            display: block;
        }
        
        .step-container {
            margin-bottom: 20px;
        }
        
        .step-title {
            font-size: 16px;
            color: #0c4da2;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #0c4da2;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            border-color: #0c4da2;
            outline: none;
        }
        
        .verification-container {
            display: flex;
            gap: 10px;
        }
        
        .verification-container input {
            flex: 1;
        }
        
        .verification-button {
            white-space: nowrap;
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 0 15px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .verification-button:hover {
            background-color: #083778;
        }
        
        .verification-button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        
        .submit-button {
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
            width: 100%;
        }
        
        .submit-button:hover {
            background-color: #083778;
        }
        
        .wechat-reset {
            text-align: center;
            padding: 20px 0;
        }
        
        .wechat-qrcode {
            display: inline-block;
            background-color: #f5f5f5;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .wechat-icon {
            font-size: 100px;
            color: #07C160;
            margin-bottom: 15px;
        }
        
        .wechat-text {
            color: #666;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        .login-link {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .login-link a {
            color: #0c4da2;
            text-decoration: none;
            font-weight: bold;
        }
        
        .login-link a:hover {
            text-decoration: underline;
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            color: #666;
            font-size: 14px;
        }
        
        /* 优雅的通知系统 */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            max-width: 350px;
        }
        
        .toast {
            display: flex;
            align-items: center;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            margin-bottom: 15px;
            padding: 12px 20px;
            overflow: hidden;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        .toast.toast-success {
            border-left: 5px solid #28a745;
        }
        
        .toast.toast-error {
            border-left: 5px solid #dc3545;
        }
        
        .toast.toast-info {
            border-left: 5px solid #17a2b8;
        }
        
        .toast.toast-warning {
            border-left: 5px solid #ffc107;
        }
        
        .toast-icon {
            margin-right: 15px;
            font-size: 20px;
        }
        
        .toast-success .toast-icon {
            color: #28a745;
        }
        
        .toast-error .toast-icon {
            color: #dc3545;
        }
        
        .toast-info .toast-icon {
            color: #17a2b8;
        }
        
        .toast-warning .toast-icon {
            color: #ffc107;
        }
        
        .toast-content {
            flex: 1;
        }
        
        .toast-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .toast-message {
            font-size: 14px;
            color: #666;
        }
        
        .toast-close {
            color: #999;
            font-size: 18px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .toast-close:hover {
            color: #333;
        }
        
        .toast-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background-color: rgba(0, 0, 0, 0.1);
            width: 100%;
        }
        
        .toast-progress-bar {
            height: 3px;
            width: 100%;
        }
        
        .toast-success .toast-progress-bar {
            background-color: #28a745;
        }
        
        .toast-error .toast-progress-bar {
            background-color: #dc3545;
        }
        
        .toast-info .toast-progress-bar {
            background-color: #17a2b8;
        }
        
        .toast-warning .toast-progress-bar {
            background-color: #ffc107;
        }
        
        /* 模态对话框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background-color: #fff;
            margin: 15% auto;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
            position: relative;
            max-width: 420px;
            transform: scale(0.8);
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .modal.show .modal-content {
            transform: scale(1);
            opacity: 1;
        }
        
        .modal-title {
            color: #0c4da2;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
            font-size: 20px;
        }
        
        .modal-text {
            margin-bottom: 25px;
            text-align: center;
            color: #555;
            font-size: 16px;
        }
        
        .modal-btn {
            display: block;
            width: 100%;
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .modal-btn:hover {
            background-color: #083778;
        }
        
        .modal-icon {
            display: block;
            text-align: center;
            font-size: 48px;
            margin-bottom: 20px;
            color: #0c4da2;
        }
        
        /* 图片验证码弹窗样式 */
        .captcha-modal {
            display: none;
            position: fixed;
            z-index: 1100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .captcha-content {
            background-color: #fff;
            margin: 15% auto;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
            position: relative;
            max-width: 350px;
            transform: scale(0.8);
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .captcha-modal.show .captcha-content {
            transform: scale(1);
            opacity: 1;
        }
        
        .captcha-title {
            color: #0c4da2;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
            font-size: 18px;
        }
        
        .captcha-container {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .captcha-image {
            height: 80px;
            background-color: #f5f5f5;
            border-radius: 5px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
            user-select: none;
        }
        
        .captcha-image canvas {
            cursor: pointer;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .captcha-refresh {
            position: absolute;
            right: 10px;
            bottom: 10px;
            color: #0c4da2;
            background-color: rgba(255, 255, 255, 0.8);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .captcha-input-container {
            display: flex;
            margin-bottom: 20px;
        }
        
        .captcha-input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .captcha-verify-btn {
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 0 20px;
            border-radius: 5px;
            margin-left: 10px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .captcha-verify-btn:hover {
            background-color: #083778;
        }
        
        .captcha-footer {
            text-align: center;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-container">
                <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="page-title">找回密码</h1>
            </div>
            <a href="login.html" class="back-button">返回登录</a>
        </div>
        
        <div class="reset-container">
            <div class="reset-tabs">
                <div class="reset-tab" onclick="switchTab('wechat')">微信找回</div>
                <div class="reset-tab active" onclick="switchTab('email')">邮箱找回</div>
            </div>
            
            <div id="wechatReset" class="reset-methods">
                <div class="wechat-reset">
                    <div class="wechat-qrcode">
                        <img src="img/wechat-qrcode.png" alt="微信二维码" style="width:150px;">
                    </div>
                    <div class="wechat-text">请使用微信扫描二维码找回密码</div>
                    <div class="wechat-text">或在微信中搜索"金舟国际物流"小程序</div>
                </div>
            </div>
            
            <div id="emailReset" class="reset-methods active">
                <form id="emailResetForm" class="reset-form">
                    <div class="step-container" id="stepOne">
                        <div class="step-title">步骤 1：验证您的邮箱</div>
                        
                        <div class="form-group">
                            <label for="email">邮箱地址</label>
                            <div class="verification-container">
                                <input type="email" id="email" name="email" placeholder="请输入注册时使用的邮箱" maxlength="320" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="verificationCode">验证码</label>
                            <div class="verification-container">
                                <input type="text" id="verificationCode" name="verificationCode" placeholder="请输入验证码" maxlength="6" required>
                                <button type="button" id="sendCodeBtn" class="verification-button">发送验证码</button>
                            </div>
                        </div>
                        
                        <button type="button" id="verifyEmailBtn" class="submit-button">验证邮箱</button>
                    </div>
                    
                    <div class="step-container" id="stepTwo" style="display: none;">
                        <div class="step-title">步骤 2：设置新密码</div>
                        
                        <div class="form-group">
                            <label for="newPassword">新密码</label>
                            <input type="password" id="newPassword" name="newPassword" placeholder="请输入新密码（至少6个字符）" maxlength="64" required minlength="6">
                        </div>
                        
                        <div class="form-group">
                            <label for="confirmPassword">确认新密码</label>
                            <input type="password" id="confirmPassword" name="confirmPassword" placeholder="请再次输入新密码" maxlength="64" required minlength="6">
                        </div>
                        
                        <button type="submit" class="submit-button">重置密码</button>
                    </div>
                </form>
            </div>
            
            <div class="login-link">
                已有账号？<a href="login.html">立即登录</a>
            </div>
        </div>
        
        <footer>
            <p>&copy; 2023 金舟国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </footer>
    </div>
    
    <!-- 模态弹窗 -->
    <div id="message-modal" class="modal">
        <div class="modal-content">
            <div class="modal-icon" id="modal-icon"><i class="fas fa-check-circle"></i></div>
            <h3 class="modal-title" id="modal-title"></h3>
            <p class="modal-text" id="modal-text"></p>
            <button class="modal-btn" id="modal-btn">确定</button>
        </div>
    </div>
    
    <!-- 优雅提示的容器 -->
    <div class="toast-container" id="toast-container"></div>
    
    <!-- 图片验证码弹窗 -->
    <div id="captcha-modal" class="captcha-modal">
        <div class="captcha-content">
            <div class="captcha-title">请完成安全验证</div>
            <div class="captcha-container">
                <div class="captcha-image">
                    <canvas id="captcha-canvas" width="300" height="80"></canvas>
                    <div class="captcha-refresh" id="refresh-captcha"><i class="fas fa-sync-alt"></i></div>
                </div>
                <div class="captcha-input-container">
                    <input type="text" id="captcha-input" class="captcha-input" placeholder="请输入验证码" maxlength="6">
                    <button class="captcha-verify-btn" id="verify-captcha">验证</button>
                </div>
            </div>
            <div class="captcha-footer">
                点击图片可刷新验证码
            </div>
        </div>
    </div>
    
    <script>
        // API地址 - 后端服务地址
        const API_BASE_URL = 'http://localhost:8080';
        
        // 显示模态弹窗的函数
        function showModal(title, text, callback, type = 'success') {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-text').textContent = text;
            
            // 设置图标
            const modalIcon = document.getElementById('modal-icon');
            if (type === 'success') {
                modalIcon.innerHTML = '<i class="fas fa-check-circle" style="color: #28a745;"></i>';
            } else if (type === 'error') {
                modalIcon.innerHTML = '<i class="fas fa-times-circle" style="color: #dc3545;"></i>';
            } else if (type === 'info') {
                modalIcon.innerHTML = '<i class="fas fa-info-circle" style="color: #17a2b8;"></i>';
            } else if (type === 'warning') {
                modalIcon.innerHTML = '<i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>';
            }
            
            const modal = document.getElementById('message-modal');
            modal.style.display = 'block';
            
            // 添加显示类以触发动画
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
            
            document.getElementById('modal-btn').onclick = function() {
                modal.classList.remove('show');
                
                // 等待动画完成后隐藏
                setTimeout(() => {
                    modal.style.display = 'none';
                    if (callback) callback();
                }, 300);
            };
        }

        // 显示优雅的toast通知
        function showToast(message, type = 'info', title = '', duration = 5000) {
            const container = document.getElementById('toast-container');
            
            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            
            // 设置图标
            let iconClass = 'info-circle';
            if (type === 'success') iconClass = 'check-circle';
            if (type === 'error') iconClass = 'times-circle';
            if (type === 'warning') iconClass = 'exclamation-triangle';
            
            // 构建toast内容
            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="fas fa-${iconClass}"></i>
                </div>
                <div class="toast-content">
                    ${title ? `<div class="toast-title">${title}</div>` : ''}
                    <div class="toast-message">${message}</div>
                </div>
                <div class="toast-close">
                    <i class="fas fa-times"></i>
                </div>
                <div class="toast-progress">
                    <div class="toast-progress-bar"></div>
                </div>
            `;
            
            // 添加到容器
            container.appendChild(toast);
            
            // 触发动画
            setTimeout(() => {
                toast.classList.add('show');
                
                // 设置进度条动画
                const progressBar = toast.querySelector('.toast-progress-bar');
                progressBar.style.transition = `width ${duration}ms linear`;
                progressBar.style.width = '0';
            }, 10);
            
            // 设置关闭事件
            const closeBtn = toast.querySelector('.toast-close');
            closeBtn.addEventListener('click', () => {
                removeToast(toast);
            });
            
            // 自动关闭
            const closeTimeout = setTimeout(() => {
                removeToast(toast);
            }, duration);
            
            // 移除Toast的函数
            function removeToast(toast) {
                clearTimeout(closeTimeout);
                toast.classList.remove('show');
                
                // 动画结束后移除DOM
                setTimeout(() => {
                    if (container.contains(toast)) {
                        container.removeChild(toast);
                    }
                }, 300);
            }
        }
        
        // API请求函数
        async function callAPI(endpoint, data, method = 'POST') {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };
                
                if (method === 'POST') {
                    options.body = JSON.stringify(data);
                } else if (method === 'GET' && data) {
                    endpoint = `${endpoint}?${new URLSearchParams(data)}`;
                }
                
                const url = `${API_BASE_URL}/api/${endpoint}`;
                console.log(`发起API请求: ${url}`, data);
                
                const response = await fetch(url, options);
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log(`API响应: ${url}`, result);
                return result;
            } catch (error) {
                console.error(`API请求失败: ${endpoint}`, error);
                return { success: false, message: '服务器连接失败，请检查后端服务是否运行' };
            }
        }
        
        function switchTab(tab) {
            // Remove active class from all tabs and content
            document.querySelectorAll('.reset-tab').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.reset-methods').forEach(item => {
                item.classList.remove('active');
            });
            
            // Add active class to selected tab and content
            if (tab === 'wechat') {
                document.querySelector('.reset-tab:nth-child(1)').classList.add('active');
                document.getElementById('wechatReset').classList.add('active');
            } else if (tab === 'email') {
                document.querySelector('.reset-tab:nth-child(2)').classList.add('active');
                document.getElementById('emailReset').classList.add('active');
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            const verifyEmailBtn = document.getElementById('verifyEmailBtn');
            const emailResetForm = document.getElementById('emailResetForm');
            const emailInput = document.getElementById('email');
            const verificationCodeInput = document.getElementById('verificationCode');
            const stepOne = document.getElementById('stepOne');
            const stepTwo = document.getElementById('stepTwo');
            
            // 监听所有设置了maxlength属性的输入框
            document.querySelectorAll('input[maxlength]').forEach(input => {
                // 记录上一次显示提示的时间
                let lastToastTime = 0;

                // 使用keydown事件，可以捕获到用户尝试输入但被maxlength阻止的情况
                input.addEventListener('keydown', function(e) {
                    const maxLength = parseInt(this.getAttribute('maxlength'));
                    const currentTime = new Date().getTime();
                    
                    // 如果已经达到最大长度且不是功能键（如退格键、方向键等）
                    if (this.value.length >= maxLength && 
                        !e.ctrlKey && !e.altKey && 
                        e.key.length === 1 && 
                        currentTime - lastToastTime > 1000) { // 限制提示频率，至少间隔1秒
                        
                        showToast(`输入已达到最大限制${maxLength}个字符`, 'warning', '字符限制');
                        lastToastTime = currentTime;
                    }
                });
                
                // 保留input事件监听，用于处理粘贴等操作
                input.addEventListener('input', function(e) {
                    const maxLength = parseInt(this.getAttribute('maxlength'));
                    const currentTime = new Date().getTime();
                    
                    if (this.value.length >= maxLength && currentTime - lastToastTime > 1000) {
                        showToast(`输入已达到最大限制${maxLength}个字符`, 'warning', '字符限制');
                        lastToastTime = currentTime;
                    }
                });
            });
            
            // 发送验证码
            sendCodeBtn.addEventListener('click', async function() {
                const email = emailInput.value.trim();
                
                if (!email) {
                    showToast('请输入邮箱地址', 'warning', '验证失败');
                    return;
                }
                
                if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                    showToast('请输入有效的邮箱地址', 'warning', '格式错误');
                    return;
                }
                
                console.log('尝试发送验证码到:', email);
                
                // 显示图片验证码
                showCaptchaModal();
            });
            
            // 验证邮箱
            verifyEmailBtn.addEventListener('click', async function() {
                const email = emailInput.value.trim();
                const verificationCode = verificationCodeInput.value.trim();
                
                if (!email) {
                    showToast('请输入邮箱地址', 'warning', '验证失败');
                    return;
                }
                
                if (!verificationCode) {
                    showToast('请输入验证码', 'warning', '验证失败');
                    return;
                }
                
                // 显示处理中状态
                verifyEmailBtn.disabled = true;
                verifyEmailBtn.textContent = '验证中...';
                
                try {
                    console.log(`尝试验证邮箱: ${email}, 验证码: ${verificationCode}`);
                    
                    const result = await callAPI('verify-reset-code', { 
                        email: email,
                        verificationCode: verificationCode 
                    });
                    
                    console.log('验证结果:', result);
                    
                    if (result.success) {
                        console.log('验证成功，显示第二步');
                        // 验证成功，显示第二步
                        showToast('验证码验证成功', 'success', '验证成功');
                        stepOne.style.display = 'none';
                        stepTwo.style.display = 'block';
                    } else {
                        // 检查是否是拉黑用户
                        if (result.blacklisted) {
                            BlacklistHandler.showBlacklistedMessage(result.message || BlacklistHandler.DEFAULT_BLACKLIST_MESSAGE);
                        } else {
                            console.log('验证失败:', result.message);
                            showToast(result.message || '验证码验证失败，请检查后重试', 'error', '验证失败');
                        }
                        // 重新启用按钮
                        verifyEmailBtn.disabled = false;
                        verifyEmailBtn.textContent = '验证邮箱';
                    }
                } catch (error) {
                    console.error('验证失败:', error);
                    showToast('验证过程中出错，请稍后重试', 'error', '系统错误');
                    // 重新启用按钮
                    verifyEmailBtn.disabled = false;
                    verifyEmailBtn.textContent = '验证邮箱';
                }
            });
            
            // 重置密码表单提交
            emailResetForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const email = emailInput.value.trim();
                const verificationCode = verificationCodeInput.value.trim();
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                
                if (newPassword !== confirmPassword) {
                    showToast('两次输入的密码不一致', 'warning', '验证失败');
                    return;
                }
                
                const passwordPattern = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,64}$/;
                if (!passwordPattern.test(newPassword)) {
                    showToast('密码必须为8-64位，且同时包含英文字母和数字', 'warning', '验证失败');
                    return;
                }
                
                const submitBtn = emailResetForm.querySelector('button[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.textContent = '处理中...';
                
                try {
                    console.log(`尝试重置密码: ${email}, 验证码: ${verificationCode}`);
                    
                    const result = await callAPI('reset-password', { 
                        email: email, 
                        newPassword: newPassword,
                        verificationCode: verificationCode
                    });
                    
                    console.log('重置结果:', result);
                    
                    if (result.success) {
                        console.log('密码重置成功');
                        showModal('密码重置成功', '请使用新密码登录', function() {
                            window.location.href = 'login.html';
                        }, 'success');
                    } else {
                        // 检查是否是拉黑用户
                        if (result.blacklisted) {
                            BlacklistHandler.showBlacklistedMessage(result.message || BlacklistHandler.DEFAULT_BLACKLIST_MESSAGE);
                        } else {
                            console.log('密码重置失败:', result.message);
                            showToast(result.message || '密码重置失败，请稍后重试', 'error', '重置失败');
                            submitBtn.disabled = false;
                            submitBtn.textContent = '重置密码';
                        }
                    }
                } catch (error) {
                    console.error('密码重置失败:', error);
                    showToast('密码重置过程中出错，请稍后重试', 'error', '系统错误');
                    submitBtn.disabled = false;
                    submitBtn.textContent = '重置密码';
                }
            });
        });
        
        // 图片验证码功能
        class ImageCaptcha {
            constructor() {
                this.canvas = document.getElementById('captcha-canvas');
                this.ctx = this.canvas.getContext('2d');
                this.captchaText = '';
                this.captchaLength = 4; // 验证码长度
                this.verified = false;
                
                // 绑定事件处理器
                this.bindEvents();
                
                // 初始化验证码
                this.refresh();
            }
            
            // 绑定事件
            bindEvents() {
                // 点击刷新图片验证码
                document.getElementById('refresh-captcha').addEventListener('click', () => this.refresh());
                this.canvas.addEventListener('click', () => this.refresh());
                
                // 验证按钮点击
                document.getElementById('verify-captcha').addEventListener('click', () => {
                    this.verify();
                });
                
                // 输入框按下回车键也可验证
                document.getElementById('captcha-input').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.verify();
                    }
                });
            }
            
            // 生成随机验证码
            generateCaptchaText() {
                const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
                let result = '';
                for (let i = 0; i < this.captchaLength; i++) {
                    result += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                return result;
            }
            
            // 刷新验证码
            refresh() {
                // 生成新验证码文本
                this.captchaText = this.generateCaptchaText();
                
                // 清空画布
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                
                // 设置背景
                this.ctx.fillStyle = '#f0f2f5';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                // 添加干扰线
                this.drawInterferenceLines(5);
                
                // 添加干扰点
                this.drawInterferenceDots(50);
                
                // 绘制验证码文本
                this.drawCaptchaText();
                
                // 重置输入框和验证状态
                document.getElementById('captcha-input').value = '';
                this.verified = false;
            }
            
            // 绘制干扰线
            drawInterferenceLines(count) {
                for (let i = 0; i < count; i++) {
                    this.ctx.strokeStyle = this.getRandomColor(40, 180);
                    this.ctx.beginPath();
                    this.ctx.moveTo(Math.random() * this.canvas.width, Math.random() * this.canvas.height);
                    this.ctx.lineTo(Math.random() * this.canvas.width, Math.random() * this.canvas.height);
                    this.ctx.lineWidth = Math.random() * 2 + 1;
                    this.ctx.stroke();
                }
            }
            
            // 绘制干扰点
            drawInterferenceDots(count) {
                for (let i = 0; i < count; i++) {
                    this.ctx.fillStyle = this.getRandomColor(0, 255);
                    this.ctx.beginPath();
                    this.ctx.arc(Math.random() * this.canvas.width, Math.random() * this.canvas.height, 
                                 Math.random() * 2 + 1, 0, Math.PI * 2);
                    this.ctx.fill();
                }
            }
            
            // 绘制验证码文本
            drawCaptchaText() {
                const textWidth = this.canvas.width / (this.captchaLength + 1);
                
                for (let i = 0; i < this.captchaText.length; i++) {
                    const char = this.captchaText[i];
                    
                    // 随机颜色
                    this.ctx.fillStyle = this.getRandomColor(10, 100);
                    
                    // 随机字体大小
                    const fontSize = Math.random() * 10 + 28;
                    
                    // 随机旋转角度
                    const rotation = (Math.random() - 0.5) * 0.4;
                    
                    // 保存当前状态
                    this.ctx.save();
                    
                    // 位置计算，让字符均匀分布
                    const x = textWidth * (i + 1);
                    const y = this.canvas.height / 2 + 10;
                    
                    // 设置旋转中心点
                    this.ctx.translate(x, y);
                    this.ctx.rotate(rotation);
                    
                    // 设置字体
                    this.ctx.font = `bold ${fontSize}px Arial`;
                    this.ctx.textAlign = 'center';
                    this.ctx.textBaseline = 'middle';
                    
                    // 绘制文本
                    this.ctx.fillText(char, 0, 0);
                    
                    // 恢复状态
                    this.ctx.restore();
                }
            }
            
            // 获取随机颜色
            getRandomColor(min, max) {
                const r = Math.floor(Math.random() * (max - min) + min);
                const g = Math.floor(Math.random() * (max - min) + min);
                const b = Math.floor(Math.random() * (max - min) + min);
                return `rgb(${r}, ${g}, ${b})`;
            }
            
            // 验证用户输入
            verify() {
                const userInput = document.getElementById('captcha-input').value.trim();
                
                if (!userInput) {
                    showToast('请输入验证码', 'warning', '验证失败');
                    return;
                }
                
                // 验证码不区分大小写
                if (userInput.toLowerCase() === this.captchaText.toLowerCase()) {
                    this.verified = true;
                    showToast('验证成功', 'success', '安全验证');
                    
                    // 关闭验证码模态窗口
                    hideCaptchaModal();
                    
                    // 继续发送邮箱验证码的流程
                    continueSendEmailCode();
                } else {
                    showToast('验证码错误，请重新输入', 'error', '验证失败');
                    this.refresh();
                }
            }
            
            // 获取验证状态
            isVerified() {
                return this.verified;
            }
        }
        
        // 显示验证码模态窗口
        function showCaptchaModal(callback) {
            const modal = document.getElementById('captcha-modal');
            modal.style.display = 'block';
            
            // 保存回调函数
            window.captchaCallback = callback;
            
            // 添加显示类以触发动画
            setTimeout(() => {
                modal.classList.add('show');
                
                // 初始化图片验证码
                if (!window.imageCaptcha) {
                    window.imageCaptcha = new ImageCaptcha();
                } else {
                    window.imageCaptcha.refresh();
                }
                
                // 聚焦到输入框
                document.getElementById('captcha-input').focus();
            }, 10);
        }
        
        // 隐藏验证码模态窗口
        function hideCaptchaModal() {
            const modal = document.getElementById('captcha-modal');
            modal.classList.remove('show');
            
            // 等待动画完成后隐藏
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }
        
        // 验证码成功后继续发送邮箱验证码
        function continueSendEmailCode() {
            // 获取邮箱输入
            const email = document.getElementById('email').value.trim();
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            
            // 显示处理中状态
            sendCodeBtn.disabled = true;
            sendCodeBtn.textContent = '发送中...';
            
            // 禁用按钮并开始倒计时
            let countdown = 60;
            
            // 先检查邮箱是否被拉黑
            callAPI('check-email-blacklist', { email: email })
                .then(blacklistResult => {
                    if (blacklistResult.blacklisted) {
                        // 邮箱被拉黑，显示拉黑提示
                        sendCodeBtn.disabled = false;
                        sendCodeBtn.textContent = '发送验证码';
                        BlacklistHandler.showBlacklistedMessage(blacklistResult.message || BlacklistHandler.DEFAULT_BLACKLIST_MESSAGE);
                    } else {
                        // 邮箱未被拉黑，继续发送验证码
                        // 调用后端API发送验证码
                        callAPI('send-verification-code', { email: email })
                            .then(result => {
                                console.log('发送验证码结果:', result);
                                
                                if (result.success) {
                                    console.log('验证码发送成功');
                                    showToast('验证码已发送至您的邮箱，请查收', 'success', '发送成功');
                                    
                                    // 开始倒计时
                                    if (window.resetCodeTimer) {
                                        clearInterval(window.resetCodeTimer);
                                    }
                                    
                                    sendCodeBtn.textContent = `${countdown}秒后重试`;
                                    window.resetCodeTimer = setInterval(() => {
                                        countdown--;
                                        if (countdown <= 0) {
                                            clearInterval(window.resetCodeTimer);
                                            sendCodeBtn.disabled = false;
                                            sendCodeBtn.textContent = '发送验证码';
                                        } else {
                                            sendCodeBtn.textContent = `${countdown}秒后重试`;
                                        }
                                    }, 1000);
                                } else {
                                    console.log('验证码发送失败:', result.message);
                                    showToast(result.message || '验证码发送失败，请稍后重试', 'error', '发送失败');
                                    sendCodeBtn.disabled = false;
                                    sendCodeBtn.textContent = '发送验证码';
                                }
                            })
                            .catch(error => {
                                console.error('发送验证码失败:', error);
                                showToast('发送验证码失败，请稍后重试', 'error', '系统错误');
                                sendCodeBtn.disabled = false;
                                sendCodeBtn.textContent = '发送验证码';
                            });
                    }
                })
                .catch(error => {
                    console.error('检查邮箱黑名单出错:', error);
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.textContent = '发送验证码';
                    showToast('检查邮箱状态出错，请稍后重试', 'error', '系统错误');
                });
        }

        // 验证码验证
        document.getElementById('verifyForm').addEventListener('submit', function(event) {
            event.preventDefault();
            
            const email = document.getElementById('email').value;
            const verificationCode = document.getElementById('verificationCode').value;
            
            // 显示加载状态
            const verifyBtn = document.getElementById('verifyBtn');
            const originalBtnText = verifyBtn.textContent;
            verifyBtn.disabled = true;
            verifyBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 验证中...';
            
            // 发送验证请求
            fetch('/api/verify-reset-code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email, verificationCode })
            })
            .then(response => response.json())
            .then(data => {
                verifyBtn.disabled = false;
                verifyBtn.textContent = originalBtnText;
                
                if (data.success) {
                    // 验证成功，显示密码重置表单
                    document.getElementById('verifyForm').style.display = 'none';
                    document.getElementById('resetForm').style.display = 'block';
                } else {
                    // 验证失败
                    // 检查是否是因为账号被拉黑
                    if (data.blacklisted) {
                        // 显示被拉黑的提示信息
                        BlacklistHandler.showBlacklistedMessage(data.message || BlacklistHandler.DEFAULT_BLACKLIST_MESSAGE);
                    } else {
                        // 显示普通错误信息
                        document.getElementById('verifyErrorMessage').textContent = data.message;
                        document.getElementById('verifyErrorContainer').style.display = 'block';
                    }
                }
            })
            .catch(error => {
                console.error('验证请求失败:', error);
                verifyBtn.disabled = false;
                verifyBtn.textContent = originalBtnText;
                document.getElementById('verifyErrorMessage').textContent = '网络错误，请稍后重试';
                document.getElementById('verifyErrorContainer').style.display = 'block';
            });
        });

        // 显示账号被拉黑的提示信息
        function showBlacklistedMessage(message) {
            // 创建模态框元素
            const modal = document.createElement('div');
            modal.className = 'blacklist-modal';
            
            modal.innerHTML = `
                <div class="blacklist-modal-content">
                    <div class="blacklist-modal-header">
                        <h3><i class="fas fa-exclamation-triangle"></i> 账号已被限制</h3>
                    </div>
                    <div class="blacklist-modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="blacklist-modal-footer">
                        <button id="blacklistOkBtn">我知道了</button>
                    </div>
                </div>
            `;
            
            // 添加到页面
            document.body.appendChild(modal);
            
            // 添加关闭按钮事件
            document.getElementById('blacklistOkBtn').addEventListener('click', function() {
                modal.remove();
                window.location.href = 'login.html'; // 返回登录页
            });
            
            // 添加样式
            const style = document.createElement('style');
            style.textContent = `
                .blacklist-modal {
                    position: fixed;
                    z-index: 1000;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0,0,0,0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .blacklist-modal-content {
                    background-color: white;
                    border-radius: 8px;
                    width: 90%;
                    max-width: 450px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
                }
                .blacklist-modal-header {
                    padding: 15px 20px;
                    border-bottom: 1px solid #eee;
                    color: #dc3545;
                }
                .blacklist-modal-header h3 {
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }
                .blacklist-modal-body {
                    padding: 20px;
                    line-height: 1.5;
                }
                .blacklist-modal-footer {
                    padding: 15px 20px;
                    display: flex;
                    justify-content: flex-end;
                    border-top: 1px solid #eee;
                }
                #blacklistOkBtn {
                    background-color: #0c4da2;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                }
                #blacklistOkBtn:hover {
                    background-color: #0a3f87;
                }
            `;
            document.head.appendChild(style);
        }
    </script>
</body>
</html> 