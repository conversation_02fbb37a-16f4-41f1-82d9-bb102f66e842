<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美国运输价格 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .page-title {
            color: #0c4da2;
            font-size: 32px;
            margin-bottom: 0;
        }
        
        .back-button {
            display: inline-block;
            color: #0c4da2;
            text-decoration: none;
            font-size: 16px;
            background-color: #fff;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background-color: #f5f5f5;
        }
        
        .content-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 100%;
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            color: #666;
            font-size: 14px;
        }

        /* 标签页样式 */
        .tab-container {
            width: 100%;
            margin-bottom: 30px;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }

        .tab-button {
            padding: 12px 24px;
            background-color: #f0f0f0;
            border: none;
            border-radius: 5px 5px 0 0;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin-right: 5px;
            transition: all 0.3s ease;
        }

        .tab-button:hover {
            background-color: #e0e0e0;
        }

        .tab-button.active {
            background-color: #0c4da2;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .page-heading {
            text-align: center;
            color: #0c4da2;
            margin-bottom: 30px;
            font-size: 28px;
        }
        
        /* 添加黑色边框样式 */
        table th, table td {
            border: 1px solid #000000 !important;
        }
        
        /* 表格标题行样式 */
        table thead tr {
            background-color: #dce6f2 !important;
        }
        
        /* 表格内容行样式 */
        table tbody tr {
            background-color: #c5d9f1 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-container">
                <img src="../img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="page-title">美国运输价格</h1>
            </div>
            <a href="../prices.html" class="back-button">返回价格页</a>
        </div>
        
        <div class="content-container">
            <!-- 顶部主标签页 -->
            <div class="tab-container main-tab-container">
                <div class="tabs" style="display: flex; margin-bottom: 20px;">
                    <button class="tab-button main-tab-button active" style="flex: 1; background-color: #0c4da2; color: white; font-size: 18px; padding: 15px;" onclick="openMainTab(event, 'sea-shipping')">美国海运价格表</button>
                    <button class="tab-button main-tab-button" style="flex: 1; background-color: #f0f0f0; font-size: 18px; padding: 15px;" onclick="openMainTab(event, 'air-shipping')">美国空运价格表</button>
                </div>

                <!-- 海运价格表内容 -->
                <div id="sea-shipping" class="main-tab-content" style="display: block;">
                    <!-- 区域标签页导航 -->
                    <div class="tab-container region-tab-container">
                        <div class="tabs">
                            <button class="tab-button region-tab-button active" onclick="openRegionTab(event, 'sea-west')">美国西部</button>
                            <button class="tab-button region-tab-button" onclick="openRegionTab(event, 'sea-central')">美国中部</button>
                            <button class="tab-button region-tab-button" onclick="openRegionTab(event, 'sea-east')">美国东部</button>
                        </div>

                        <!-- 美国西部价格表 -->
                        <div id="sea-west" class="tab-content region-tab-content" style="display: block;">
                            <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">美国西部海运超大件普货</h3>
                            <p style="background-color: #e9ecef; padding: 8px; margin-bottom: 10px; font-size: 14px; text-align: center;">专接【家具、机器、舞台灯、按摩器械、航空箱、户外LCD屏幕、木箱1木架1卡板】。商业地址、私人地址、无私人地址附加费。</p>
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                                <thead>
                                    <tr style="background-color: #d9e2f3;">
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" colspan="2">国家/重量</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">130KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">150KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">300KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">500KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1000KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">2000KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">3000KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">提取时效</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" colspan="2">美西90-92邮编特价</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">15</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">14</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">13</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">12.5</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">12</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">六航四开！<br>开船后16-18个工作<br>日提取（码头到场，<br>海关查验及不可抗因<br>素除外）。<br>尾程由卡车派送！</td>
                                    </tr>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" colspan="2">美国西部<br>(邮编8、9开头)</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">**</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.5</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16.5</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">15</td>
                                    </tr>
                                </tbody>
                            </table>
                            
                            <!-- 普通海运价格表 -->
                            <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">美西海运小件（普货/敏感货）包清关 包税费 派送到门</h3>
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                                <thead>
                                    <tr style="background-color: #d9e2f3;">
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">货物类型</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">12-50kg</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">50kg以上</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">100kg以上</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">普货（美西8-9邮编）</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">26/kg</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">23/kg</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">20/kg</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">开船后15-18天到港</td>
                                    </tr>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">敏货（美西8-9邮编）</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">31.5/kg</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">28.5/kg</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">25/kg</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 美国中部价格表 -->
                        <div id="sea-central" class="tab-content region-tab-content" style="display: none;">
                            <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">美国中部海运超大件普货</h3>
                            <p style="background-color: #e9ecef; padding: 8px; margin-bottom: 10px; font-size: 14px; text-align: center;">专接【家具、机器、舞台灯、按摩器械、航空箱、户外LCD屏幕、木箱1木架1卡板】。商业地址、私人地址、无私人地址附加费。</p>
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                                <thead>
                                    <tr style="background-color: #d9e2f3;">
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" colspan="2">国家/重量</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">130KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">150KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">300KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">500KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1000KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">2000KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">3000KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">提取时效</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" colspan="2">美国中部<br>(邮编5、6、7开头)<br>80-82 96-99开头邮编</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">**</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">22.5</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20.5</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.5</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.5</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">六航四开！<br>开船后16-18个工作<br>日提取（码头到场，<br>海关查验及不可抗因<br>素除外）。<br>尾程由卡车派送！</td>
                                    </tr>
                                </tbody>
                            </table>
                            
                            <!-- 普通海运价格表 - 中部 -->
                            <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">美中海运小件（普货/敏感货）包清关 包税费 派送到门</h3>
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                                <thead>
                                    <tr style="background-color: #d9e2f3;">
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">货物类型</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">12-50kg</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">50kg以上</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">100kg以上</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">普货（美中4-7邮编）</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">27.5/kg</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">25.5/kg</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">21.5/kg</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">开船后15-18天到港</td>
                                    </tr>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">敏货（美中4-7邮编）</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">32.5/kg</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">29/kg</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">26.5/kg</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 美国东部价格表 -->
                        <div id="sea-east" class="tab-content region-tab-content" style="display: none;">
                            <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">美国东部海运超大件普货</h3>
                            <p style="background-color: #e9ecef; padding: 8px; margin-bottom: 10px; font-size: 14px; text-align: center;">专接【家具、机器、舞台灯、按摩器械、航空箱、户外LCD屏幕、木箱1木架1卡板】。商业地址、私人地址、无私人地址附加费。</p>
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                                <thead>
                                    <tr style="background-color: #d9e2f3;">
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" colspan="2">国家/重量</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">130KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">150KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">300KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">500KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1000KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">2000KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">3000KG+</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">提取时效</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" colspan="2">美国东部<br>(邮编0、1、2、3、4开<br>头) 邮编为11开头</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">**</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">23.5</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">21.5</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20.5</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.5</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">六航四开！<br>开船后16-18个工作<br>日提取（码头到场，<br>海关查验及不可抗因<br>素除外）。<br>尾程由卡车派送！</td>
                                    </tr>
                                </tbody>
                            </table>
                            
                            <!-- 普通海运价格表 - 东部 -->
                            <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">美东海运小件（普货/敏感货）包清关 包税费 派送到门</h3>
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                                <thead>
                                    <tr style="background-color: #d9e2f3;">
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">货物类型</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">12-50kg</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">50kg以上</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">100kg以上</th>
                                        <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">普货（美东0-3邮编）</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">29.5/kg</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">26.5/kg</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">22.5/kg</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">开船后15-18天到港</td>
                                    </tr>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">敏货（美东0-3邮编）</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">33.5/kg</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">29.5/kg</td>
                                        <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center; color: #ff0000;">27.5/kg</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 空运价格表内容 -->
                <div id="air-shipping" class="main-tab-content" style="display: none;">
                    <!-- 区域标签页导航 -->
                    <div class="tab-container region-tab-container">
                        <div class="tabs">
                            <button class="tab-button region-tab-button active" onclick="openRegionTab(event, 'air-west')">美国西部</button>
                            <button class="tab-button region-tab-button" onclick="openRegionTab(event, 'air-central')">美国中部</button>
                            <button class="tab-button region-tab-button" onclick="openRegionTab(event, 'air-east')">美国东部</button>
                        </div>

                        <!-- 美国西部空运价格表 -->
                        <div id="air-west" class="tab-content region-tab-content" style="display: block;">
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                                <thead>
                                    <tr>
                                        <th style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; border: 1px solid #000000;" colspan="6">美西空运（普货/敏感货）包清关 包税费 派送到门</th>
                                    </tr>
                                    <tr style="background-color: #d9e2f3;">
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">货物类型</th>
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;" colspan="2">0.5-5kg</th>
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">0-21kg以上</th>
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">50-100kg以上</th>
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">时效</th>
                                    </tr>
                                    <tr style="background-color: #d9e2f3;">
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;">首重</th>
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;">续重</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center;">普货（美西8-9邮编）</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">180/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">50/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">55/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">50/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="6">8-10天提取</td>
                                    </tr>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center;">敏货（美西8-9邮编）</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">180/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">50/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">65/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">60/kg</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 美国中部空运价格表 -->
                        <div id="air-central" class="tab-content region-tab-content" style="display: none;">
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                                <thead>
                                    <tr>
                                        <th style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; border: 1px solid #000000;" colspan="6">美中空运（普货/敏感货）包清关 包税费 派送到门</th>
                                    </tr>
                                    <tr style="background-color: #d9e2f3;">
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">货物类型</th>
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;" colspan="2">0.5-5kg</th>
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">0-21kg以上</th>
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">50-100kg以上</th>
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">时效</th>
                                    </tr>
                                    <tr style="background-color: #d9e2f3;">
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;">首重</th>
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;">续重</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center;">普货（美中4-7邮编）</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">180/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">50/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">56/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">52/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="6">8-10天提取</td>
                                    </tr>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center;">敏货（美中4-7邮编）</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">180/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">50/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">66/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">62/kg</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 美国东部空运价格表 -->
                        <div id="air-east" class="tab-content region-tab-content" style="display: none;">
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                                <thead>
                                    <tr>
                                        <th style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; border: 1px solid #000000;" colspan="6">美东空运（普货/敏感货）包清关 包税费 派送到门</th>
                                    </tr>
                                    <tr style="background-color: #d9e2f3;">
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">货物类型</th>
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;" colspan="2">0.5-5kg</th>
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">0-21kg以上</th>
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">50-100kg以上</th>
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">时效</th>
                                    </tr>
                                    <tr style="background-color: #d9e2f3;">
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;">首重</th>
                                        <th style="border: 1px solid #000000; padding: 8px; text-align: center;">续重</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center;">普货（美东0-3邮编）</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">180/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">50/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">57/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">53/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="6">8-10天提取</td>
                                    </tr>
                                    <tr style="background-color: #b8cce4;">
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center;">敏货（美东0-3邮编）</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">180/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">50/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">67/kg</td>
                                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; color: #ff0000;">63/kg</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <footer>
            <p>&copy; 2023 金舟国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </footer>
    </div>

    <script>
        function openMainTab(evt, tabName) {
            var i, tabcontent, tabbuttons;
            
            tabcontent = document.getElementsByClassName("main-tab-content");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            
            tabbuttons = document.getElementsByClassName("main-tab-button");
            for (i = 0; i < tabbuttons.length; i++) {
                tabbuttons[i].className = tabbuttons[i].className.replace(" active", "");
                tabbuttons[i].style.backgroundColor = "#f0f0f0";
                tabbuttons[i].style.color = "#000";
            }
            
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
            evt.currentTarget.style.backgroundColor = "#0c4da2";
            evt.currentTarget.style.color = "white";
        }
        
        function openRegionTab(evt, tabName) {
            // Find the parent tab container
            var parentContainer = evt.currentTarget.closest('.region-tab-container');
            
            // Get all tab content elements within this container
            var tabcontent = parentContainer.getElementsByClassName("region-tab-content");
            for (var i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            
            // Get all tab buttons within this container
            var tabbuttons = parentContainer.getElementsByClassName("region-tab-button");
            for (var i = 0; i < tabbuttons.length; i++) {
                tabbuttons[i].className = tabbuttons[i].className.replace(" active", "");
            }
            
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }
    </script>
</body>
</html> 