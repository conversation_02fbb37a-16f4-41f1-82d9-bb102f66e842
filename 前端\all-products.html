<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全部商品 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="js/blacklist-handler.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header Styles */
        header {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .logo {
            display: flex;
            align-items: center;
        }

        .logo-img {
            width: 45px;
            height: 45px;
            margin-right: 10px;
            border-radius: 8px;
        }

        .logo-text h1 {
            font-size: 22px;
            margin-bottom: 2px;
        }

        .logo-text p {
            font-size: 12px;
            color: #666;
        }

        .gold {
            color: #d4af37;
        }

        /* Welcome Message */
        .welcome-message {
            background-color: rgba(255, 255, 255, 0.8);
            padding: 6px 15px;
            border-radius: 15px;
            font-size: 14px;
            color: #0c4da2;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            position: absolute;
            left: 300px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
        }

        .welcome-message i {
            margin-right: 5px;
            color: #d4af37;
        }

        .not-logged-in {
            color: #888;
        }

        /* 用户下拉菜单 */
        .user-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            padding: 10px 0;
            margin-top: 8px;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
        }

        .user-dropdown.active {
            opacity: 1;
            visibility: visible;
        }

        .user-dropdown:before {
            content: '';
            position: absolute;
            top: -10px;
            left: 20px;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid white;
        }

        .user-dropdown-item {
            padding: 8px 15px;
            display: block;
            color: #333;
            font-size: 13px;
            text-decoration: none;
            transition: background-color 0.3s;
            text-align: left;
        }

        .user-dropdown-item:hover {
            background-color: #f5f5f5;
        }

        .user-dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 5px 0;
        }

        .user-dropdown-item i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }

        .user-dropdown-item.logout {
            color: #e74c3c;
        }

        /* Header Layout */
        header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            position: relative;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .header-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: #333;
        }

        .header-title h1 {
            font-size: 22px;
            margin-bottom: 2px;
            color: #0c4da2;
        }

        .header-title h1 i {
            color: #ffd700;
            margin-right: 8px;
        }

        .header-title p {
            font-size: 12px;
            color: #666;
            margin: 0;
        }

        .back-button {
            background: linear-gradient(135deg, #0c4da2 0%, #1e5bb8 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(12, 77, 162, 0.3);
            font-size: 14px;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(12, 77, 162, 0.4);
        }

        .back-button i {
            margin-right: 8px;
        }

        /* 发布商品按钮样式 */
        .publish-product-button {
            background: linear-gradient(135deg, #52c41a 0%, #1da57a 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(82, 196, 26, 0.3);
            font-size: 14px;
            position: absolute;
            left: calc(50% + 220px);
            top: 50%;
            transform: translateY(-50%);
        }

        .publish-product-button:hover {
            transform: translateY(calc(-50% - 2px));
            box-shadow: 0 6px 12px rgba(82, 196, 26, 0.4);
        }

        .publish-product-button i {
            margin-right: 8px;
        }

        /* Main Content */
        main {
            padding: 30px 0;
            min-height: calc(100vh - 150px);
        }

        .products-container {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        /* 商品列表样式 */
        .products-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
        }
        
        .products-header h2 {
            font-size: 22px;
            color: #0c4da2;
        }
        
        .products-actions {
            display: flex;
            gap: 10px;
        }
        
        .action-button {
            padding: 8px 15px;
            border-radius: 5px;
            border: none;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: all 0.3s;
        }
        
        .action-button i {
            margin-right: 5px;
        }
        
        .btn-add {
            background-color: #52c41a;
            color: white;
        }
        
        .btn-add:hover {
            background-color: #389e0d;
        }
        
        .btn-filter {
            background-color: #f0f0f0;
            color: #666;
        }
        
        .btn-filter:hover {
            background-color: #e0e0e0;
        }
        
        .empty-products {
            text-align: center;
            padding: 50px 0;
        }
        
        .empty-products i {
            font-size: 48px;
            color: #d9d9d9;
            margin-bottom: 20px;
        }
        
        .empty-products h3 {
            font-size: 18px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .empty-products p {
            color: #999;
            margin-bottom: 20px;
        }

        /* Footer */
        footer {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 50px;
        }

        footer p {
            font-size: 14px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-left">
                <div class="logo">
                    <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                    <div class="logo-text">
                        <h1><span class="gold">金舟</span>国际物流</h1>
                        <p>Jin Zhou International Logistics</p>
                    </div>
                </div>
                
                <div class="welcome-message" id="welcomeMessage">
                    <i class="fas fa-user-circle"></i>
                    <span id="loginStatusText">正在加载...</span>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" class="user-dropdown-item account-info">
                            <i class="fas fa-user"></i><span id="accountName">我的账号</span>
                        </a>
                        <div class="user-dropdown-divider"></div>
                        <a href="#" class="user-dropdown-item logout" id="logoutButton">
                            <i class="fas fa-sign-out-alt"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>

            <div class="header-title">
                <h1><i class="fas fa-list"></i>全部商品</h1>
                <p>管理您发布的商品</p>
            </div>

            <a href="publish-product.html" class="publish-product-button">
                <i class="fas fa-plus-circle"></i>发布商品
            </a>

            <div class="header-right">
                <a href="dashboard.html" class="back-button">
                    <i class="fas fa-arrow-left"></i>返回控制台
                </a>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="products-container">
                <div class="products-header">
                    <h2>我的商品列表</h2>
                    <div class="products-actions">
                        <!-- 筛选和添加商品按钮已移除 -->
                    </div>
                </div>
                
                <!-- 商品列表内容 -->
                <div class="empty-products" id="emptyProductsMessage">
                    <i class="fas fa-box-open"></i>
                    <h3>暂无商品</h3>
                    <p>您还没有发布任何商品，点击"添加商品"按钮开始发布吧！</p>
                </div>
                
                <!-- 商品表格将在这里动态加载 -->
                <div id="productsTable" style="display: none;">
                    <!-- 商品表格将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container" style="max-width: 800px;">
            <p>&copy; 2023 <span class="gold">金舟</span>国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </div>
    </footer>

    <script>
        // 检查用户登录状态
        document.addEventListener('DOMContentLoaded', function() {
            const welcomeMessage = document.getElementById('welcomeMessage');
            const loginStatusText = document.getElementById('loginStatusText');
            const userDropdown = document.getElementById('userDropdown');
            const accountName = document.getElementById('accountName');
            const logoutButton = document.getElementById('logoutButton');
            
            let dropdownTimeout;
            
            // 添加鼠标事件来控制下拉菜单的显示和隐藏
            if (welcomeMessage && userDropdown) {
                // 鼠标进入欢迎信息区域显示下拉菜单
                welcomeMessage.addEventListener('mouseenter', function() {
                    clearTimeout(dropdownTimeout);
                    userDropdown.classList.add('active');
                });
                
                // 鼠标离开欢迎信息区域后隐藏下拉菜单
                welcomeMessage.addEventListener('mouseleave', function() {
                    clearTimeout(dropdownTimeout);
                    dropdownTimeout = setTimeout(() => {
                        userDropdown.classList.remove('active');
                    }, 10);
                });
                
                // 鼠标进入下拉菜单时清除隐藏计时器
                userDropdown.addEventListener('mouseenter', function() {
                    clearTimeout(dropdownTimeout);
                });
                
                // 鼠标离开下拉菜单后隐藏
                userDropdown.addEventListener('mouseleave', function() {
                    clearTimeout(dropdownTimeout);
                    dropdownTimeout = setTimeout(() => {
                        userDropdown.classList.remove('active');
                    }, 10);
                });
            }
            
            // 安全地获取登录信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to parse user data from sessionStorage:', error);
                sessionStorage.removeItem('loggedInUser');
            }

            // 更新欢迎信息
            if (loggedInUser && loggedInUser.isLoggedIn && loggedInUser.username) {
                // 检查用户是否被拉黑
                BlacklistHandler.checkUserBlacklisted();
                
                // 定期检查用户状态
                setInterval(BlacklistHandler.checkUserBlacklisted, 30000);
                
                // 用户已登录
                loginStatusText.textContent = '欢迎，' + loggedInUser.username;
                welcomeMessage.classList.remove('not-logged-in');
                
                // 更新下拉菜单中的用户名
                accountName.textContent = "我的账户";
                
                // 为"我的账户"添加点击事件，跳转到用户中心
                document.querySelector('.account-info').addEventListener('click', function(e) {
                    e.preventDefault();
                    window.location.href = 'dashboard.html'; 
                });
                
                // 处理退出登录
                logoutButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    sessionStorage.removeItem('loggedInUser');
                    window.location.reload();
                });
                
                // 检查商家认证状态
                checkMerchantAuthStatus(loggedInUser.username);
            } else {
                // 用户未登录，重定向到登录页面
                window.location.href = 'login.html?returnUrl=' + encodeURIComponent(window.location.href);
            }
        });
        
        // 检查商家认证状态
        function checkMerchantAuthStatus(username) {
            fetch(`/api/get-merchant-auth?username=${encodeURIComponent(username)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 如果商家认证未通过，则重定向到发布商品页面
                        if (!data.data || data.data.status !== 'approved') {
                            window.location.href = 'publish-product.html';
                        } else {
                            // 认证已通过，加载商品列表
                            loadProducts(username);
                        }
                    } else {
                        console.warn('获取认证状态失败:', data.message);
                        // 获取状态失败，重定向到发布商品页面
                        window.location.href = 'publish-product.html';
                    }
                })
                .catch(error => {
                    console.error('获取认证状态出错:', error);
                    // 出错时也重定向到发布商品页面
                    window.location.href = 'publish-product.html';
                });
        }
        
        // 加载商品列表
        function loadProducts(username) {
            // 这里应该有一个API调用来获取该用户的商品列表
            // 由于目前没有实际的商品数据，我们保持空状态显示
            
            // 模拟API调用
            setTimeout(() => {
                // 如果有商品数据，则显示商品表格
                // 这里我们保持空状态，将来可以添加真实商品数据的逻辑
                document.getElementById('emptyProductsMessage').style.display = 'block';
                document.getElementById('productsTable').style.display = 'none';
                
                // 删除页面底部可能存在的绿色添加商品按钮
                const bottomAddButtons = document.querySelectorAll('a[href="publish-product.html"]');
                bottomAddButtons.forEach(btn => {
                    // 检查是否是页面底部的大型绿色按钮
                    if (btn.offsetTop > window.innerHeight / 2 && 
                        (btn.style.backgroundColor === '#52c41a' || 
                         btn.style.background && btn.style.background.includes('#52c41a'))) {
                        btn.remove();
                    }
                });
            }, 500);
        }
    </script>
</body>
</html> 