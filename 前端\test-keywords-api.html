<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试关键词API</title>
</head>
<body>
    <h1>测试关键词API</h1>
    
    <div>
        <h2>添加关键词</h2>
        <input type="text" id="keywordInput" placeholder="输入关键词">
        <button onclick="addKeyword()">添加</button>
    </div>
    
    <div>
        <h2>获取关键词列表</h2>
        <button onclick="getKeywords()">获取列表</button>
        <div id="result"></div>
    </div>
    
    <script>
        async function addKeyword() {
            const keyword = document.getElementById('keywordInput').value.trim();
            if (!keyword) {
                alert('请输入关键词');
                return;
            }
            
            try {
                const response = await fetch('/api/keywords', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ keyword })
                });
                
                const result = await response.json();
                console.log('添加结果:', result);
                
                if (result.success) {
                    alert('添加成功');
                    document.getElementById('keywordInput').value = '';
                    getKeywords(); // 重新获取列表
                } else {
                    alert('添加失败: ' + result.message);
                }
            } catch (error) {
                console.error('添加失败:', error);
                alert('添加失败: ' + error.message);
            }
        }
        
        async function getKeywords() {
            try {
                const response = await fetch('/api/keywords');
                const result = await response.json();
                console.log('获取结果:', result);
                
                const resultDiv = document.getElementById('result');
                
                if (result.success) {
                    const keywords = result.keywords || [];
                    resultDiv.innerHTML = '<h3>关键词列表:</h3>' + 
                        '<pre>' + JSON.stringify(keywords, null, 2) + '</pre>';
                } else {
                    resultDiv.innerHTML = '<p style="color: red;">获取失败: ' + result.message + '</p>';
                }
            } catch (error) {
                console.error('获取失败:', error);
                document.getElementById('result').innerHTML = '<p style="color: red;">获取失败: ' + error.message + '</p>';
            }
        }
        
        // 页面加载时自动获取关键词列表
        window.onload = function() {
            getKeywords();
        };
    </script>
</body>
</html>
