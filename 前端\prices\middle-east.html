<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中东运输价格 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .page-title {
            color: #0c4da2;
            font-size: 32px;
            margin-bottom: 0;
        }
        
        .back-button {
            display: inline-block;
            color: #0c4da2;
            text-decoration: none;
            font-size: 16px;
            background-color: #fff;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background-color: #f5f5f5;
        }
        
        .content-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 100%;
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            color: #666;
            font-size: 14px;
        }

        /* 标签页样式 */
        .tab-container {
            width: 100%;
            margin-bottom: 30px;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }

        .tab-button {
            padding: 12px 24px;
            background-color: #f0f0f0;
            border: none;
            border-radius: 5px 5px 0 0;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin-right: 5px;
            transition: all 0.3s ease;
        }

        .tab-button:hover {
            background-color: #e0e0e0;
        }

        .tab-button.active {
            background-color: #0c4da2;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .page-heading {
            text-align: center;
            color: #0c4da2;
            margin-bottom: 30px;
            font-size: 28px;
        }
        
        /* 添加黑色边框样式 */
        table th, table td {
            border: 1px solid #000000 !important;
        }
        
        /* 表格标题行样式 */
        table thead tr {
            background-color: #dce6f2 !important;
        }
        
        /* 表格内容行样式 */
        table tbody tr {
            background-color: #c5d9f1 !important;
        }
        
        .price-red {
            color: red;
        }
        
        /* 价格表区域样式 */
        .price-tables {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .price-table {
            width: 48%;
        }
        
        .sea-freight-title {
            background-color: #0c4da2;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .air-freight-title {
            background-color: #f8f9fa;
            padding: 15px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
            cursor: pointer;
        }
        
        /* 添加激活和非激活状态样式 */
        .title-active {
            background-color: #0c4da2;
            color: white;
        }
        
        .title-inactive {
            background-color: #f8f9fa;
            color: #333;
            border: 1px solid #e0e0e0;
        }
        
        /* 空运价格表区域样式 */
        .air-freight-content {
            display: none;
            margin-top: 30px;
            margin-bottom: 30px;
        }
        
        .air-freight-header {
            background-color: #ffbb00; 
            color: #000; 
            padding: 10px; 
            text-align: center; 
            font-weight: bold; 
            margin-bottom: 5px;
        }
        
        .air-freight-desc {
            background-color: #e9ecef; 
            padding: 8px; 
            margin-bottom: 10px; 
            font-size: 14px; 
            text-align: center;
        }
        
        .air-price-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .air-price-table th, .air-price-table td {
            border: 1px solid #b4c6e7;
            padding: 8px;
            text-align: center;
        }
        
        .air-price-table thead tr {
            background-color: #d9e2f3;
        }
        
        .air-price-table tbody tr {
            background-color: #b8cce4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-container">
                <img src="../img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="page-title">中东运输价格</h1>
            </div>
            <a href="../prices.html" class="back-button">返回价格页</a>
        </div>
        
        <div class="content-container">
            <!-- 价格表区域 -->
            <div class="price-tables">
                <!-- 海运价格表 -->
                <div class="price-table">
                    <div class="sea-freight-title" onclick="toggleSeaFreight()">中东海运价格表</div>
                </div>
                
                <!-- 空运价格表 -->
                <div class="price-table">
                    <div class="air-freight-title" onclick="toggleAirFreight()">中东空运价格表</div>
                </div>
            </div>
            
            <!-- 空运价格内容区域 -->
            <div id="airFreightContent" class="air-freight-content">
                <!-- 添加空运国家标签栏 -->
                <div class="tabs">
                    <button class="tab-button active" onclick="openAirTab(event, 'air-uae')">阿联酋</button>
                    <button class="tab-button" onclick="openAirTab(event, 'air-saudi')">沙特</button>
                    <button class="tab-button" onclick="openAirTab(event, 'air-pakistan')">巴基斯坦</button>
                    <button class="tab-button" onclick="openAirTab(event, 'air-iran')">伊朗</button>
                    <button class="tab-button" onclick="openAirTab(event, 'air-qatar')">卡塔尔</button>
                </div>
                
                <!-- 阿联酋空运价格表 -->
                <div id="air-uae" class="tab-content active">
                    <h3 class="air-freight-header">阿联酋空运小件（迪拜包税专线）</h3>
                    <p class="air-freight-desc">以下价格RMB报价，已含燃油，含进口国关税/VAT/派送（不提供税单）！！</p>
                    <table class="air-price-table">
                        <thead>
                            <tr>
                                <th rowspan="2">产品类别</th>
                                <th rowspan="2">区域</th>
                                <th colspan="2">包装</th>
                                <th colspan="4">大货优惠价 (KG)</th>
                                <th rowspan="2">参考时效<br>(工作日)</th>
                                <th rowspan="2">注意事项</th>
                            </tr>
                            <tr>
                                <th>首0.5</th>
                                <th>续0.5</th>
                                <th>16-99</th>
                                <th>100-299</th>
                                <th>300-499</th>
                                <th>500以上</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td rowspan="2">香港背货渠道</td>
                                <td>迪拜/沙迦</td>
                                <td class="price-red">66</td>
                                <td class="price-red">32</td>
                                <td class="price-red">43</td>
                                <td class="price-red">42</td>
                                <td class="price-red">41</td>
                                <td class="price-red">41</td>
                                <td>6—7</td>
                                <td rowspan="2">单件限制300CM*135CM*160CM 除6000分半液（气度超过135CM或高度超过160CM请里询后再发货）</td>
                            </tr>
                            <tr>
                                <td>其他区</td>
                                <td class="price-red">68</td>
                                <td class="price-red">34</td>
                                <td class="price-red">45</td>
                                <td class="price-red">44</td>
                                <td class="price-red">43</td>
                                <td class="price-red">43</td>
                                <td>7</td>
                            </tr>
                            <tr>
                                <td rowspan="2">移动电源/电池/超功率电池产品</td>
                                <td>迪拜/沙迦</td>
                                <td class="price-red">73</td>
                                <td class="price-red">38</td>
                                <td class="price-red">53</td>
                                <td class="price-red">52</td>
                                <td class="price-red">51</td>
                                <td class="price-red">51</td>
                                <td>7—8</td>
                                <td rowspan="2">纯电产品需要里个产品绝缘包装，所有内电产品≦100wh（瓦时）为超功率产品，提供MSDS，单件限制300CM*135CM*160CM 除6000分半液（气度超过135CM或高度超过160CM请里询后再发货）</td>
                            </tr>
                            <tr>
                                <td>其他区</td>
                                <td class="price-red">75</td>
                                <td class="price-red">40</td>
                                <td class="price-red">55</td>
                                <td class="price-red">54</td>
                                <td class="price-red">53</td>
                                <td class="price-red">53</td>
                                <td>8</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 沙特空运价格表 -->
                <div id="air-saudi" class="tab-content">
                    <h3 class="air-freight-header">沙特空运小件</h3>
                    <p class="air-freight-desc">以下价格为沙特全境派送</p>
                    <table class="air-price-table">
                        <thead>
                            <tr>
                                <th>渠道代码</th>
                                <th>产品名称（SA-1和SA-2要求单一品名）</th>
                                <th colspan="2">包装16KG以下</th>
                                <th>到仓时效（工作日）</th>
                            </tr>
                            <tr>
                                <th></th>
                                <th></th>
                                <th>首5KG</th>
                                <th>续0.5</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="price-red">SA-1</td>
                                <td class="price-red">普货无电</td>
                                <td class="price-red">320</td>
                                <td class="price-red">44</td>
                                <td class="price-red">8—12</td>
                            </tr>
                            <tr>
                                <td class="price-red">SA-2</td>
                                <td class="price-red">普货内置电池</td>
                                <td class="price-red">350</td>
                                <td class="price-red">48</td>
                                <td class="price-red">8—12</td>
                            </tr>
                        </tbody>
                    </table>
                    <div style="background-color: #e9ecef; padding: 8px; margin-top: 10px; font-size: 14px;">
                        本渠道沙特亚马逊货物16KG以内，入利雅得仓库收100RMB/票，入吉达仓库收280RMB/票
                    </div>
                    <div style="background-color: #e9ecef; padding: 8px; margin-top: 10px; font-size: 14px;">
                        不接受：超功率产品类（内置单个电池不超20瓦时，内置电池组不超100瓦时）/移动电源/纯电/化妆品/牌子（包含沙特当地注册牌子）/医疗器械，派送地址均为私人地址，如需送货亚马逊，参考大货价格加亚马逊入仓费用
                    </div>
                </div>
                
                <!-- 巴基斯坦空运价格表 -->
                <div id="air-pakistan" class="tab-content">
                    <h3 class="air-freight-header">巴基斯坦空运DDP(双清含税)</h3>
                    <p class="air-freight-desc"></p>
                    <table class="air-price-table">
                        <thead>
                            <tr>
                                <th>产品</th>
                                <th>渠道代码</th>
                                <th>首重0.5KG</th>
                                <th>续重0.5KG</th>
                                <th style="color: red;">11kg+</th>
                                <th>100kg+</th>
                                <th>300kg+</th>
                                <th>材积</th>
                                <th>时效</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>普货A</td>
                                <td>PK-P04</td>
                                <td>95</td>
                                <td>65</td>
                                <td class="price-red">96</td>
                                <td>94</td>
                                <td>92</td>
                                <td rowspan="4">/6000</td>
                                <td rowspan="4">10-15天</td>
                            </tr>
                            <tr>
                                <td>普货B</td>
                                <td>PK-M05</td>
                                <td>100</td>
                                <td>70</td>
                                <td class="price-red">110</td>
                                <td>108</td>
                                <td>106</td>
                            </tr>
                            <tr>
                                <td>敏感货(内电带磁)</td>
                                <td>PK-M06</td>
                                <td>120</td>
                                <td>75</td>
                                <td class="price-red">124</td>
                                <td>122</td>
                                <td>120</td>
                            </tr>
                            <tr>
                                <td>敏感货(高货值)</td>
                                <td>PK-G07</td>
                                <td colspan="5" style="text-align: center;">单询</td>
                            </tr>
                        </tbody>
                    </table>
                    <div style="background-color: #e9ecef; padding: 8px; margin-top: 10px; font-size: 14px;">
                        参考时效: 7-15天，双清包税，Islamabad和Karachi包派，其他地址加收派送费5/KG, 网点外地址派送费单询
                    </div>
                    <div style="background-color: #e9ecef; padding: 8px; margin-top: 10px; font-size: 14px;">
                        <span style="color: red;">注：单件限重30kg以下清关最快</span>
                    </div>
                    <div style="background-color: #e9ecef; padding: 8px; margin-top: 10px; font-size: 14px;">
                        <span style="color: red;">PK-04</span>: 纺织类、服装、鞋子，无牌包包，布料，花边，帽子，围巾，皮带，家纺数据线，转接头，键盘，普通耳机，有线鼠标，键盘，无电游戏手柄，手机壳，皮套，手机保护膜，手机支架，眼镜盒，手表盒，手表带，包装盒等普通产品等
                    </div>
                    <div style="background-color: #e9ecef; padding: 8px; margin-top: 10px; font-size: 14px;">
                        <span style="color: red;">PK-M05</span>: 无电厨具，普通家用灯具类; 无牌普通眼镜，充电器，适配器，袖扣，文具，无线鼠标; 泳装、写字板，玩具类，化妆刷，卫浴类，五金工具，开关，铁艺雕塑，全喷雾瓶等
                    </div>
                    <div style="background-color: #e9ecef; padding: 8px; margin-top: 10px; font-size: 14px;">
                        <span style="color: red;">PK-M06</span>: 饰品，电子元件，小家电，汽车器，节日用品（气球），模型，千子努，吸管，树脂摆件，无牌蓝牙产品，笔，摄像机、<span style="color: red;">无牌（智能）手表，化妆品，电路板</span> 测试仪等高价值电子产品单询
                    </div>
                    <div style="background-color: #e9ecef; padding: 8px; margin-top: 10px; font-size: 14px;">
                        <span style="color: red;">PK-G07</span>: 汽配，运动手环，打印机。
                    </div>
                </div>
                
                <!-- 伊朗空运价格表 -->
                <div id="air-iran" class="tab-content">
                    <h3 class="air-freight-header" style="background-color: #ffbb00; color: #000;">伊朗空运小件</h3>
                    <p class="air-freight-desc"></p>
                    <table class="air-price-table">
                        <thead>
                            <tr>
                                <th>渠道说明</th>
                                <th>货物类型</th>
                                <th style="color: red;">渠道代码</th>
                                <th>首重0.5KG</th>
                                <th>续重0.5kg</th>
                                <th>21KG+</th>
                                <th>51KG+</th>
                                <th>分泡</th>
                                <th>正常时效</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td rowspan="2">伊朗</td>
                                <td>普货</td>
                                <td class="price-red">IR-PA</td>
                                <td>130</td>
                                <td>60</td>
                                <td>73</td>
                                <td>70</td>
                                <td rowspan="2">无</td>
                                <td>4-6天</td>
                            </tr>
                            <tr>
                                <td>带电带磁</td>
                                <td class="price-red">IR-D/M</td>
                                <td>150</td>
                                <td>70</td>
                                <td>86</td>
                                <td>85</td>
                                <td>10-12天</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 卡塔尔空运价格表 -->
                <div id="air-qatar" class="tab-content">
                    <h3 class="air-freight-header">卡塔尔空运小件</h3>
                    <p class="air-freight-desc"></p>
                    <table class="air-price-table">
                        <thead>
                            <tr>
                                <th>渠道说明</th>
                                <th>货物类型</th>
                                <th style="color: red;">渠道代码</th>
                                <th>首重0.5KG</th>
                                <th>续重0.5kg</th>
                                <th>21KG+</th>
                                <th>51KG+</th>
                                <th>分泡</th>
                                <th>正常时效</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td rowspan="2">卡塔尔</td>
                                <td>普货</td>
                                <td></td>
                                <td>单询</td>
                                <td>单询</td>
                                <td>单询</td>
                                <td>单询</td>
                                <td rowspan="2">无</td>
                                <td rowspan="2"></td>
                            </tr>
                            <tr>
                                <td>带电带磁</td>
                                <td></td>
                                <td>单询</td>
                                <td>单询</td>
                                <td>单询</td>
                                <td>单询</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 标签页导航 -->
            <div class="tab-container" id="seaFreightContent">
                <div class="tabs">
                    <button class="tab-button active" onclick="openTab(event, 'uae')">阿联酋</button>
                    <button class="tab-button" onclick="openTab(event, 'saudi')">沙特</button>
                    <button class="tab-button" onclick="openTab(event, 'pakistan')">巴基斯坦</button>
                    <button class="tab-button" onclick="openTab(event, 'iran')">伊朗</button>
                    <button class="tab-button" onclick="openTab(event, 'qatar')">卡塔尔</button>
                </div>

                <!-- 阿联酋价格表 -->
                <div id="uae" class="tab-content active">
                    <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">阿联酋海运超大件</h3>
                    <p style="background-color: #e9ecef; padding: 8px; margin-bottom: 10px; font-size: 14px; text-align: center;">可接卡板货、木箱、航空箱、家私家具，建材等各种不规则包装的超大件.</p>
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                        <thead>
                            <tr style="background-color: #d9e2f3;">
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">国家/重量</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">类型</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">167KG-500kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">501KG-1000kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1001KG-1670kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1680KG以上</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="background-color: #b8cce4;">
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">阿联酋</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">普货</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">11</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">10.5</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">8.5</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">8</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">30-35天</td>
                            </tr>
                            <tr style="background-color: #b8cce4;">
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">敏货</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">13</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">13</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">12.5</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">12</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 沙特价格表 -->
                <div id="saudi" class="tab-content">
                    <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">沙特海运超大件</h3>
                    <p style="background-color: #e9ecef; padding: 8px; margin-bottom: 10px; font-size: 14px; text-align: center;">可接卡板货、木箱、航空箱、家私家具，建材等各种不规则包装的超大件.</p>
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                        <thead>
                            <tr style="background-color: #d9e2f3;">
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">国家/重量</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">类型</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">167KG-500kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">501KG-1000kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1001KG-1670kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1680KG以上</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="background-color: #b8cce4;">
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">沙特</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">普货</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">14</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">13</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">13</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">12</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">45天左右</td>
                            </tr>
                            <tr style="background-color: #b8cce4;">
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">敏货</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 巴基斯坦价格表 -->
                <div id="pakistan" class="tab-content">
                    <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">巴基斯坦海运超大件</h3>
                    <p style="background-color: #e9ecef; padding: 8px; margin-bottom: 10px; font-size: 14px; text-align: center;">可接卡板货、木箱、航空箱、家私家具，建材等各种不规则包装的超大件.</p>
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                        <thead>
                            <tr style="background-color: #d9e2f3;">
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">国家/重量</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">类型</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">167KG-500kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">501KG-1000kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1001KG-1670kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1680KG以上</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="background-color: #b8cce4;">
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">巴基斯坦</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">普货</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">20天左右</td>
                            </tr>
                            <tr style="background-color: #b8cce4;">
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">敏货</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 伊朗价格表 -->
                <div id="iran" class="tab-content">
                    <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">伊朗海运超大件</h3>
                    <p style="background-color: #e9ecef; padding: 8px; margin-bottom: 10px; font-size: 14px; text-align: center;">可接卡板货、木箱、航空箱、家私家具，建材等各种不规则包装的超大件.</p>
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                        <thead>
                            <tr style="background-color: #d9e2f3;">
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">国家/重量</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">类型</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">167KG-500kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">501KG-1000kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1001KG-1670kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1680KG以上</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="background-color: #b8cce4;">
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">伊朗</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">普货</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">63</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">60</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">57</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">54</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">普货4-6天，敏货10-12天</td>
                            </tr>
                            <tr style="background-color: #b8cce4;">
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">敏货</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">78</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">75</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">74</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" class="price-red">72</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 卡塔尔价格表 -->
                <div id="qatar" class="tab-content">
                    <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">卡塔尔超大件普货</h3>
                    <p style="background-color: #e9ecef; padding: 8px; margin-bottom: 10px; font-size: 14px; text-align: center;">可接卡板货、木箱、航空箱、家私家具，建材等各种不规则包装的超大件.</p>
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                        <thead>
                            <tr style="background-color: #d9e2f3;">
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">国家/重量</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">类型</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">167KG-500kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">501KG-1000kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1001KG-1670kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1680KG以上</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="background-color: #b8cce4;">
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">卡塔尔</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">普货</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2"></td>
                            </tr>
                            <tr style="background-color: #b8cce4;">
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">敏货</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">单询</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <footer>
            <p>&copy; 2023 金舟国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </footer>
    </div>

    <script>
        // 页面加载时初始化
        window.onload = function() {
            // 默认显示海运内容，隐藏空运内容
            document.getElementById("airFreightContent").style.display = "none";
            document.getElementById("seaFreightContent").style.display = "block";
            
            // 设置默认标题样式
            document.querySelector(".sea-freight-title").classList.add("title-active");
            document.querySelector(".air-freight-title").classList.add("title-inactive");
        };
        
        function openTab(evt, tabName) {
            // 只针对海运标签内容
            var tabcontent = document.querySelectorAll("#seaFreightContent .tab-content");
            for (var i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            
            // 只针对海运标签按钮
            var tabbuttons = document.querySelectorAll("#seaFreightContent .tab-button");
            for (var i = 0; i < tabbuttons.length; i++) {
                tabbuttons[i].classList.remove("active");
            }
            
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.classList.add("active");
        }
        
        function toggleAirFreight() {
            // 显示空运内容，隐藏海运内容
            document.getElementById("airFreightContent").style.display = "block";
            document.getElementById("seaFreightContent").style.display = "none";
            
            // 切换标题样式
            document.querySelector(".air-freight-title").classList.add("title-active");
            document.querySelector(".air-freight-title").classList.remove("title-inactive");
            document.querySelector(".sea-freight-title").classList.remove("title-active");
            document.querySelector(".sea-freight-title").classList.add("title-inactive");
        }
        
        function toggleSeaFreight() {
            // 显示海运内容，隐藏空运内容
            document.getElementById("airFreightContent").style.display = "none";
            document.getElementById("seaFreightContent").style.display = "block";
            
            // 切换标题样式
            document.querySelector(".sea-freight-title").classList.add("title-active");
            document.querySelector(".sea-freight-title").classList.remove("title-inactive");
            document.querySelector(".air-freight-title").classList.remove("title-active");
            document.querySelector(".air-freight-title").classList.add("title-inactive");
        }
        
        function openAirTab(evt, tabName) {
            // 只针对空运标签内容
            var tabcontent = document.querySelectorAll("#airFreightContent .tab-content");
            for (var i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            
            // 只针对空运标签按钮
            var tabbuttons = document.querySelectorAll("#airFreightContent .tab-button");
            for (var i = 0; i < tabbuttons.length; i++) {
                tabbuttons[i].classList.remove("active");
            }
            
            // 显示当前标签并设为活动状态
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.classList.add("active");
        }
    </script>
</body>
</html> 