// 图片压缩函数
function compressImage(file, callback) {
    // 如果文件小于500KB，不进行压缩
    if (file.size <= 500 * 1024) {
        callback(file);
        return;
    }
    
    // 创建图片对象
    const img = new Image();
    const reader = new FileReader();
    
    reader.onload = function(e) {
        img.src = e.target.result;
        
        img.onload = function() {
            // 创建Canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // 计算压缩后的尺寸
            let width = img.width;
            let height = img.height;
            const maxDimension = 1200; // 最大尺寸
            
            // 如果图片尺寸过大，按比例缩小
            if (width > maxDimension || height > maxDimension) {
                if (width > height) {
                    height = Math.round(height * maxDimension / width);
                    width = maxDimension;
                } else {
                    width = Math.round(width * maxDimension / height);
                    height = maxDimension;
                }
            }
            
            // 设置Canvas尺寸
            canvas.width = width;
            canvas.height = height;
            
            // 绘制到Canvas
            ctx.drawImage(img, 0, 0, width, height);
            
            // 根据原始文件大小确定压缩质量
            let quality = 0.7; // 默认压缩质量70%
            
            if (file.size > 5 * 1024 * 1024) { // 大于5MB
                quality = 0.5;
            } else if (file.size > 2 * 1024 * 1024) { // 大于2MB
                quality = 0.6;
            } else if (file.size > 1 * 1024 * 1024) { // 大于1MB
                quality = 0.7;
            }
            
            // 将Canvas转换为Blob
            canvas.toBlob(
                function(blob) {
                    // 创建新的文件对象
                    const compressedFile = new File(
                        [blob], 
                        file.name, 
                        {
                            type: 'image/jpeg',
                            lastModified: Date.now()
                        }
                    );
                    
                    console.log(`图片已压缩: ${Math.round(file.size/1024)}KB → ${Math.round(blob.size/1024)}KB (${Math.round((1 - blob.size/file.size) * 100)}% 压缩率)`);
                    
                    // 返回压缩后的文件
                    callback(compressedFile);
                }, 
                'image/jpeg', 
                quality
            );
        };
    };
    
    reader.readAsDataURL(file);
}
