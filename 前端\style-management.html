<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>款式管理 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 28px;
            font-weight: 600;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .admin-info {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(52, 152, 219, 0.1);
            padding: 8px 15px;
            border-radius: 20px;
            color: #2c3e50;
        }

        .admin-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .logout-btn {
            color: #e74c3c;
            text-decoration: none;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.3s ease;
            opacity: 0.8;
        }

        .logout-btn:hover {
            opacity: 1;
            background: rgba(231, 76, 60, 0.1);
        }

        .header .back-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .header .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #ecf0f1;
        }

        .toolbar h2 {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 600;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .search-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-input {
            padding: 10px 40px 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            font-size: 14px;
            width: 300px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-btn {
            position: absolute;
            right: 5px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            background: #5a6fd8;
            transform: scale(1.05);
        }

        .clear-search-btn {
            position: absolute;
            right: 40px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .clear-search-btn:hover {
            background: #5a6268;
        }

        .search-results-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            color: #1976d2;
            display: none;
        }

        .search-results-info i {
            margin-right: 8px;
        }

        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }

        /* 分页样式 */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding: 20px 0;
            border-top: 1px solid #e1e5e9;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-size: 14px;
        }

        .page-size-selector select {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background: white;
        }

        .pagination-buttons {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #666;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #f8f9fa;
            border-color: #007bff;
            color: #007bff;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-btn.active {
            background: #007bff;
            border-color: #007bff;
            color: white;
        }

        .page-input-container {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 10px;
        }

        .page-input {
            width: 50px;
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
            font-size: 14px;
        }

        .goto-btn {
            padding: 6px 12px;
            border: 1px solid #007bff;
            background: #007bff;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .goto-btn:hover {
            background: #0056b3;
        }

        .btn-primary {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        .styles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .style-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .style-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #3498db;
        }

        .style-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .style-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }



        .style-actions {
            display: flex;
            gap: 8px;
        }

        .btn-small {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .btn-edit {
            background: #f39c12;
            color: white;
        }

        .btn-edit:hover {
            background: #e67e22;
        }

        .btn-delete {
            background: #e74c3c;
            color: white;
        }

        .btn-delete:hover {
            background: #c0392b;
        }

        .products-preview {
            margin-top: 15px;
        }

        .products-preview h4 {
            font-size: 14px;
            color: #34495e;
            margin-bottom: 10px;
        }

        .products-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .product-item {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            color: #495057;
        }

        .product-item img {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            margin-right: 8px;
            object-fit: cover;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 20px;
            margin-bottom: 10px;
        }

        .empty-state p {
            font-size: 14px;
            line-height: 1.6;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }

        .close {
            color: white;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.3s ease;
        }

        .close:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 30px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
        }

        textarea.form-control {
            resize: vertical;
            min-height: 80px;
        }

        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid #ecf0f1;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .toolbar {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .styles-grid {
                grid-template-columns: 1fr;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-palette"></i> 款式管理</h1>
            <div class="header-right">
                <div class="admin-info">
                    <div class="admin-avatar" id="adminInitial">A</div>
                    <span id="adminName">管理员</span>
                    <a href="#" id="logoutBtn" class="logout-btn" title="退出登录">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
                <a href="admin-dashboard.html" class="back-btn">
                    <i class="fas fa-arrow-left"></i> 返回控制台
                </a>
            </div>
        </div>

        <div class="main-content">
            <div class="toolbar">
                <div class="toolbar-left">
                    <h2>款式组列表</h2>
                    <div class="search-container">
                        <input type="text" id="searchInput" class="search-input" placeholder="搜索款式组名称、商品名称或商品ID...">
                        <button type="button" id="clearSearchBtn" class="clear-search-btn" title="清除搜索">×</button>
                        <button type="button" id="searchBtn" class="search-btn" title="搜索">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <button class="btn-primary" id="createStyleBtn">
                    <i class="fas fa-plus"></i> 创建款式组
                </button>
            </div>

            <div id="searchResultsInfo" class="search-results-info">
                <i class="fas fa-info-circle"></i>
                <span id="searchResultsText"></span>
            </div>

            <div id="stylesContainer">
                <!-- 款式组将通过JavaScript动态加载 -->
                <div class="empty-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    <h3>正在加载款式组...</h3>
                    <p>请稍候</p>
                </div>
            </div>

            <!-- 分页控件 -->
            <div class="pagination-container" id="paginationContainer" style="display: none;">
                <div class="pagination-info">
                    <span id="paginationInfo">显示第 1-9 项，共 0 项</span>
                </div>
                <div class="pagination-controls">
                    <div class="page-size-selector">
                        <span>每页显示:</span>
                        <select id="pageSizeSelect">
                            <option value="9">9</option>
                            <option value="18">18</option>
                            <option value="45">45</option>
                        </select>
                    </div>
                    <div class="pagination-buttons">
                        <button class="pagination-btn" id="firstPageBtn" title="首页">
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button class="pagination-btn" id="prevPageBtn" title="上一页">
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <div class="page-input-container">
                            <span>第</span>
                            <input type="number" class="page-input" id="pageInput" min="1" value="1">
                            <span>页，共 <span id="totalPagesSpan">1</span> 页</span>
                            <button class="goto-btn" id="gotoPageBtn">跳转</button>
                        </div>
                        <button class="pagination-btn" id="nextPageBtn" title="下一页">
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button class="pagination-btn" id="lastPageBtn" title="末页">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑款式组模态框 -->
    <div id="styleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">创建款式组</h3>
                <button class="close" id="closeModal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="styleForm">
                    <div class="form-group">
                        <label for="styleName">款式组名称 *</label>
                        <input type="text" id="styleName" class="form-control" placeholder="请输入款式组名称" required>
                    </div>

                    <div class="form-group">
                        <label>关联商品</label>
                        <button type="button" class="btn-primary" id="selectProductsBtn">
                            <i class="fas fa-search"></i> 选择商品
                        </button>
                        <div id="selectedProducts" style="margin-top: 15px;">
                            <!-- 已选择的商品将显示在这里 -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" id="cancelBtn">取消</button>
                <button type="button" class="btn-primary" id="saveStyleBtn">保存</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentStyleId = null;
        let selectedProductIds = [];
        let allProducts = [];
        let allStyles = []; // 存储所有款式组数据
        let filteredStyles = []; // 存储搜索过滤后的款式组数据
        let currentSearchTerm = ''; // 当前搜索关键词

        // 分页相关变量
        let currentPage = 1; // 当前页码
        let pageSize = 9; // 每页显示数量
        let totalPages = 1; // 总页数

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查管理员登录状态
            const adminData = checkAdminAuth();
            if (!adminData) {
                return; // 如果未登录，checkAdminAuth会重定向到登录页
            }

            // 设置管理员信息
            setupAdminInfo(adminData);

            loadStyles();
            loadProducts();
            setupEventListeners();
        });

        // 检查管理员登录状态
        function checkAdminAuth() {
            // 首先检查URL参数，看是否从admin-dashboard跳转过来
            const urlParams = new URLSearchParams(window.location.search);
            const fromDashboard = urlParams.get('from') === 'dashboard';

            // 检查sessionStorage中的登录状态
            const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');

            if (!adminData.isAdmin) {
                // 未登录或不是管理员，重定向到登录页
                window.location.href = 'admin-login.html';
                return null;
            }

            // 如果从dashboard跳转过来，清除URL参数
            if (fromDashboard && window.history.replaceState) {
                window.history.replaceState({}, document.title, window.location.pathname);
            }

            return adminData;
        }

        // 设置管理员信息
        function setupAdminInfo(adminData) {
            // 设置管理员头像和姓名（如果页面有这些元素）
            const adminInitial = document.getElementById('adminInitial');
            const adminName = document.getElementById('adminName');

            if (adminData.username) {
                if (adminName) adminName.textContent = adminData.username;
                if (adminInitial) adminInitial.textContent = adminData.username.charAt(0).toUpperCase();
            }

            // 设置退出登录功能
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    sessionStorage.removeItem('loggedInAdmin');
                    sessionStorage.removeItem('welcomeShown');
                    window.location.href = 'admin-login.html';
                });
            }
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 创建款式组按钮
            document.getElementById('createStyleBtn').addEventListener('click', () => {
                openStyleModal();
            });

            // 模态框关闭按钮
            document.getElementById('closeModal').addEventListener('click', closeStyleModal);
            document.getElementById('cancelBtn').addEventListener('click', closeStyleModal);

            // 保存款式组按钮
            document.getElementById('saveStyleBtn').addEventListener('click', saveStyle);

            // 选择商品按钮
            document.getElementById('selectProductsBtn').addEventListener('click', openProductSelector);

            // 点击模态框外部关闭
            document.getElementById('styleModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeStyleModal();
                }
            });

            // 搜索功能
            const searchInput = document.getElementById('searchInput');
            const searchBtn = document.getElementById('searchBtn');
            const clearSearchBtn = document.getElementById('clearSearchBtn');

            // 搜索输入事件
            searchInput.addEventListener('input', function(e) {
                const searchTerm = e.target.value.trim();
                if (searchTerm) {
                    clearSearchBtn.style.display = 'flex';
                } else {
                    clearSearchBtn.style.display = 'none';
                    clearSearch();
                }
            });

            // 回车搜索
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });

            // 搜索按钮点击
            searchBtn.addEventListener('click', performSearch);

            // 清除搜索按钮点击
            clearSearchBtn.addEventListener('click', clearSearch);

            // 分页事件监听器
            setupPaginationListeners();
        }

        // 设置分页事件监听器
        function setupPaginationListeners() {
            // 每页显示数量选择
            document.getElementById('pageSizeSelect').addEventListener('change', function(e) {
                pageSize = parseInt(e.target.value);
                currentPage = 1;
                renderCurrentPage();
            });

            // 首页按钮
            document.getElementById('firstPageBtn').addEventListener('click', function() {
                if (currentPage > 1) {
                    currentPage = 1;
                    renderCurrentPage();
                }
            });

            // 上一页按钮
            document.getElementById('prevPageBtn').addEventListener('click', function() {
                if (currentPage > 1) {
                    currentPage--;
                    renderCurrentPage();
                }
            });

            // 下一页按钮
            document.getElementById('nextPageBtn').addEventListener('click', function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    renderCurrentPage();
                }
            });

            // 末页按钮
            document.getElementById('lastPageBtn').addEventListener('click', function() {
                if (currentPage < totalPages) {
                    currentPage = totalPages;
                    renderCurrentPage();
                }
            });

            // 跳转按钮
            document.getElementById('gotoPageBtn').addEventListener('click', function() {
                const pageInput = document.getElementById('pageInput');
                const targetPage = parseInt(pageInput.value);

                if (targetPage >= 1 && targetPage <= totalPages) {
                    currentPage = targetPage;
                    renderCurrentPage();
                } else {
                    pageInput.value = currentPage;
                    alert(`请输入1到${totalPages}之间的页码`);
                }
            });

            // 页码输入框回车事件
            document.getElementById('pageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    document.getElementById('gotoPageBtn').click();
                }
            });
        }

        // 加载所有款式组
        async function loadStyles() {
            try {
                const response = await fetch('/api/styles');
                const data = await response.json();

                if (data.success) {
                    allStyles = data.styles; // 保存到全局变量
                    filteredStyles = data.styles; // 初始化过滤后的数据
                    currentPage = 1; // 重置页码
                    renderCurrentPage();
                } else {
                    showError('加载款式组失败');
                }
            } catch (error) {
                console.error('加载款式组失败:', error);
                showError('网络错误，请稍后重试');
            }
        }

        // 渲染当前页的款式组
        function renderCurrentPage() {
            // 计算总页数
            totalPages = Math.ceil(filteredStyles.length / pageSize);
            if (totalPages === 0) totalPages = 1;

            // 确保当前页在有效范围内
            if (currentPage > totalPages) currentPage = totalPages;
            if (currentPage < 1) currentPage = 1;

            // 计算当前页的数据范围
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = Math.min(startIndex + pageSize, filteredStyles.length);
            const currentPageStyles = filteredStyles.slice(startIndex, endIndex);

            // 渲染款式组
            renderStyles(currentPageStyles);

            // 更新分页信息
            updatePaginationInfo();
        }

        // 渲染款式组列表
        function renderStyles(styles) {
            const container = document.getElementById('stylesContainer');

            if (filteredStyles.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-palette"></i>
                        <h3>暂无款式组</h3>
                        <p>点击"创建款式组"按钮开始创建您的第一个款式组</p>
                    </div>
                `;
                document.getElementById('paginationContainer').style.display = 'none';
                return;
            }

            const stylesHtml = styles.map(style => {
                // 检查是否有搜索关键词需要高亮
                const styleName = currentSearchTerm ? highlightSearchTerm(style.name, currentSearchTerm) : style.name;

                return `
                <div class="style-card">
                    <div class="style-header">
                        <div>
                            <div class="style-title">${styleName}</div>
                            ${style.matchType ? `
                                <div style="font-size: 12px; color: #666; margin-top: 4px;">
                                    ${style.matchType === 'style' ? '匹配款式名称' :
                                      style.matchType === 'product' ? `匹配商品: ${style.matchedProducts.map(p => {
                                          // 检查是匹配商品名称还是商品ID
                                          const isNameMatch = p.name.toLowerCase().includes(style.searchTerm);
                                          const isIdMatch = p.id.toLowerCase().includes(style.searchTerm);
                                          if (isNameMatch && isIdMatch) {
                                              return `${p.name} (ID: ${p.id})`;
                                          } else if (isIdMatch) {
                                              return `${p.name} (ID: ${p.id})`;
                                          } else {
                                              return p.name;
                                          }
                                      }).join(', ')}` :
                                      '匹配款式名称和商品'}
                                </div>
                            ` : ''}
                        </div>
                        <div class="style-actions">
                            <button class="btn-small btn-edit" onclick="editStyle('${style.id}')">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn-small btn-delete" onclick="deleteStyle('${style.id}')">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                    <div class="products-preview">
                        <h4>关联商品 (${style.products ? style.products.length : 0})</h4>
                        <div class="products-list">
                            ${style.products && style.products.length > 0
                                ? style.products.map(product => {
                                    const productName = currentSearchTerm ? highlightSearchTerm(product.name, currentSearchTerm) : product.name;
                                    const isMatched = style.matchedProducts && style.matchedProducts.some(mp => mp.id === product.id);
                                    return `
                                        <div class="product-item" ${isMatched ? 'style="background-color: #fff3cd; border: 1px solid #ffc107;"' : ''}>
                                            <img src="${product.images && product.images.length > 0 ? product.images[0].url : 'https://via.placeholder.com/24x24/f0f0f0/999999?text=无'}" alt="${product.name}">
                                            ${productName}
                                        </div>
                                    `;
                                }).join('')
                                : '<div class="product-item" style="color: #999;">暂无关联商品</div>'
                            }
                        </div>
                    </div>
                </div>
                `;
            }).join('');

            container.innerHTML = `<div class="styles-grid">${stylesHtml}</div>`;

            // 显示分页控件
            if (filteredStyles.length > 0) {
                document.getElementById('paginationContainer').style.display = 'flex';
            }
        }

        // 更新分页信息
        function updatePaginationInfo() {
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, filteredStyles.length);

            // 更新分页信息文本
            document.getElementById('paginationInfo').textContent =
                `显示第 ${startIndex}-${endIndex} 项，共 ${filteredStyles.length} 项`;

            // 更新页码输入框和总页数
            document.getElementById('pageInput').value = currentPage;
            document.getElementById('totalPagesSpan').textContent = totalPages;

            // 更新按钮状态
            document.getElementById('firstPageBtn').disabled = currentPage <= 1;
            document.getElementById('prevPageBtn').disabled = currentPage <= 1;
            document.getElementById('nextPageBtn').disabled = currentPage >= totalPages;
            document.getElementById('lastPageBtn').disabled = currentPage >= totalPages;

            // 更新页面大小选择器
            document.getElementById('pageSizeSelect').value = pageSize;
        }

        // 显示错误信息
        function showError(message) {
            // 这里可以实现更复杂的错误提示
            alert(message);
        }

        // 显示成功信息
        function showSuccess(message) {
            // 这里可以实现更复杂的成功提示
            alert(message);
        }

        // 加载所有商品
        async function loadProducts() {
            try {
                const response = await fetch('/api/products');
                const data = await response.json();

                if (data.success) {
                    allProducts = data.products.filter(product => product.status === 'active');
                } else {
                    console.error('加载商品失败');
                }
            } catch (error) {
                console.error('加载商品失败:', error);
            }
        }

        // 获取已被其他款式组使用的商品ID列表
        function getUsedProductIds() {
            const usedProductIds = new Set();

            allStyles.forEach(style => {
                // 如果是编辑模式，排除当前正在编辑的款式组
                if (currentStyleId && style.id === currentStyleId) {
                    return;
                }

                // 添加该款式组中的所有商品ID
                if (style.productIds && Array.isArray(style.productIds)) {
                    style.productIds.forEach(productId => {
                        usedProductIds.add(productId);
                    });
                }
            });

            return Array.from(usedProductIds);
        }

        // 打开款式组模态框
        function openStyleModal(styleId = null) {
            currentStyleId = styleId;
            selectedProductIds = [];

            if (styleId) {
                // 编辑模式
                loadStyleForEdit(styleId);
                document.getElementById('modalTitle').textContent = '编辑款式组';
            } else {
                // 创建模式
                document.getElementById('modalTitle').textContent = '创建款式组';
                document.getElementById('styleName').value = '';

                updateSelectedProductsDisplay();
            }

            document.getElementById('styleModal').style.display = 'block';
        }

        // 关闭款式组模态框
        function closeStyleModal() {
            document.getElementById('styleModal').style.display = 'none';
            currentStyleId = null;
            selectedProductIds = [];
        }

        // 加载款式组数据用于编辑
        async function loadStyleForEdit(styleId) {
            try {
                const response = await fetch('/api/styles');
                const data = await response.json();

                if (data.success) {
                    const style = data.styles.find(s => s.id === styleId);
                    if (style) {
                        document.getElementById('styleName').value = style.name;

                        selectedProductIds = style.productIds || [];
                        updateSelectedProductsDisplay();
                    }
                }
            } catch (error) {
                console.error('加载款式组数据失败:', error);
                showError('加载款式组数据失败');
            }
        }

        // 保存款式组
        async function saveStyle() {
            const name = document.getElementById('styleName').value.trim();


            if (!name) {
                showError('请输入款式组名称');
                return;
            }

            const styleData = {
                name,
                productIds: selectedProductIds
            };

            try {
                const url = currentStyleId ? `/api/styles/${currentStyleId}` : '/api/styles';
                const method = currentStyleId ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(styleData)
                });

                const data = await response.json();

                if (data.success) {
                    showSuccess(currentStyleId ? '款式组更新成功' : '款式组创建成功');
                    closeStyleModal();
                    loadStyles(); // 重新加载列表
                } else {
                    showError(data.message || '保存失败');
                }
            } catch (error) {
                console.error('保存款式组失败:', error);
                showError('网络错误，请稍后重试');
            }
        }

        // 编辑款式组
        function editStyle(styleId) {
            openStyleModal(styleId);
        }

        // 删除款式组
        async function deleteStyle(styleId) {
            if (!confirm('确定要删除这个款式组吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await fetch(`/api/styles/${styleId}`, {
                    method: 'DELETE'
                });

                const data = await response.json();

                if (data.success) {
                    showSuccess('款式组删除成功');
                    loadStyles(); // 重新加载列表
                } else {
                    showError(data.message || '删除失败');
                }
            } catch (error) {
                console.error('删除款式组失败:', error);
                showError('网络错误，请稍后重试');
            }
        }

        // 打开商品选择器
        function openProductSelector() {
            // 创建商品选择器模态框
            const selectorHtml = `
                <div id="productSelectorModal" class="modal" style="display: block;">
                    <div class="modal-content" style="max-width: 800px;">
                        <div class="modal-header">
                            <h3>选择商品</h3>
                            <button class="close" onclick="closeProductSelector()">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div style="margin-bottom: 20px;">
                                <input type="text" id="productSearchInput" class="form-control" placeholder="搜索商品名称...">
                            </div>
                            <div id="productsList" style="max-height: 400px; overflow-y: auto;">
                                ${renderProductsList()}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn-secondary" onclick="closeProductSelector()">取消</button>
                            <button type="button" class="btn-primary" onclick="confirmProductSelection()">确认选择</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', selectorHtml);

            // 设置搜索功能
            document.getElementById('productSearchInput').addEventListener('input', function(e) {
                const searchTerm = e.target.value.toLowerCase();
                // 先过滤掉已被使用的商品，再进行搜索
                const usedProductIds = getUsedProductIds();
                const availableProducts = allProducts.filter(product =>
                    !usedProductIds.includes(product.id)
                );
                const filteredProducts = availableProducts.filter(product =>
                    product.name.toLowerCase().includes(searchTerm)
                );
                document.getElementById('productsList').innerHTML = renderProductsList(filteredProducts);
            });
        }

        // 渲染商品列表
        function renderProductsList(products = allProducts) {
            // 获取已被其他款式组使用的商品ID列表
            const usedProductIds = getUsedProductIds();

            // 过滤掉已被使用的商品
            const availableProducts = products.filter(product =>
                !usedProductIds.includes(product.id)
            );

            // 如果没有可用商品，显示提示信息
            if (availableProducts.length === 0) {
                return `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
                        <div>暂无可选择的商品</div>
                        <div style="font-size: 14px; margin-top: 5px;">所有商品都已被其他款式组使用</div>
                    </div>
                `;
            }

            return availableProducts.map(product => {
                const isSelected = selectedProductIds.includes(product.id);
                const mainImage = product.images && product.images.length > 0
                    ? product.images[0].url
                    : 'https://via.placeholder.com/60x60/f0f0f0/999999?text=无图';

                return `
                    <div class="product-selector-item" style="display: flex; align-items: center; padding: 10px; border: 1px solid #eee; margin-bottom: 10px; border-radius: 8px; cursor: pointer; ${isSelected ? 'background-color: #e3f2fd; border-color: #2196f3;' : ''}" onclick="toggleProductSelection('${product.id}')">
                        <input type="checkbox" ${isSelected ? 'checked' : ''} style="margin-right: 15px;" onchange="toggleProductSelection('${product.id}')">
                        <img src="${mainImage}" alt="${product.name}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 6px; margin-right: 15px;">
                        <div style="flex: 1;">
                            <div style="font-weight: 500; margin-bottom: 5px;">${product.name}</div>
                            <div style="color: #666; font-size: 14px;">价格: ¥${product.price} | 库存: ${product.stock}</div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 切换商品选择状态
        function toggleProductSelection(productId) {
            const index = selectedProductIds.indexOf(productId);
            if (index > -1) {
                selectedProductIds.splice(index, 1);
            } else {
                selectedProductIds.push(productId);
            }

            // 更新复选框状态
            const checkbox = document.querySelector(`input[onchange="toggleProductSelection('${productId}')"]`);
            if (checkbox) {
                checkbox.checked = selectedProductIds.includes(productId);
            }

            // 更新商品项的样式
            const productItem = checkbox.closest('.product-selector-item');
            if (productItem) {
                if (selectedProductIds.includes(productId)) {
                    productItem.style.backgroundColor = '#e3f2fd';
                    productItem.style.borderColor = '#2196f3';
                } else {
                    productItem.style.backgroundColor = '';
                    productItem.style.borderColor = '#eee';
                }
            }
        }

        // 关闭商品选择器
        function closeProductSelector() {
            const modal = document.getElementById('productSelectorModal');
            if (modal) {
                modal.remove();
            }
        }

        // 确认商品选择
        function confirmProductSelection() {
            updateSelectedProductsDisplay();
            closeProductSelector();
        }

        // 更新已选择商品的显示
        function updateSelectedProductsDisplay() {
            const container = document.getElementById('selectedProducts');

            if (selectedProductIds.length === 0) {
                container.innerHTML = '<div style="color: #999; font-style: italic;">暂未选择商品</div>';
                return;
            }

            const selectedProducts = allProducts.filter(product =>
                selectedProductIds.includes(product.id)
            );

            const html = selectedProducts.map(product => {
                const mainImage = product.images && product.images.length > 0
                    ? product.images[0].url
                    : 'https://via.placeholder.com/40x40/f0f0f0/999999?text=无';

                return `
                    <div class="selected-product-item" style="display: flex; align-items: center; padding: 8px; background: #f8f9fa; border-radius: 6px; margin-bottom: 8px;">
                        <img src="${mainImage}" alt="${product.name}" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px; margin-right: 12px;">
                        <div style="flex: 1; font-size: 14px;">${product.name}</div>
                        <button type="button" onclick="removeSelectedProduct('${product.id}')" style="background: #dc3545; color: white; border: none; border-radius: 4px; padding: 4px 8px; cursor: pointer; font-size: 12px;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
            }).join('');

            container.innerHTML = html;
        }

        // 移除已选择的商品
        function removeSelectedProduct(productId) {
            const index = selectedProductIds.indexOf(productId);
            if (index > -1) {
                selectedProductIds.splice(index, 1);
                updateSelectedProductsDisplay();
            }
        }

        // 执行搜索
        function performSearch() {
            const searchTerm = document.getElementById('searchInput').value.trim();
            if (!searchTerm) {
                clearSearch();
                return;
            }

            currentSearchTerm = searchTerm;
            const searchResults = searchStyles(searchTerm);

            filteredStyles = searchResults;
            currentPage = 1; // 重置到第一页
            renderCurrentPage();
            showSearchResults(searchTerm, searchResults.length);
        }

        // 搜索款式组
        function searchStyles(searchTerm) {
            const term = searchTerm.toLowerCase();
            const results = [];

            allStyles.forEach(style => {
                let matchType = null;
                let matchedProducts = [];

                // 搜索款式组名称
                if (style.name.toLowerCase().includes(term)) {
                    matchType = 'style';
                }

                // 搜索商品名称和商品ID
                if (style.products && style.products.length > 0) {
                    const matchingProducts = style.products.filter(product =>
                        product.name.toLowerCase().includes(term) ||
                        product.id.toLowerCase().includes(term)
                    );
                    if (matchingProducts.length > 0) {
                        matchType = matchType ? 'both' : 'product';
                        matchedProducts = matchingProducts;
                    }
                }

                if (matchType) {
                    results.push({
                        ...style,
                        matchType,
                        matchedProducts,
                        searchTerm: term
                    });
                }
            });

            return results;
        }

        // 清除搜索
        function clearSearch() {
            document.getElementById('searchInput').value = '';
            document.getElementById('clearSearchBtn').style.display = 'none';
            document.getElementById('searchResultsInfo').style.display = 'none';

            currentSearchTerm = '';
            filteredStyles = allStyles;
            currentPage = 1; // 重置到第一页
            renderCurrentPage();
        }

        // 显示搜索结果信息
        function showSearchResults(searchTerm, count) {
            const searchResultsInfo = document.getElementById('searchResultsInfo');
            const searchResultsText = document.getElementById('searchResultsText');

            if (count > 0) {
                searchResultsText.textContent = `找到 ${count} 个包含"${searchTerm}"的款式组`;
            } else {
                searchResultsText.textContent = `未找到包含"${searchTerm}"的款式组或商品`;
            }

            searchResultsInfo.style.display = 'block';
        }

        // 高亮搜索关键词
        function highlightSearchTerm(text, searchTerm) {
            if (!searchTerm || !text) return text;

            const regex = new RegExp(`(${searchTerm})`, 'gi');
            return text.replace(regex, '<span class="highlight">$1</span>');
        }
    </script>
</body>
</html>
