<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户中心 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/order-limit.js"></script>
    <script src="js/blacklist-handler.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
            flex-wrap: nowrap;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .page-title {
            color: #0c4da2;
            font-size: 32px;
            margin-bottom: 0;
        }
        
        .logout-button {
            display: inline-block;
            color: white;
            text-decoration: none;
            font-size: 16px;
            background-color: #d9534f;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #d43f3a;
            transition: all 0.3s ease;
            margin-left: 10px;
        }
        
        .logout-button:hover {
            background-color: #c9302c;
        }
        
        .nav-button {
            display: inline-block;
            color: #0c4da2;
            text-decoration: none;
            font-size: 16px;
            background-color: #fff;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .nav-button:hover {
            background-color: #f5f5f5;
        }
        
        .welcome-header {
            color: #0c4da2;
            font-size: 20px;
            font-weight: bold;
            margin: 0 20px;
            white-space: nowrap;
        }
        
        .welcome-header span {
            color: #0c4da2;
            font-weight: bold;
        }
        
        /* 用户中心内容区域 */
        .dashboard-content {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin: 50px 0;
        }
        
        .dashboard-card {
            flex: 1;
            min-width: 300px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 25px;
            transition: all 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .dashboard-card h3 {
            color: #0c4da2;
            font-size: 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .dashboard-card h3 i {
            margin-right: 10px;
            font-size: 24px;
        }
        
        /* 专属客服样式 */
        .customer-service {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .cs-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 3px solid #0c4da2;
            overflow: hidden;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .cs-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .cs-info {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .cs-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        
        .cs-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .cs-contact {
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
        }
        
        .cs-contact-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            transition: all 0.2s ease;
        }
        
        .cs-contact-item:hover {
            background-color: #e9ecef;
        }
        
        .cs-contact-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
            color: #0c4da2;
        }
        
        .cs-contact-btn {
            display: block;
            background-color: #0c4da2;
            color: white;
            text-align: center;
            padding: 12px;
            border-radius: 5px;
            margin-top: 15px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .cs-contact-btn:hover {
            background-color: #083778;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            color: #666;
            font-size: 14px;
        }
        
        /* 客服聊天窗口 */
        .chat-window {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 350px;
            height: 450px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            z-index: 1000;
            display: none;
        }
        
        .chat-header {
            background-color: #0c4da2;
            color: white;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chat-header-info {
            display: flex;
            align-items: center;
        }
        
        .chat-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 10px;
        }
        
        .chat-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .chat-name {
            font-weight: bold;
        }
        
        .chat-status {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .chat-close {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
        }
        
        .chat-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            background-color: #f5f5f5;
        }
        
        .message {
            margin-bottom: 15px;
            max-width: 80%;
        }
        
        .message-content {
            padding: 10px;
            border-radius: 10px;
            font-size: 14px;
        }
        
        .message-time {
            font-size: 11px;
            color: #888;
            margin-top: 5px;
            text-align: right;
        }
        
        .message.received {
            align-self: flex-start;
        }
        
        .message.received .message-content {
            background-color: white;
            border: 1px solid #e0e0e0;
            border-top-left-radius: 0;
        }
        
        .message.sent {
            align-self: flex-end;
            margin-left: auto;
        }
        
        .message.sent .message-content {
            background-color: #0c4da2;
            color: white;
            border-top-right-radius: 0;
        }
        
        .chat-input {
            display: flex;
            padding: 10px;
            border-top: 1px solid #e0e0e0;
        }
        
        .chat-input input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 20px;
            margin-right: 10px;
            font-size: 14px;
        }
        
        .chat-input input:focus {
            outline: none;
            border-color: #0c4da2;
        }
        
        .chat-input button {
            background-color: #0c4da2;
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .chat-input button:hover {
            background-color: #083778;
        }
        
        /* 订单列表样式 */
        .order-list {
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            padding-right: 5px;
        }
        
        .order-item {
            background-color: #f9f9f9;
            border: 1px solid #eee;
            border-left: 3px solid #0c4da2;
            border-radius: 5px;
            margin-bottom: 15px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        
        .order-item:hover {
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px dashed #ddd;
        }
        
        .order-id {
            font-weight: bold;
            color: #0c4da2;
        }
        
        .order-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            background-color: #ff9800;
        }
        
        .order-status.pending {
            background-color: #ff9800;
        }
        
        .order-status.processing {
            background-color: #2196F3;
        }
        
        .order-status.shipped {
            background-color: #4CAF50;
        }
        
        .order-status.delivered {
            background-color: #4CAF50;
        }
        
        .order-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
            color: #666;
        }
        
        .order-address {
            font-size: 14px;
            margin-top: 10px;
            color: #555;
        }
        
        .order-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px dashed #ddd;
        }
        
        .order-details-btn {
            padding: 6px 12px;
            background-color: #0c4da2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .order-details-btn:hover {
            background-color: #083778;
        }
        
        /* 订单详情弹窗 */
        .order-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .order-modal-content {
            background-color: #fff;
            width: 90%;
            max-width: 700px;
            max-height: 80vh;
            border-radius: 10px;
            overflow-y: auto;
            padding: 20px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        }
        
        .order-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 15px;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .order-modal-title {
            font-size: 22px;
            color: #0c4da2;
            font-weight: bold;
        }
        
        .order-modal-close {
            font-size: 24px;
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
        }
        
        .order-modal-close:hover {
            color: #333;
        }
        
        .order-modal-body {
            /* 订单详情将通过JavaScript动态加载 */
        }
        
        .order-items-title {
            font-size: 18px;
            color: #0c4da2;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
        
        .order-item-list {
            margin-bottom: 20px;
        }
        
        .order-product-item {
            display: flex;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
            align-items: center;
        }
        
        .order-product-img {
            width: 70px;
            height: 70px;
            object-fit: cover;
            border-radius: 5px;
            margin-right: 15px;
            border: 1px solid #eee;
        }

        .order-product-img-placeholder {
            width: 70px;
            height: 70px;
            background-color: #f0f0f0;
            border: 2px dashed #ccc;
            border-radius: 5px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #999;
            text-align: center;
        }
        
        .order-product-details {
            flex: 1;
        }
        
        .order-product-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .order-product-meta {
            font-size: 13px;
            color: #666;
            display: flex;
            flex-wrap: wrap;
        }
        
        .order-product-meta span {
            margin-right: 15px;
        }
        
        .order-shipping-info {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        
        .no-orders {
            text-align: center;
            padding: 40px 0;
            color: #666;
        }
        
        /* 自定义滚动条样式 */
        .order-list::-webkit-scrollbar {
            width: 6px;
        }
        
        .order-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        
        .order-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }
        
        .order-list::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        ::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
            font-size: 18px;
            opacity: 1; /* Firefox */
        }

        :-ms-input-placeholder { /* Internet Explorer 10-11 */
            font-size: 18px;
        }

        ::-ms-input-placeholder { /* Microsoft Edge */
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-container">
                <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="page-title">用户中心</h1>
            </div>
            <div class="welcome-header">
                欢迎 <span id="username">用户</span>
            </div>
            <div>
                <a href="main.html" class="nav-button">返回首页</a>
                <a href="#" class="logout-button" id="logoutButton">
                    <i class="fas fa-sign-out-alt"></i> 退出登录
                </a>
            </div>
        </div>
        
        <div class="dashboard-content">
            <div class="dashboard-card">
                <h3 style="margin-bottom: 10px;"><i class="fas fa-headset"></i> 我的专属客服 - 李小姐 <span style="color: #ffc107; font-size: 12px;">(高级物流顾问)</span></h3>
                <div class="customer-service">
                    <div class="cs-info" style="margin-bottom: 5px;">
                    </div>
                    
                    <!-- 嵌入式聊天窗口 -->
                    <div class="embedded-chat" style="width: 100%; border: 1px solid #e0e0e0; border-radius: 10px; overflow: hidden; margin-top: 5px; height: 475px; display: flex; flex-direction: column;">
                        <div class="chat-messages" id="embeddedChatMessages" style="flex: 1; padding: 15px; overflow-y: auto; background-color: #f5f5f5;">
                            <!-- 消息内容会通过JavaScript动态添加 -->
                        </div>
                        <div class="chat-actions" style="display: flex; justify-content: space-between; padding: 10px; background-color: #f8f9fa; border-top: 1px solid #e0e0e0;">
                            <button onclick="showMoreContactMethods()" style="background-color: #f8f9fa; color: #0c4da2; border: 1px solid #0c4da2; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-size: 14px;">
                                <i class="fas fa-address-book"></i> 更多联系方式
                            </button>
                            <button onclick="showSendOrderForm()" style="background-color: #0c4da2; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-size: 14px;">
                                <i class="fas fa-shopping-cart"></i> 发送订单
                            </button>
                        </div>
                        <div class="chat-input" style="display: flex; padding: 10px; border-top: 1px solid #e0e0e0; background-color: #fff; align-items: flex-end; position: relative;">
                            <textarea id="embeddedMessageInput" placeholder="请输入您的问题..." maxlength="200" style="flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 20px; margin-right: 10px; font-size: 16px; resize: none; overflow: hidden; min-height: 40px; max-height: 120px;"></textarea>
                            <span id="charCount" style="position: absolute; bottom: 5px; right: 60px; font-size: 12px; color: #999;">0/200</span>
                            <button onclick="sendEmbeddedMessage()" style="background-color: #0c4da2; color: white; border: none; width: 40px; height: 40px; border-radius: 50%; cursor: pointer;"><i class="fas fa-paper-plane"></i></button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-card">
                <h3><i class="fas fa-box"></i> 我的订单</h3>
                <div id="ordersContainer">
                    <p class="no-orders" id="noOrdersMessage" style="display: none;">
                    您暂无订单记录
                </p>
                    <div class="order-list" id="orderList">
                        <!-- 订单列表将通过JavaScript动态加载 -->
                    </div>
                </div>
                <a href="web_order.html" class="cs-contact-btn order-now-btn" id="orderButton">
                    <i class="fas fa-plus-circle"></i> 立即下单
                </a>
            </div>
        </div>
        
        <footer>
            <p>&copy; 2023 金舟国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </footer>
    </div>
    
    <!-- 订单详情弹窗 -->
    <div class="order-modal" id="orderModal">
        <div class="order-modal-content">
            <div class="order-modal-header">
                <div class="order-modal-title">订单详情</div>
                <button class="order-modal-close" onclick="closeOrderModal()">&times;</button>
            </div>
            <div class="order-modal-body" id="orderModalBody">
                <!-- 订单详情将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>
    
    <!-- 客服聊天窗口 -->
    <div class="chat-window" id="chatWindow">
        <div class="chat-header">
            <div class="chat-header-info">
                <div>
                    <div class="chat-name">李小姐</div>
                    <div class="chat-status">在线</div>
                </div>
            </div>
            <button class="chat-close" onclick="closeChat()">×</button>
        </div>
        <div class="chat-messages" id="chatMessages">
            <!-- 消息内容会通过JavaScript动态添加 -->
        </div>
        <div class="chat-input">
            <input type="text" id="messageInput" placeholder="请输入您的问题..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()"><i class="fas fa-paper-plane"></i></button>
        </div>
    </div>
    
    <script>
        // 嵌入式聊天窗口功能
        document.addEventListener('DOMContentLoaded', function() {
            // 在嵌入式聊天窗口显示欢迎消息
            setTimeout(() => {
                addEmbeddedMessage('您好！我是您的专属客服李小姐，很高兴为您服务。请问有什么可以帮助您的吗？', 'received');
            }, 1000);
            
            // 为嵌入式聊天输入框添加回车键事件和自动调整高度功能
            const embeddedInput = document.getElementById('embeddedMessageInput');
            if (embeddedInput) {
                // 自动调整高度函数
                function autoResize() {
                    embeddedInput.style.height = 'auto'; // 重置高度
                    embeddedInput.style.height = (embeddedInput.scrollHeight) + 'px'; // 设置为内容高度
                }
                
                // 初始化高度
                autoResize();
                
                // 监听输入事件自动调整高度并更新字符计数
                embeddedInput.addEventListener('input', function() {
                    autoResize();
                    updateCharCount();
                });
                
                // 字符计数器更新函数
                function updateCharCount() {
                    const charCountElement = document.getElementById('charCount');
                    if (charCountElement) {
                        const currentLength = embeddedInput.value.length;
                        charCountElement.textContent = currentLength + '/200';
                        
                        // 当接近限制时改变颜色提醒
                        if (currentLength > 180) {
                            charCountElement.style.color = '#ff9800';
                        } else if (currentLength > 190) {
                            charCountElement.style.color = '#f44336';
                        } else {
                            charCountElement.style.color = '#999';
                        }
                    }
                }
                
                // 回车键发送消息
                embeddedInput.addEventListener('keypress', function(event) {
                    if (event.key === 'Enter' && !event.shiftKey) {
                        sendEmbeddedMessage();
                        event.preventDefault();
                    }
                });
            }
        });
        
        function sendEmbeddedMessage() {
            const messageInput = document.getElementById('embeddedMessageInput');
            if (!messageInput) {
                console.error('嵌入式消息输入框未找到');
                return;
            }

            const message = messageInput.value.trim();
            if (message) {
                // 添加用户发送的消息
                addEmbeddedMessage(message, 'sent');
                messageInput.value = '';
                
                // 重置输入框高度
                messageInput.style.height = 'auto';
                
                // 让输入框重新获得焦点
                messageInput.focus();

                // 模拟客服回复
                setTimeout(() => {
                    let reply = generateReply(message);
                    addEmbeddedMessage(reply, 'received');
                }, Math.random() * 1000 + 500); // 随机延迟500-1500ms
            }
        }
        
        // 显示更多联系方式
        function showMoreContactMethods() {
            // 创建联系方式弹窗
            const modal = document.createElement('div');
            modal.style.position = 'fixed';
            modal.style.top = '0';
            modal.style.left = '0';
            modal.style.width = '100%';
            modal.style.height = '100%';
            modal.style.backgroundColor = 'rgba(0,0,0,0.5)';
            modal.style.display = 'flex';
            modal.style.justifyContent = 'center';
            modal.style.alignItems = 'center';
            modal.style.zIndex = '2000';
            
            const content = document.createElement('div');
            content.style.backgroundColor = 'white';
            content.style.borderRadius = '10px';
            content.style.padding = '20px';
            content.style.width = '90%';
            content.style.maxWidth = '500px';
            content.style.position = 'relative';
            
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '&times;';
            closeBtn.style.position = 'absolute';
            closeBtn.style.right = '10px';
            closeBtn.style.top = '10px';
            closeBtn.style.border = 'none';
            closeBtn.style.background = 'none';
            closeBtn.style.fontSize = '24px';
            closeBtn.style.cursor = 'pointer';
            closeBtn.onclick = function() {
                document.body.removeChild(modal);
            };
            
            const title = document.createElement('h3');
            title.textContent = '联系方式';
            title.style.marginBottom = '20px';
            title.style.color = '#0c4da2';
            
            const contactList = document.createElement('div');
            contactList.innerHTML = `
                <div style="margin-bottom: 15px; display: flex; align-items: center;">
                    <i class="fas fa-phone" style="color: #0c4da2; width: 30px; text-align: center; font-size: 18px;"></i>
                    <span style="margin-left: 10px;">************ (工作时间: 9:00-18:00)</span>
                </div>
                <div style="margin-bottom: 15px; display: flex; align-items: center;">
                    <i class="fas fa-envelope" style="color: #0c4da2; width: 30px; text-align: center; font-size: 18px;"></i>
                    <span style="margin-left: 10px;"><EMAIL></span>
                </div>
                <div style="margin-bottom: 15px; display: flex; align-items: center;">
                    <i class="fab fa-weixin" style="color: #0c4da2; width: 30px; text-align: center; font-size: 18px;"></i>
                    <span style="margin-left: 10px;">微信号: JZ_Service_8888</span>
                </div>
            `;
            
            content.appendChild(closeBtn);
            content.appendChild(title);
            content.appendChild(contactList);
            modal.appendChild(content);
            
            document.body.appendChild(modal);
        }
        
        // 显示发送订单表单
        function showSendOrderForm() {
            window.location.href = 'web_order.html';
        }
        
        function addEmbeddedMessage(text, type) {
            const chatMessages = document.getElementById('embeddedChatMessages');
            if (!chatMessages) {
                console.error('嵌入式聊天消息容器未找到');
                return;
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.style.marginBottom = '15px';
            messageDiv.style.maxWidth = '80%';
            messageDiv.style.width = 'fit-content'; // 使宽度适应内容
            
            if (type === 'sent') {
                messageDiv.style.marginLeft = 'auto';
            }

            const now = new Date();
            const timeStr = now.getHours().toString().padStart(2, '0') + ':' +
                          now.getMinutes().toString().padStart(2, '0');

            // 安全地处理HTML内容
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.style.padding = '10px';
            messageContent.style.borderRadius = '10px';
            messageContent.style.fontSize = '18px';
            messageContent.style.wordWrap = 'break-word'; // 允许长单词换行
            messageContent.style.overflowWrap = 'break-word'; // 确保内容不会溢出
            messageContent.style.whiteSpace = 'pre-wrap'; // 保留换行符和空格，但允许文本换行
            messageContent.style.maxWidth = '100%'; // 确保内容不会超出消息气泡
            
            if (type === 'received') {
                messageContent.style.backgroundColor = 'white';
                messageContent.style.border = '1px solid #e0e0e0';
                messageContent.style.borderTopLeftRadius = '0';
            } else {
                messageContent.style.backgroundColor = '#0c4da2';
                messageContent.style.color = 'white';
                messageContent.style.borderTopRightRadius = '0';
            }
            
            messageContent.textContent = text;

            const messageTime = document.createElement('div');
            messageTime.className = 'message-time';
            messageTime.style.fontSize = '11px';
            messageTime.style.color = '#888';
            messageTime.style.marginTop = '5px';
            messageTime.style.textAlign = 'right';
            messageTime.textContent = timeStr;

            messageDiv.appendChild(messageContent);
            messageDiv.appendChild(messageTime);

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 用户认证和初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 安全地获取用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to parse user data:', error);
                sessionStorage.removeItem('loggedInUser');
            }

            // 验证用户登录状态
            if (!loggedInUser || !loggedInUser.isLoggedIn || !loggedInUser.username) {
                alert('请先登录');
                window.location.href = 'login.html';
                return;
            }
            
            // 检查用户是否被拉黑
            BlacklistHandler.checkUserBlacklisted();
            
            // 定期检查用户状态
            setInterval(BlacklistHandler.checkUserBlacklisted, 30000);

            // 显示用户名
            const usernameElement = document.getElementById('username');
            if (usernameElement) {
                usernameElement.textContent = loggedInUser.username;
            }
            
            // 区分真实用户和测试用户
            const isTestUser = isUserTestAccount(loggedInUser.username);
            
            // 对测试用户显示提示信息
            if (isTestUser) {
                const header = document.querySelector('.header');
                if (header) {
                    const testNotice = document.createElement('div');
                    testNotice.style.background = '#fff3cd';
                    testNotice.style.color = '#856404';
                    testNotice.style.padding = '8px 15px';
                    testNotice.style.borderRadius = '5px';
                    testNotice.style.margin = '10px 0';
                    testNotice.style.fontSize = '14px';
                    testNotice.style.width = '100%';
                    testNotice.style.textAlign = 'center';
                    testNotice.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 您正在使用测试账号，部分功能可能受限。建议<a href="register.html" style="color:#856404;text-decoration:underline;font-weight:bold;">注册</a>一个账号以获得完整体验。';
                    
                    header.parentNode.insertBefore(testNotice, header.nextSibling);
                }
            }

            // 退出登录处理
            const logoutButton = document.getElementById('logoutButton');
            if (logoutButton) {
                logoutButton.addEventListener('click', function(e) {
                    e.preventDefault();

                    if (confirm('确定要退出登录吗？')) {
                        // 清除所有登录相关数据
                        sessionStorage.removeItem('loggedInUser');
                        
                        // 跳转到主页
                        window.location.href = 'main.html';
                    }
                });
            }

            // 加载订单列表
            loadOrderList(loggedInUser);
            
            // 检查每日订单限制
            if (typeof checkDailyOrderLimit === 'function') {
                checkDailyOrderLimit();
            }
        });
        
        // 检查是否为测试账户
        function isUserTestAccount(username) {
            const testUsernames = ['test', 'admin', 'user'];
            return testUsernames.includes(username.toLowerCase());
        }
        
        // 客服聊天功能
        function startChat() {
            const chatWindow = document.getElementById('chatWindow');
            if (!chatWindow) {
                console.error('Chat window not found');
                return;
            }

            chatWindow.style.display = 'flex';

            // 如果是第一次打开聊天，显示欢迎消息
            const chatMessages = document.getElementById('chatMessages');
            if (chatMessages && chatMessages.children.length === 0) {
                // 添加欢迎消息
                setTimeout(() => {
                    addMessage('您好！我是您的专属客服李小姐，很高兴为您服务。请问有什么可以帮助您的吗？', 'received');
                }, 1000);
            }

            // 聚焦输入框
            const messageInput = document.getElementById('messageInput');
            if (messageInput) {
                messageInput.focus();
            }
        }

        function closeChat() {
            const chatWindow = document.getElementById('chatWindow');
            if (chatWindow) {
                chatWindow.style.display = 'none';
            }
        }

        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            if (!messageInput) {
                console.error('Message input not found');
                return;
            }

            const message = messageInput.value.trim();

            if (message) {
                // 添加用户发送的消息
                addMessage(message, 'sent');
                messageInput.value = '';

                // 模拟客服回复
                setTimeout(() => {
                    let reply = generateReply(message);
                    addMessage(reply, 'received');
                }, Math.random() * 1000 + 500); // 随机延迟500-1500ms
            }
        }

        function generateReply(message) {
            const lowerMessage = message.toLowerCase();

            if (lowerMessage.includes('价格') || lowerMessage.includes('费用') || lowerMessage.includes('多少钱')) {
                return '您好，我们的运输价格根据目的地和货物重量有所不同。您可以在"运输价格"页面查询详细费用，或者告诉我您的具体需求，我可以为您提供精确报价。';
            } else if (lowerMessage.includes('时间') || lowerMessage.includes('多久') || lowerMessage.includes('几天')) {
                return '我们的标准运输时间为：国内3-5个工作日，国际10-15个工作日。特殊地区可能需要更长时间，详情可查看我们的配送时效表。';
            } else if (lowerMessage.includes('订单') || lowerMessage.includes('查询') || lowerMessage.includes('追踪')) {
                return '您可以在"我的订单"页面查询您的所有订单状态。如需具体订单的详细信息，请提供订单号，我会立即为您查询。';
            } else if (lowerMessage.includes('下单') || lowerMessage.includes('寄件')) {
                return '您可以点击"立即下单"按钮开始下单流程，我们支持在线下单，操作简单便捷。如需帮助，我随时为您服务。';
            } else if (lowerMessage.includes('联系') || lowerMessage.includes('电话')) {
                return '您可以通过以下方式联系我们：\n客服热线：************（工作时间：9:00-18:00）\n邮箱：<EMAIL>\n微信：JZ_Service_8888';
            } else {
                return '感谢您的咨询。我已收到您的问题，将尽快为您解答。如有紧急需求，也可以拨打我们的客服热线：************。';
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                sendMessage();
                event.preventDefault();
            }
        }

        function addMessage(text, type) {
            const chatMessages = document.getElementById('chatMessages');
            if (!chatMessages) {
                console.error('Chat messages container not found');
                return;
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const now = new Date();
            const timeStr = now.getHours().toString().padStart(2, '0') + ':' +
                          now.getMinutes().toString().padStart(2, '0');

            // 安全地处理HTML内容
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.textContent = text; // 使用textContent防止XSS

            const messageTime = document.createElement('div');
            messageTime.className = 'message-time';
            messageTime.textContent = timeStr;

            messageDiv.appendChild(messageContent);
            messageDiv.appendChild(messageTime);

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 加载订单列表
        function loadOrderList(loggedInUser) {
            // 显示加载状态
            const orderListElement = document.getElementById('orderList');
            const noOrdersMessage = document.getElementById('noOrdersMessage');
            orderListElement.innerHTML = '<div class="loading-message">正在加载订单数据...</div>';
            orderListElement.style.display = 'block';
            noOrdersMessage.style.display = 'none';
            
            // 从服务器获取订单数据
            fetch('/api/get-orders')
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    const orders = result.data || [];
                    
                    // 区分测试用户和注册用户，只显示属于当前用户的订单
                    let userOrders = [];
                    
                    if (isUserTestAccount(loggedInUser.username)) {
                        // 测试用户的订单:
                        // 1. 没有关联userEmail的旧订单
                        // 2. 或者明确关联该测试用户名的订单
                        userOrders = orders.filter(order => 
                            !order.userEmail || 
                            (order.username === loggedInUser.username)
                        );
                    } else {
                        // 真实用户只显示关联其用户名和邮箱的订单
                        userOrders = orders.filter(order => 
                            (order.userEmail === loggedInUser.email) && 
                            (order.username === loggedInUser.username)
                        );
                    }
                    
                    // 缓存订单数据
                    window.ordersCache = orders;
                    
                    if (userOrders.length > 0) {
                        // 有订单，显示订单列表
                        orderListElement.style.display = 'block';
                        noOrdersMessage.style.display = 'none';
                        
                        // 清空当前列表
                        orderListElement.innerHTML = '';
                        
                        // 添加每个订单
                        userOrders.forEach(order => {
                            const orderElement = createOrderElement(order);
                            orderListElement.appendChild(orderElement);
                        });
                        
                        // 检查订单数量，如果超过两个，调整滚动条样式
                        if (userOrders.length > 2) {
                            // 设置固定高度，启用滚动
                            orderListElement.style.maxHeight = '400px';
                            orderListElement.style.overflowY = 'auto';
                        } else {
                            // 少于或等于两个订单，不需要滚动
                            orderListElement.style.maxHeight = 'none';
                            orderListElement.style.overflowY = 'visible';
                        }
                    } else {
                        // 没有订单，显示无订单消息
                        orderListElement.style.display = 'none';
                        noOrdersMessage.style.display = 'block';
                    }
                } else {
                    // 加载失败
                    orderListElement.innerHTML = '<div class="error-message">加载订单数据失败</div>';
                    console.error("Failed to load orders:", result.message);
                }
            })
            .catch(error => {
                console.error('Error loading orders:', error);
                orderListElement.innerHTML = '<div class="error-message">加载订单时发生错误，请稍后重试</div>';
            });
        }
        
        // 创建订单元素
        function createOrderElement(order) {
            const orderDiv = document.createElement('div');
            orderDiv.className = 'order-item';
            
            // 格式化日期
            const orderDate = new Date(order.date);
            const formattedDate = `${orderDate.getFullYear()}-${(orderDate.getMonth() + 1).toString().padStart(2, '0')}-${orderDate.getDate().toString().padStart(2, '0')} ${orderDate.getHours().toString().padStart(2, '0')}:${orderDate.getMinutes().toString().padStart(2, '0')}`;
            
            // 设置订单内容
            orderDiv.innerHTML = `
                <div class="order-header">
                    <div class="order-id">订单号: ${order.id}</div>
                    <div class="order-status ${getStatusClass(order.status)}">${order.status}</div>
                </div>
                <div class="order-info">
                    <div>下单时间: ${formattedDate}</div>
                    <div>商品数量: ${order.totalItems}件</div>
                </div>
                <div class="order-address">
                    收件人: ${order.shippingInfo.name} | ${order.shippingInfo.phone}
                </div>
                <div class="order-actions">
                    <button class="order-details-btn" onclick="showOrderDetails('${order.id}')">
                        查看详情
                    </button>
                </div>
            `;
            
            return orderDiv;
        }
        
        // 获取状态对应的类名
        function getStatusClass(status) {
            switch (status) {
                case '待处理':
                    return 'pending';
                case '处理中':
                    return 'processing';
                case '已发货':
                    return 'shipped';
                case '已送达':
                    return 'delivered';
                default:
                    return 'pending';
            }
        }
        
        // 显示订单详情
        function showOrderDetails(orderId) {
            // 使用缓存的订单数据
            const orders = window.ordersCache || [];
            
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
                return;
            }
            
            // 确保只显示属于当前用户的订单详情
            let order;
            
            if (isUserTestAccount(loggedInUser.username)) {
                order = orders.find(o => o.id === orderId && (
                    !o.userEmail || (o.username === loggedInUser.username)
                ));
            } else {
                order = orders.find(o => o.id === orderId && 
                    (o.userEmail === loggedInUser.email) && 
                    (o.username === loggedInUser.username)
                );
            }
            
            if (!order) {
                alert('订单不存在');
                return;
            }
            
            const modalBody = document.getElementById('orderModalBody');
            modalBody.innerHTML = '';
            
            // 格式化日期
            const orderDate = new Date(order.date);
            const formattedDate = `${orderDate.getFullYear()}-${(orderDate.getMonth() + 1).toString().padStart(2, '0')}-${orderDate.getDate().toString().padStart(2, '0')} ${orderDate.getHours().toString().padStart(2, '0')}:${orderDate.getMinutes().toString().padStart(2, '0')}`;
            
            // 添加订单基本信息
            const orderInfo = document.createElement('div');
            orderInfo.innerHTML = `
                <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
                    <div>
                        <div style="font-weight: bold; margin-bottom: 5px;">订单号: ${order.id}</div>
                        <div>下单时间: ${formattedDate}</div>
                    </div>
                    <div class="order-status ${getStatusClass(order.status)}" style="align-self: flex-start;">
                        ${order.status}
                    </div>
                </div>
            `;
            modalBody.appendChild(orderInfo);
            
            // 添加订单商品列表
            const itemsTitle = document.createElement('div');
            itemsTitle.className = 'order-items-title';
            itemsTitle.textContent = '订单商品';
            modalBody.appendChild(itemsTitle);
            
            const itemsList = document.createElement('div');
            itemsList.className = 'order-item-list';
            
            order.items.forEach((item, index) => {
                const itemElement = document.createElement('div');
                itemElement.className = 'order-product-item';
                
                // 运输方式文本映射
                const shippingMethodText = {
                    // 空运
                    'air_regular': '空运普货',
                    'air_sensitive': '空运敏货',

                    // 海运
                    'sea_small_regular': '海运小件普货',
                    'sea_small_sensitive': '海运小件敏货',
                    'sea_full_regular': '海运整柜普货',
                    'sea_full_sensitive': '海运整柜敏货',
                    'sea_oversized_regular': '海运超大件普货',
                    'sea_oversized_sensitive': '海运超大件敏货',

                    // 铁运
                    'rail_full_regular': '铁运整柜普货',
                    'rail_full_sensitive': '铁运整柜敏货',
                    'rail_oversized_regular': '铁运超大件普货',
                    'rail_oversized_sensitive': '铁运超大件敏货',

                    // 陆运
                    'land_small_regular': '陆运小件普货',
                    'land_small_sensitive': '陆运小件敏货',
                    'land_full_regular': '陆运整柜普货',
                    'land_full_sensitive': '陆运整柜敏货',
                    'land_oversized_regular': '陆运超大件普货',
                    'land_oversized_sensitive': '陆运超大件敏货',

                    // 兼容旧的简化版本
                    'air': '空运',
                    'sea': '海运'
                };
                
                // 获取图片URL，优先使用新的imageUrl字段，兼容旧的imageData字段
                const imageUrl = item.imageUrl || item.imageData;
                const imageHtml = imageUrl ?
                    `<img src="${imageUrl}" alt="商品图片" class="order-product-img" onerror="this.style.display='none'">` :
                    `<div class="order-product-img-placeholder">暂无图片</div>`;

                itemElement.innerHTML = `
                    ${imageHtml}
                    <div class="order-product-details">
                        <div class="order-product-name">${item.productName}</div>
                        <div class="order-product-meta">
                            <span>物流码: ${item.code}</span>
                            <span>价格: ¥${item.price}</span>
                            <span>运输方式: ${shippingMethodText[item.shippingMethod] || item.shippingMethod}</span>
                        </div>
                    </div>
                `;
                
                itemsList.appendChild(itemElement);
            });
            
            modalBody.appendChild(itemsList);
            
            // 添加收货信息
            const shippingTitle = document.createElement('div');
            shippingTitle.className = 'order-items-title';
            shippingTitle.textContent = '收货信息';
            modalBody.appendChild(shippingTitle);
            
            const shippingInfo = document.createElement('div');
            shippingInfo.className = 'order-shipping-info';
            shippingInfo.innerHTML = `
                <div style="margin-bottom: 10px;">
                    <strong>收件人: </strong>${order.shippingInfo.name}
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>联系电话: </strong>${order.shippingInfo.phone}
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>收货地址: </strong>${order.shippingInfo.country} ${order.shippingInfo.city} ${order.shippingInfo.address}
                </div>
                <div>
                    <strong>邮政编码: </strong>${order.shippingInfo.zipCode}
                </div>
            `;
            modalBody.appendChild(shippingInfo);
            
            // 显示弹窗
            document.getElementById('orderModal').style.display = 'flex';
        }
        
        // 关闭订单详情
        function closeOrderModal() {
            document.getElementById('orderModal').style.display = 'none';
        }
        
        // 额外的每日订单限制检查函数（用于确保按钮状态正确）
        function checkOrderButtonState() {
            // 如果没有加载order-limit.js，这里提供一个简单的实现
            if (typeof getDailyOrderCount !== 'function') {
                // 获取当前日期（年-月-日格式）
                const today = new Date().toISOString().split('T')[0];
                
                // 获取当前登录用户
                let username = null;
                try {
                    const userDataStr = sessionStorage.getItem('loggedInUser');
                    if (userDataStr) {
                        const userData = JSON.parse(userDataStr);
                        username = userData.username;
                    }
                } catch (error) {
                    console.warn('Failed to get user data:', error);
                    return;
                }
                
                if (!username) return;
                
                // 获取所有订单
                const orders = JSON.parse(localStorage.getItem('orders') || '[]');
                
                // 过滤出当前用户今天的订单
                const userTodayOrders = orders.filter(order => {
                    // 检查用户名
                    if (order.username !== username) return false;
                    
                    // 检查日期
                    const orderDate = new Date(order.date).toISOString().split('T')[0];
                    return orderDate === today;
                });
                
                const dailyOrderCount = userTodayOrders.length;
                
                // 获取下单按钮
                const orderButton = document.getElementById('orderButton');
                if (!orderButton) return;
                
                // 如果超过限制，修改按钮行为
                if (dailyOrderCount >= 20) {
                    // 修改按钮样式
                    orderButton.style.backgroundColor = '#cccccc';
                    orderButton.style.cursor = 'not-allowed';
                    
                    // 修改按钮点击行为
                    orderButton.href = 'javascript:void(0)';
                    orderButton.onclick = function(e) {
                        e.preventDefault();
                        alert('您今日已达到下单上限(20单)，请明日再来下单。');
                        return false;
                    };
                    
                                         // 添加文本提示
                     const orderCard = document.querySelector('.dashboard-card:nth-child(2)');
                    if (orderCard) {
                        let limitNotice = document.getElementById('orderLimitNotice');
                        if (!limitNotice) {
                            limitNotice = document.createElement('div');
                            limitNotice.id = 'orderLimitNotice';
                            limitNotice.style.color = '#dc3545';
                            limitNotice.style.backgroundColor = '#f8d7da';
                            limitNotice.style.border = '1px solid #f5c6cb';
                            limitNotice.style.borderRadius = '4px';
                            limitNotice.style.padding = '10px';
                            limitNotice.style.marginTop = '10px';
                            limitNotice.style.textAlign = 'center';
                            limitNotice.style.fontSize = '14px';
                            limitNotice.innerHTML = '<i class="fas fa-exclamation-circle"></i> 您今日已达到下单上限(20单)，请明日再来下单。';
                            
                            // 插入到按钮前面
                            orderButton.parentNode.insertBefore(limitNotice, orderButton);
                        }
                    }
                }
            } else {
                // 如果已经加载了order-limit.js，直接调用其函数
                checkDailyOrderLimit();
            }
        }
        
        // 页面加载完毕后检查按钮状态
        window.addEventListener('load', function() {
            setTimeout(checkOrderButtonState, 800);
        });
    </script>
</body>
</html> 