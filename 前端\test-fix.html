<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试修复 - 关键词筛选</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .test-container {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button.warning {
            background: #ff9800;
        }
        .test-button.danger {
            background: #f44336;
        }
        h1, h2 {
            color: #fff;
        }
        .note {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 关键词筛选修复测试</h1>
    
    <div class="note">
        <strong>测试说明：</strong>
        <br>• 点击下面的按钮测试不同的关键词筛选情况
        <br>• 第一次点击应该立即显示正确的结果
        <br>• 绿色按钮：有商品的关键词
        <br>• 橙色按钮：无商品的关键词  
        <br>• 红色按钮：不存在的关键词
    </div>

    <div class="test-container">
        <h2>✅ 有商品的关键词测试</h2>
        <button class="test-button" onclick="testKeyword('红木家具')">测试"红木家具"</button>
        <button class="test-button" onclick="testKeyword('实木家具')">测试"实木家具"</button>
        <button class="test-button" onclick="testKeyword('蓝色家具')">测试"蓝色家具"</button>
    </div>

    <div class="test-container">
        <h2>⚠️ 无商品的关键词测试</h2>
        <button class="test-button warning" onclick="testKeyword('儿童家具')">测试"儿童家具"</button>
        <button class="test-button warning" onclick="testKeyword('紫色家具')">测试"紫色家具"</button>
    </div>

    <div class="test-container">
        <h2>❌ 不存在的关键词测试</h2>
        <button class="test-button danger" onclick="testKeyword('不存在的商品')">测试"不存在的商品"</button>
        <button class="test-button danger" onclick="testKeyword('随机关键词')">测试"随机关键词"</button>
    </div>

    <div class="test-container">
        <h2>🔄 分类测试</h2>
        <button class="test-button" onclick="testCategory('1753523684066', '家具')">测试"家具"分类(有商品)</button>
        <button class="test-button warning" onclick="testCategory('1753525037752', '家1')">测试"家1"分类(无商品)</button>
    </div>

    <div class="test-container">
        <h2>🏠 返回测试</h2>
        <button class="test-button" onclick="goToCategories()">返回分类页面</button>
        <button class="test-button" onclick="goToRecommend()">返回推荐页面(无参数)</button>
    </div>

    <script>
        function testKeyword(keyword) {
            const url = `/前端/recommend.html?search=${encodeURIComponent(keyword)}`;
            console.log('测试关键词:', keyword, '-> URL:', url);
            window.open(url, '_blank');
        }

        function testCategory(categoryId, categoryName) {
            const url = `/前端/recommend.html?category=${categoryId}&categoryName=${encodeURIComponent(categoryName)}`;
            console.log('测试分类:', categoryName, '-> URL:', url);
            window.open(url, '_blank');
        }

        function goToCategories() {
            window.open('/前端/product-categories-display.html', '_blank');
        }

        function goToRecommend() {
            window.open('/前端/recommend.html', '_blank');
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('测试页面已加载，请点击按钮测试各种情况');
        });
    </script>
</body>
</html>
