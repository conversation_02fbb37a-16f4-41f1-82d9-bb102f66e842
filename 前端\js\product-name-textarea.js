/**
 * 商品名称输入框自动调整大小
 * 此脚本替换商品名称输入框为可自动调整高度的textarea
 */
document.addEventListener('DOMContentLoaded', function() {
    // 等待DOM完全加载
    setTimeout(replaceProductNameInputs, 1000);
    
    // 监听DOM变化，以便在添加新的物流码对时替换产品名称输入框
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                replaceProductNameInputs();
            }
        });
    });
    
    // 开始观察DOM变化
    observer.observe(document.body, { childList: true, subtree: true });
});

/**
 * 执行替换所有商品名称输入框的操作
 */
function replaceProductNameInputs() {
    // 查找所有商品名称输入框
    const productNameInputs = document.querySelectorAll('input.product-name');
    
    productNameInputs.forEach(function(input) {
        // 保存输入框的值和属性
        const value = input.value;
        const placeholder = input.placeholder;
        const required = input.required;
        const className = input.className;
        const style = input.style.cssText;
        
        // 创建textarea元素
        const textarea = document.createElement('textarea');
        textarea.value = value;
        textarea.placeholder = placeholder;
        textarea.required = required;
        textarea.className = className;
        textarea.style.cssText = style;
        
        // 设置textarea的特定样式
        textarea.style.minHeight = '42px';
        textarea.style.maxHeight = '150px';
        textarea.style.height = '42px';
        textarea.style.resize = 'vertical';
        textarea.style.overflowY = 'hidden';
        
        // 添加自动调整高度功能
        textarea.addEventListener('input', function() {
            // 重置高度以获取正确的scrollHeight
            this.style.height = 'auto';
            // 设置新高度
            this.style.height = Math.min(this.scrollHeight, 150) + 'px';
            // 调用原始的保存函数
            saveLogisticsData();
        });
        
        // 聚焦时也调整高度
        textarea.addEventListener('focus', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 150) + 'px';
        });
        
        // 替换原始的输入框
        input.parentNode.replaceChild(textarea, input);
    });
}

// 在还原数据后自动调整高度
function adjustTextareaHeights() {
    const textareas = document.querySelectorAll('textarea.product-name');
    textareas.forEach(function(textarea) {
        // 重置高度以获取正确的scrollHeight
        textarea.style.height = 'auto';
        // 设置新高度
        textarea.style.height = Math.min(textarea.scrollHeight, 150) + 'px';
    });
}

// 监听restoreLogisticsData函数完成
document.addEventListener('logistics-data-restored', adjustTextareaHeights);

// 覆盖原始的restoreLogisticsData函数以添加事件触发
const originalRestoreLogisticsData = window.restoreLogisticsData;
window.restoreLogisticsData = function() {
    if (originalRestoreLogisticsData) {
        originalRestoreLogisticsData.apply(this, arguments);
    }
    // 等待DOM更新
    setTimeout(function() {
        // 触发自定义事件
        const event = new Event('logistics-data-restored');
        document.dispatchEvent(event);
        
        // 直接调用调整函数
        adjustTextareaHeights();
    }, 300);
}; 