document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('nav a');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href');

            // 只有当href是锚点链接时才阻止默认行为
            if (targetId && targetId.startsWith('#')) {
                e.preventDefault();
                const targetSection = document.querySelector(targetId);

                if (targetSection) {
                    const offsetTop = targetSection.offsetTop;

                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            }
        });
    });

    // Form submission handling
    const contactForm = document.querySelector('.contact-form form');

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Basic form validation
            const inputs = contactForm.querySelectorAll('input, textarea');
            let isValid = true;
            let errorMessages = [];

            inputs.forEach(input => {
                // 清除之前的错误状态
                input.classList.remove('error');

                // 检查必填字段
                if (input.hasAttribute('required') && !input.value.trim()) {
                    isValid = false;
                    input.classList.add('error');
                    errorMessages.push(`${input.previousElementSibling?.textContent || '字段'} 不能为空`);
                }

                // 验证邮箱格式
                if (input.type === 'email' && input.value.trim()) {
                    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailPattern.test(input.value.trim())) {
                        isValid = false;
                        input.classList.add('error');
                        errorMessages.push('邮箱格式不正确');
                    }
                }
            });

            if (isValid) {
                // 显示成功消息
                alert('消息已发送！我们会尽快回复您。');
                contactForm.reset();
            } else {
                // 显示具体的错误信息
                alert('请修正以下错误：\n' + errorMessages.join('\n'));
            }
        });
    }

    // Header scroll effect
    let ticking = false;

    function updateHeader() {
        const scrollPosition = window.scrollY;
        const header = document.querySelector('header');

        if (header) {
            if (scrollPosition > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        }
        ticking = false;
    }

    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(updateHeader);
            ticking = true;
        }
    });
});