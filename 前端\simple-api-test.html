<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱验证API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f7fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #0c4da2; text-align: center; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 8px; margin-bottom: 10px; border: 1px solid #ddd; border-radius: 3px; }
        button { background: #0c4da2; color: white; border: none; padding: 8px 15px; border-radius: 3px; cursor: pointer; }
        button:hover { background: #083778; }
        button:disabled { background: #cccccc; cursor: not-allowed; }
        .result { margin-top: 10px; padding: 10px; background: #eee; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
        .success { border-left: 4px solid #4CAF50; }
        .error { border-left: 4px solid #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <h1>邮箱验证API测试</h1>
        
        <div class="section">
            <h2>服务器信息</h2>
            <p>请确保后端服务器已启动 (<code>start-no-compile.bat</code>)</p>
            <p>API地址: <code id="apiUrl">http://localhost:8000</code></p>
        </div>
        
        <div class="section">
            <h2>1. 发送验证码</h2>
            <label for="sendEmail">邮箱地址：</label>
            <input type="email" id="sendEmail" placeholder="请输入邮箱地址">
            <button id="sendCodeBtn">发送验证码</button>
            <div id="sendResult" class="result"></div>
        </div>
        
        <div class="section">
            <h2>2. 验证验证码</h2>
            <label for="verifyEmail">邮箱地址：</label>
            <input type="email" id="verifyEmail" placeholder="请输入邮箱地址">
            <label for="verifyCode">验证码：</label>
            <input type="text" id="verifyCode" placeholder="请输入收到的验证码">
            <button id="verifyCodeBtn">验证</button>
            <div id="verifyResult" class="result"></div>
        </div>
    </div>
    
    <script>
        // API基本地址
        const API_BASE_URL = "http://*************:8080";
        
        // 发送验证码
        document.getElementById("sendCodeBtn").addEventListener("click", async function() {
            const email = document.getElementById("sendEmail").value.trim();
            const resultElement = document.getElementById("sendResult");
            
            if (!email) {
                resultElement.textContent = "请输入邮箱地址";
                resultElement.className = "result error";
                return;
            }
            
            this.disabled = true;
            this.textContent = "发送中...";
            resultElement.textContent = "请求中，请稍候...";
            resultElement.className = "result";
            
            try {
                const response = await fetch(`${API_BASE_URL}/send?email=${encodeURIComponent(email)}`);
                const result = await response.text();
                resultElement.textContent = result;
                
                if (result.includes("已发送")) {
                    resultElement.className = "result success";
                } else {
                    resultElement.className = "result error";
                }
            } catch (error) {
                resultElement.textContent = `请求失败: ${error.message}\n\n请确保后端服务已启动`;
                resultElement.className = "result error";
            }
            
            this.disabled = false;
            this.textContent = "发送验证码";
        });
        
        // 验证验证码
        document.getElementById("verifyCodeBtn").addEventListener("click", async function() {
            const email = document.getElementById("verifyEmail").value.trim();
            const code = document.getElementById("verifyCode").value.trim();
            const resultElement = document.getElementById("verifyResult");
            
            if (!email || !code) {
                resultElement.textContent = "请输入邮箱地址和验证码";
                resultElement.className = "result error";
                return;
            }
            
            this.disabled = true;
            this.textContent = "验证中...";
            resultElement.textContent = "请求中，请稍候...";
            resultElement.className = "result";
            
            try {
                const response = await fetch(`${API_BASE_URL}/verify?email=${encodeURIComponent(email)}&code=${encodeURIComponent(code)}`);
                const result = await response.text();
                resultElement.textContent = result;
                
                if (result.includes("验证成功")) {
                    resultElement.className = "result success";
                } else {
                    resultElement.className = "result error";
                }
            } catch (error) {
                resultElement.textContent = `请求失败: ${error.message}\n\n请确保后端服务已启动`;
                resultElement.className = "result error";
            }
            
            this.disabled = false;
            this.textContent = "验证";
        });
    </script>
</body>
</html> 