#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的HTTP服务器，用于测试前端页面
"""

import http.server
import socketserver
import json
import urllib.parse
import os
from datetime import datetime

class CarouselHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/api/carousels':
            self.send_carousel_response()
        elif self.path.startswith('/api/carousels/'):
            carousel_id = self.path.split('/')[-1]
            self.send_single_carousel_response(carousel_id)
        else:
            super().do_GET()
    
    def send_carousel_response(self):
        # 返回模拟的轮播图数据
        carousels = [
            {
                "id": "1",
                "title": "夏日清新",
                "subtitle": "轻盈夏日穿搭",
                "description": "精选女装 火热开售",
                "image": None,
                "products": [],
                "createdAt": datetime.now().isoformat()
            },
            {
                "id": "2", 
                "title": "数码狂欢",
                "subtitle": "24期免息",
                "description": "iPad、耳机、手机全场优惠",
                "image": None,
                "products": [],
                "createdAt": datetime.now().isoformat()
            },
            {
                "id": "3",
                "title": "居家好物", 
                "subtitle": "宅家刷剧好伴侣",
                "description": "休闲零食 全网低价 限时直降",
                "image": None,
                "products": [],
                "createdAt": datetime.now().isoformat()
            }
        ]
        
        response_data = {
            "success": True,
            "carousels": carousels
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))
    
    def send_single_carousel_response(self, carousel_id):
        # 返回单个轮播图数据
        carousel = {
            "id": carousel_id,
            "title": f"轮播图 {carousel_id}",
            "subtitle": "测试副标题",
            "description": "测试描述",
            "image": None,
            "products": [],
            "createdAt": datetime.now().isoformat()
        }
        
        response_data = {
            "success": True,
            "carousel": carousel
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))

if __name__ == "__main__":
    PORT = 8080
    
    with socketserver.TCPServer(("", PORT), CarouselHandler) as httpd:
        print(f"服务器启动在端口 {PORT}")
        print(f"访问 http://localhost:{PORT}/recommend.html 来测试")
        httpd.serve_forever()
