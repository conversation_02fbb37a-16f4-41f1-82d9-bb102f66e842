<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品分类 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background-color: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 28px;
            display: flex;
            align-items: center;
            color: #0c4da2;
        }
        
        .header h1 i {
            margin-right: 12px;
            color: #667eea;
        }
        
        .back-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .back-button:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }
        
        .category-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .category-name {
            font-size: 20px;
            font-weight: bold;
            color: #0c4da2;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .category-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .keywords-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 15px;
        }
        
        .keyword-tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .keyword-tag:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        /* 关键词标签颜色类 - 简洁清晰版本 */
        .keyword-tag.color-1 {
            background: #667eea !important;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.25) !important;
        }

        .keyword-tag.color-2 {
            background: #e74c3c !important;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.25) !important;
        }

        .keyword-tag.color-3 {
            background: #3498db !important;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.25) !important;
        }

        .keyword-tag.color-4 {
            background: #2ecc71 !important;
            box-shadow: 0 2px 8px rgba(46, 204, 113, 0.25) !important;
        }

        .keyword-tag.color-5 {
            background: #f39c12 !important;
            box-shadow: 0 2px 8px rgba(243, 156, 18, 0.25) !important;
        }

        .keyword-tag.color-6 {
            background: #9b59b6 !important;
            box-shadow: 0 2px 8px rgba(155, 89, 182, 0.25) !important;
        }

        .keyword-tag.color-7 {
            background: #1abc9c !important;
            box-shadow: 0 2px 8px rgba(26, 188, 156, 0.25) !important;
        }

        .keyword-tag.color-8 {
            background: #34495e !important;
            box-shadow: 0 2px 8px rgba(52, 73, 94, 0.25) !important;
        }

        .keyword-tag.color-9 {
            background: #e67e22 !important;
            box-shadow: 0 2px 8px rgba(230, 126, 34, 0.25) !important;
        }

        .keyword-tag.color-10 {
            background: #95a5a6 !important;
            box-shadow: 0 2px 8px rgba(149, 165, 166, 0.25) !important;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .empty-state i {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.6;
        }
        
        .empty-state h3 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .empty-state p {
            font-size: 16px;
            opacity: 0.8;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .loading i {
            font-size: 32px;
            margin-bottom: 15px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .categories-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .category-card {
                padding: 20px;
            }
            
            .category-name {
                font-size: 18px;
            }
        }
        
        @media (max-width: 480px) {
            .header h1 {
                font-size: 20px;
            }
            
            .category-card {
                padding: 15px;
            }
            
            .category-name {
                font-size: 16px;
            }
            
            .back-button {
                padding: 10px 16px;
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-list"></i>
                商品分类
            </h1>
            <a href="recommend.html" class="back-button">
                <i class="fas fa-arrow-left"></i>
                返回商品页面
            </a>
        </div>
        
        <div id="categoriesContainer">
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                <p>正在加载分类数据...</p>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadCategories();
        });
        
        // 加载分类数据
        function loadCategories() {
            const container = document.getElementById('categoriesContainer');
            
            fetch('/api/categories')
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        const categories = result.categories || [];
                        renderCategories(categories);
                    } else {
                        showEmptyState('加载分类数据失败');
                    }
                })
                .catch(error => {
                    console.error('加载分类失败:', error);
                    showEmptyState('加载分类数据失败');
                });
        }
        
        // 渲染分类列表
        function renderCategories(categories) {
            const container = document.getElementById('categoriesContainer');
            
            if (categories.length === 0) {
                showEmptyState('暂无分类数据');
                return;
            }
            
            const categoriesHTML = `
                <div class="categories-grid">
                    ${categories.map(category => `
                        <div class="category-card" onclick="selectCategory('${category.id}', '${category.name}')">
                            <div class="category-name">
                                <div class="category-icon">
                                    <i class="fas fa-tag"></i>
                                </div>
                                ${category.name}
                            </div>
                            <div class="keywords-container">
                                ${category.keywords.map((keyword, index) => `
                                    <span class="keyword-tag color-${(index % 10) + 1}" onclick="event.stopPropagation(); searchKeyword('${keyword.text}')">${keyword.text}</span>
                                `).join('')}
                                ${category.keywords.length === 0 ? '<span style="color: #999; font-style: italic;">暂无关键词</span>' : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            
            container.innerHTML = categoriesHTML;
        }
        
        // 显示空状态
        function showEmptyState(message) {
            const container = document.getElementById('categoriesContainer');
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-list"></i>
                    <h3>${message}</h3>
                    <p>请稍后再试或联系管理员</p>
                </div>
            `;
        }
        
        // 选择分类
        function selectCategory(categoryId, categoryName) {
            // 跳转到商品页面并传递分类信息
            const url = new URL('recommend.html', window.location.origin);
            url.searchParams.set('category', categoryId);
            url.searchParams.set('categoryName', categoryName);
            window.location.href = url.toString();
        }
        
        // 搜索关键词
        function searchKeyword(keyword) {
            // 跳转到商品页面并传递搜索关键词
            const url = new URL('recommend.html', window.location.origin);
            url.searchParams.set('search', keyword);
            window.location.href = url.toString();
        }
    </script>
</body>
</html>
