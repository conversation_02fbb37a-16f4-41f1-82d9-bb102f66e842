<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品分类管理 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background-color: #0c4da2;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 24px;
            display: flex;
            align-items: center;
        }
        
        .header h1 i {
            margin-right: 10px;
        }
        
        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .admin-info {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255,255,255,0.1);
            padding: 8px 15px;
            border-radius: 20px;
            color: white;
        }

        .admin-avatar {
            width: 32px;
            height: 32px;
            background: #ffd700;
            color: #0c4da2;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .logout-btn {
            color: #ffcccb;
            text-decoration: none;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.3s ease;
            opacity: 0.8;
        }

        .logout-btn:hover {
            opacity: 1;
            background: rgba(255,255,255,0.1);
            color: #ff6b6b;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #28a745;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #218838;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .card h2 {
            color: #0c4da2;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #0c4da2;
            box-shadow: 0 0 0 3px rgba(12, 77, 162, 0.1);
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .table tr:hover {
            background-color: #f8f9fa;
        }
        
        .keyword-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .keyword-tag {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.25);
            transition: all 0.3s ease;
        }

        /* 关键词标签颜色类 - 简洁清晰版本 */
        .keyword-tag.color-1 {
            background: #667eea !important;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.25) !important;
        }

        .keyword-tag.color-2 {
            background: #e74c3c !important;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.25) !important;
        }

        .keyword-tag.color-3 {
            background: #3498db !important;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.25) !important;
        }

        .keyword-tag.color-4 {
            background: #2ecc71 !important;
            box-shadow: 0 2px 8px rgba(46, 204, 113, 0.25) !important;
        }

        .keyword-tag.color-5 {
            background: #f39c12 !important;
            box-shadow: 0 2px 8px rgba(243, 156, 18, 0.25) !important;
        }

        .keyword-tag.color-6 {
            background: #9b59b6 !important;
            box-shadow: 0 2px 8px rgba(155, 89, 182, 0.25) !important;
        }

        .keyword-tag.color-7 {
            background: #1abc9c !important;
            box-shadow: 0 2px 8px rgba(26, 188, 156, 0.25) !important;
        }

        .keyword-tag.color-8 {
            background: #34495e !important;
            box-shadow: 0 2px 8px rgba(52, 73, 94, 0.25) !important;
        }

        .keyword-tag.color-9 {
            background: #e67e22 !important;
            box-shadow: 0 2px 8px rgba(230, 126, 34, 0.25) !important;
        }

        .keyword-tag.color-10 {
            background: #95a5a6 !important;
            box-shadow: 0 2px 8px rgba(149, 165, 166, 0.25) !important;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #ddd;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .alert {
            padding: 12px 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
        
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-header h3 {
            color: #0c4da2;
            margin: 0;
        }
        
        .close-modal {
            font-size: 24px;
            font-weight: bold;
            color: #888;
            cursor: pointer;
            border: none;
            background: none;
        }
        
        .close-modal:hover {
            color: #333;
        }
        
        .keyword-search-container {
            position: relative;
        }
        
        .keyword-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #667eea;
            border-top: none;
            border-radius: 0 0 8px 8px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }
        
        .keyword-dropdown.show {
            display: block;
        }
        
        .keyword-item {
            padding: 12px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.2s ease;
        }
        
        .keyword-item:hover {
            background-color: #f8f9fa;
        }
        
        .keyword-item.disabled {
            color: #999;
            cursor: not-allowed;
            background-color: #f5f5f5;
        }
        
        .selected-keywords {
            margin-top: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            min-height: 40px;
            padding: 10px;
            border: 1px dashed #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }
        
        .selected-keywords:empty::before {
            content: "暂未选择关键词";
            color: #999;
            font-style: italic;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 20px;
        }
        
        .selected-keyword-tag {
            background: #667eea;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.25);
            transition: all 0.3s ease;
        }

        /* 选中关键词标签颜色类 - 简洁清晰版本 */
        .selected-keyword-tag.color-1 {
            background: #667eea !important;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.25) !important;
        }

        .selected-keyword-tag.color-2 {
            background: #e74c3c !important;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.25) !important;
        }

        .selected-keyword-tag.color-3 {
            background: #3498db !important;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.25) !important;
        }

        .selected-keyword-tag.color-4 {
            background: #2ecc71 !important;
            box-shadow: 0 2px 8px rgba(46, 204, 113, 0.25) !important;
        }

        .selected-keyword-tag.color-5 {
            background: #f39c12 !important;
            box-shadow: 0 2px 8px rgba(243, 156, 18, 0.25) !important;
        }

        .selected-keyword-tag.color-6 {
            background: #9b59b6 !important;
            box-shadow: 0 2px 8px rgba(155, 89, 182, 0.25) !important;
        }

        .selected-keyword-tag.color-7 {
            background: #1abc9c !important;
            box-shadow: 0 2px 8px rgba(26, 188, 156, 0.25) !important;
        }

        .selected-keyword-tag.color-8 {
            background: #34495e !important;
            box-shadow: 0 2px 8px rgba(52, 73, 94, 0.25) !important;
        }

        .selected-keyword-tag.color-9 {
            background: #e67e22 !important;
            box-shadow: 0 2px 8px rgba(230, 126, 34, 0.25) !important;
        }

        .selected-keyword-tag.color-10 {
            background: #95a5a6 !important;
            box-shadow: 0 2px 8px rgba(149, 165, 166, 0.25) !important;
        }

        .selected-keyword-tag.color-7 {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important;
            box-shadow: 0 2px 8px rgba(255, 154, 158, 0.3) !important;
        }

        .selected-keyword-tag.color-8 {
            background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%) !important;
            box-shadow: 0 2px 8px rgba(161, 140, 209, 0.3) !important;
        }

        .selected-keyword-tag.color-9 {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%) !important;
            box-shadow: 0 2px 8px rgba(255, 236, 210, 0.3) !important;
        }

        .selected-keyword-tag.color-10 {
            background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%) !important;
            box-shadow: 0 2px 8px rgba(137, 247, 254, 0.3) !important;
        }
        
        .remove-keyword {
            cursor: pointer;
            font-weight: bold;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .remove-keyword:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-list"></i>
                商品分类管理
            </h1>
            <div class="header-actions">
                <div class="admin-info">
                    <div class="admin-avatar" id="adminInitial">A</div>
                    <span id="adminName">管理员</span>
                    <a href="#" id="logoutBtn" class="logout-btn" title="退出登录">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
                <button class="btn btn-primary" id="addCategoryBtn">
                    <i class="fas fa-plus"></i>
                    添加分类
                </button>
                <a href="admin-dashboard.html" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回控制台
                </a>
            </div>
        </div>
        
        <div class="alert alert-success" id="successAlert"></div>
        <div class="alert alert-error" id="errorAlert"></div>
        
        <div class="card">
            <h2>分类列表</h2>
            <div id="categoriesContainer">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    正在加载分类数据...
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加/编辑分类模态框 -->
    <div class="modal" id="categoryModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">添加分类</h3>
                <button class="close-modal" onclick="closeCategoryModal()">&times;</button>
            </div>
            <form id="categoryForm">
                <div class="form-group">
                    <label for="categoryName">分类名称 *</label>
                    <input type="text" id="categoryName" class="form-control" placeholder="请输入分类名称" required>
                </div>
                <div class="form-group">
                    <label for="keywordSearch">关键词 <small>(可选择多个)</small></label>
                    <div class="keyword-search-container">
                        <input type="text" id="keywordSearch" class="form-control" placeholder="搜索关键词..." autocomplete="off">
                        <div class="keyword-dropdown" id="keywordDropdown"></div>
                    </div>
                    <div class="selected-keywords" id="selectedKeywords"></div>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeCategoryModal()">取消</button>
                    <button type="submit" class="btn btn-primary" style="margin-left: 10px;">
                        <span id="submitBtnText">添加分类</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        // 全局变量
        let allKeywords = [];
        let selectedKeywords = [];
        let categories = [];
        let editingCategoryId = null;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化页面
            initializePage();
        });
        
        // 检查管理员登录状态
        function checkAdminAuth() {
            // 首先检查URL参数，看是否从admin-dashboard跳转过来
            const urlParams = new URLSearchParams(window.location.search);
            const fromDashboard = urlParams.get('from') === 'dashboard';

            // 检查sessionStorage中的登录状态（与keywords-management.html保持一致）
            const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');

            if (!adminData.isAdmin) {
                // 未登录或不是管理员，直接重定向到登录页（无提示框）
                window.location.href = 'admin-login.html';
                return null;
            }

            // 如果从dashboard跳转过来，清除URL参数
            if (fromDashboard && window.history.replaceState) {
                window.history.replaceState({}, document.title, window.location.pathname);
            }

            return adminData;
        }

        // 设置管理员信息
        function setupAdminInfo(adminData) {
            // 设置管理员头像和姓名
            const adminInitial = document.getElementById('adminInitial');
            const adminName = document.getElementById('adminName');

            if (adminData.username) {
                if (adminName) adminName.textContent = adminData.username;
                if (adminInitial) adminInitial.textContent = adminData.username.charAt(0).toUpperCase();
            }

            // 设置退出登录功能
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    sessionStorage.removeItem('loggedInAdmin');
                    sessionStorage.removeItem('welcomeShown');
                    window.location.href = 'admin-login.html';
                });
            }
        }

        // 初始化页面
        function initializePage() {
            // 检查管理员登录状态
            const adminData = checkAdminAuth();
            if (!adminData) {
                // 如果没有登录数据，页面将重定向，不继续初始化
                return;
            }

            // 设置管理员信息显示
            setupAdminInfo(adminData);

            loadKeywords();
            loadCategories();
            setupEventListeners();
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            // 添加分类按钮
            document.getElementById('addCategoryBtn').addEventListener('click', openAddCategoryModal);
            
            // 分类表单提交
            document.getElementById('categoryForm').addEventListener('submit', handleCategorySubmit);
            
            // 关键词搜索
            setupKeywordSearch();
        }

        // 加载关键词数据
        function loadKeywords() {
            fetch('/api/keywords')
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        allKeywords = result.keywords || [];
                    } else {
                        console.error('加载关键词失败:', result.message);
                    }
                })
                .catch(error => {
                    console.error('加载关键词失败:', error);
                });
        }

        // 加载分类数据
        function loadCategories() {
            const container = document.getElementById('categoriesContainer');
            container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> 正在加载分类数据...</div>';

            fetch('/api/categories')
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        categories = result.categories || [];
                        renderCategories();
                    } else {
                        container.innerHTML = '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><p>加载分类数据失败</p></div>';
                        showAlert('加载分类数据失败: ' + result.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('加载分类失败:', error);
                    container.innerHTML = '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><p>加载分类数据失败</p></div>';
                    showAlert('加载分类数据失败', 'error');
                });
        }

        // 渲染分类列表
        function renderCategories() {
            const container = document.getElementById('categoriesContainer');

            if (categories.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-list"></i>
                        <p>暂无分类数据</p>
                        <p style="font-size: 14px; color: #999;">点击上方"添加分类"按钮创建第一个分类</p>
                    </div>
                `;
                return;
            }

            const tableHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>分类名称</th>
                            <th>关键词</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${categories.map(category => `
                            <tr>
                                <td><strong>${category.name}</strong></td>
                                <td>
                                    <div class="keyword-tags">
                                        ${category.keywords.map((keyword, index) => {
                                            const colorClass = `color-${(index % 10) + 1}`;
                                            return `<span class="keyword-tag ${colorClass}">${keyword.text}</span>`;
                                        }).join('')}
                                        ${category.keywords.length === 0 ? '<span style="color: #999; font-style: italic;">无关键词</span>' : ''}
                                    </div>
                                </td>
                                <td>${new Date(category.createdAt).toLocaleString('zh-CN')}</td>
                                <td>
                                    <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px; margin-right: 5px;" onclick="editCategory('${category.id}')">
                                        <i class="fas fa-edit"></i> 编辑
                                    </button>
                                    <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="deleteCategory('${category.id}', '${category.name}')">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHTML;
        }

        // 打开添加分类模态框
        function openAddCategoryModal() {
            editingCategoryId = null;
            document.getElementById('modalTitle').textContent = '添加分类';
            document.getElementById('submitBtnText').textContent = '添加分类';
            document.getElementById('categoryName').value = '';
            selectedKeywords = [];
            updateSelectedKeywordsDisplay();
            document.getElementById('categoryModal').style.display = 'flex';
        }

        // 编辑分类
        function editCategory(categoryId) {
            const category = categories.find(c => c.id === categoryId);
            if (!category) {
                showAlert('分类不存在', 'error');
                return;
            }

            editingCategoryId = categoryId;
            document.getElementById('modalTitle').textContent = '编辑分类';
            document.getElementById('submitBtnText').textContent = '更新分类';
            document.getElementById('categoryName').value = category.name;
            selectedKeywords = [...category.keywords];
            updateSelectedKeywordsDisplay();
            document.getElementById('categoryModal').style.display = 'flex';
        }

        // 删除分类
        function deleteCategory(categoryId, categoryName) {
            if (!confirm(`确定要删除分类"${categoryName}"吗？此操作不可撤销。`)) {
                return;
            }

            fetch(`/api/categories/${categoryId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showAlert('分类删除成功', 'success');
                    loadCategories();
                } else {
                    showAlert('删除分类失败: ' + result.message, 'error');
                }
            })
            .catch(error => {
                console.error('删除分类失败:', error);
                showAlert('删除分类失败', 'error');
            });
        }

        // 关闭分类模态框
        function closeCategoryModal() {
            document.getElementById('categoryModal').style.display = 'none';
            editingCategoryId = null;
            selectedKeywords = [];
        }

        // 处理分类表单提交
        function handleCategorySubmit(e) {
            e.preventDefault();

            const name = document.getElementById('categoryName').value.trim();
            if (!name) {
                showAlert('请输入分类名称', 'error');
                return;
            }

            const categoryData = {
                name: name,
                keywords: selectedKeywords
            };

            const url = editingCategoryId ? `/api/categories/${editingCategoryId}` : '/api/categories';
            const method = editingCategoryId ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(categoryData)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    const action = editingCategoryId ? '更新' : '添加';
                    showAlert(`分类${action}成功`, 'success');
                    closeCategoryModal();
                    loadCategories();
                } else {
                    showAlert(`${editingCategoryId ? '更新' : '添加'}分类失败: ` + result.message, 'error');
                }
            })
            .catch(error => {
                console.error('提交分类失败:', error);
                showAlert(`${editingCategoryId ? '更新' : '添加'}分类失败`, 'error');
            });
        }

        // 设置关键词搜索功能
        function setupKeywordSearch() {
            const keywordSearch = document.getElementById('keywordSearch');
            const keywordDropdown = document.getElementById('keywordDropdown');

            // 搜索输入事件
            keywordSearch.addEventListener('input', (e) => {
                const searchTerm = e.target.value.trim().toLowerCase();
                filterAndShowKeywords(searchTerm);
            });

            // 点击外部关闭下拉框
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.keyword-search-container')) {
                    keywordDropdown.classList.remove('show');
                }
            });

            // 获得焦点时显示下拉框
            keywordSearch.addEventListener('focus', () => {
                if (keywordSearch.value.trim()) {
                    filterAndShowKeywords(keywordSearch.value.trim().toLowerCase());
                }
            });
        }

        // 过滤并显示关键词
        function filterAndShowKeywords(searchTerm) {
            const keywordDropdown = document.getElementById('keywordDropdown');

            if (!searchTerm) {
                keywordDropdown.classList.remove('show');
                return;
            }

            // 过滤关键词
            const filteredKeywords = allKeywords.filter(keyword =>
                keyword.text.toLowerCase().includes(searchTerm) &&
                !selectedKeywords.some(selected => selected.id === keyword.id)
            );

            // 显示下拉框
            if (filteredKeywords.length > 0) {
                keywordDropdown.innerHTML = filteredKeywords.map(keyword => `
                    <div class="keyword-item" onclick="selectKeyword('${keyword.id}', '${keyword.text}')">
                        ${keyword.text}
                    </div>
                `).join('');
                keywordDropdown.classList.add('show');
            } else {
                keywordDropdown.innerHTML = '<div class="keyword-item disabled">未找到匹配的关键词</div>';
                keywordDropdown.classList.add('show');
            }
        }

        // 选择关键词
        function selectKeyword(keywordId, keywordText) {
            // 检查是否已选择
            if (selectedKeywords.some(keyword => keyword.id === keywordId)) {
                return;
            }

            // 添加到已选择列表
            selectedKeywords.push({ id: keywordId, text: keywordText });

            // 更新显示
            updateSelectedKeywordsDisplay();

            // 清空搜索框并隐藏下拉框
            document.getElementById('keywordSearch').value = '';
            document.getElementById('keywordDropdown').classList.remove('show');
        }

        // 移除关键词
        function removeKeyword(keywordId) {
            selectedKeywords = selectedKeywords.filter(keyword => keyword.id !== keywordId);
            updateSelectedKeywordsDisplay();
        }

        // 更新已选择关键词的显示
        function updateSelectedKeywordsDisplay() {
            const selectedKeywordsContainer = document.getElementById('selectedKeywords');

            if (selectedKeywords.length === 0) {
                selectedKeywordsContainer.innerHTML = '';
                return;
            }

            selectedKeywordsContainer.innerHTML = selectedKeywords.map((keyword, index) => {
                const colorClass = `color-${(index % 10) + 1}`;
                return `
                    <div class="selected-keyword-tag ${colorClass}">
                        ${keyword.text}
                        <span class="remove-keyword" onclick="removeKeyword('${keyword.id}')">&times;</span>
                    </div>
                `;
            }).join('');
        }

        // 显示提示信息
        function showAlert(message, type) {
            const alertElement = document.getElementById(type === 'error' ? 'errorAlert' : 'successAlert');
            const otherAlert = document.getElementById(type === 'error' ? 'successAlert' : 'errorAlert');

            // 隐藏其他类型的提示
            otherAlert.style.display = 'none';

            // 显示当前提示
            alertElement.textContent = message;
            alertElement.style.display = 'block';

            // 3秒后自动隐藏
            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
