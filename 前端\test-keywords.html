<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关键词功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .keyword-search-container {
            position: relative;
        }
        .form-control {
            transition: all 0.3s ease;
            border: 2px solid #ddd;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .keyword-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #667eea;
            border-top: none;
            border-radius: 0 0 8px 8px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
            animation: dropdownSlideDown 0.2s ease-out;
        }

        @keyframes dropdownSlideDown {
            0% {
                opacity: 0;
                transform: translateY(-10px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .keyword-dropdown.show {
            display: block;
        }

        .keyword-item {
            padding: 12px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.2s ease;
            position: relative;
            font-weight: 500;
        }

        .keyword-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transform: scaleY(0);
            transition: transform 0.2s ease;
        }

        .keyword-item:hover {
            background-color: #f8f9fa;
            padding-left: 20px;
        }

        .keyword-item:hover::before {
            transform: scaleY(1);
        }

        .keyword-item.disabled {
            color: #999;
            cursor: not-allowed;
            background-color: #f5f5f5;
        }

        .keyword-item.disabled:hover {
            padding-left: 15px;
        }

        .keyword-item.disabled::before {
            display: none;
        }
        .selected-keywords {
            margin-top: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            min-height: 40px;
            padding: 10px;
            border: 1px dashed #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
            transition: all 0.3s ease;
        }

        .selected-keywords:empty::before {
            content: "暂未选择关键词";
            color: #999;
            font-style: italic;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 20px;
        }

        .keyword-tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .keyword-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .keyword-tag:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .keyword-tag:hover::before {
            left: 100%;
        }

        .keyword-tag .remove-keyword {
            cursor: pointer;
            font-weight: bold;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.2s ease;
            margin-left: 2px;
        }

        .keyword-tag .remove-keyword:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* 不同颜色的关键词标签 */
        .keyword-tag:nth-child(5n+1) {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .keyword-tag:nth-child(5n+2) {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            box-shadow: 0 2px 8px rgba(240, 147, 251, 0.3);
        }

        .keyword-tag:nth-child(5n+3) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
        }

        .keyword-tag:nth-child(5n+4) {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            box-shadow: 0 2px 8px rgba(67, 233, 123, 0.3);
        }

        .keyword-tag:nth-child(5n+5) {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            box-shadow: 0 2px 8px rgba(250, 112, 154, 0.3);
        }

        /* 关键词添加动画 */
        @keyframes keywordFadeIn {
            0% {
                opacity: 0;
                transform: scale(0.8) translateY(-10px);
            }
            50% {
                transform: scale(1.05) translateY(-2px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .keyword-tag.new-keyword {
            animation: keywordFadeIn 0.4s ease-out forwards;
        }

        /* 关键词移除动画 */
        @keyframes keywordFadeOut {
            0% {
                opacity: 1;
                transform: scale(1);
            }
            100% {
                opacity: 0;
                transform: scale(0.8);
            }
        }

        .keyword-tag.removing {
            animation: keywordFadeOut 0.3s ease-in forwards;
        }

        .selected-keywords.empty {
            border-style: dashed;
            border-color: #d0d0d0;
        }

        .selected-keywords:not(.empty) {
            border-style: solid;
            border-color: #e0e0e0;
            background-color: #f8f9fa;
        }
        .no-keywords-found {
            padding: 20px 15px;
            text-align: center;
            color: #999;
            font-style: italic;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 4px;
            margin: 5px;
            position: relative;
        }

        .no-keywords-found::before {
            content: '🔍';
            display: block;
            font-size: 24px;
            margin-bottom: 8px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <h1>关键词搜索功能测试</h1>
    
    <div class="form-group">
        <label for="productKeywords">商品关键词 <small>(最多可选择5个)</small></label>
        <div class="keyword-search-container">
            <input type="text" id="keywordSearch" class="form-control" placeholder="搜索关键词..." autocomplete="off">
            <div class="keyword-dropdown" id="keywordDropdown"></div>
        </div>
        <div class="selected-keywords" id="selectedKeywords"></div>
    </div>
    
    <div style="margin-top: 20px;">
        <button onclick="showSelectedKeywords()">显示已选择的关键词</button>
        <button onclick="clearAllKeywords()">清空所有关键词</button>
    </div>
    
    <script>
        // 模拟关键词数据
        let allKeywords = [
            { id: '1', text: '电子产品', addedAt: new Date().toISOString() },
            { id: '2', text: '手机配件', addedAt: new Date().toISOString() },
            { id: '3', text: '数码相机', addedAt: new Date().toISOString() },
            { id: '4', text: '笔记本电脑', addedAt: new Date().toISOString() },
            { id: '5', text: '智能手表', addedAt: new Date().toISOString() },
            { id: '6', text: '蓝牙耳机', addedAt: new Date().toISOString() },
            { id: '7', text: '充电器', addedAt: new Date().toISOString() },
            { id: '8', text: '数据线', addedAt: new Date().toISOString() },
            { id: '9', text: '移动电源', addedAt: new Date().toISOString() },
            { id: '10', text: '无线鼠标', addedAt: new Date().toISOString() }
        ];
        
        let selectedKeywords = [];
        const maxKeywords = 5;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupKeywordSearch();
        });
        
        const setupKeywordSearch = () => {
            const keywordSearch = document.getElementById('keywordSearch');
            const keywordDropdown = document.getElementById('keywordDropdown');
            
            // 搜索输入事件
            keywordSearch.addEventListener('input', (e) => {
                const searchTerm = e.target.value.trim().toLowerCase();
                filterAndShowKeywords(searchTerm);
            });
            
            // 点击外部关闭下拉框
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.keyword-search-container')) {
                    keywordDropdown.classList.remove('show');
                }
            });
            
            // 获得焦点时显示下拉框
            keywordSearch.addEventListener('focus', () => {
                if (keywordSearch.value.trim()) {
                    filterAndShowKeywords(keywordSearch.value.trim().toLowerCase());
                }
            });
        };
        
        // 过滤并显示关键词
        const filterAndShowKeywords = (searchTerm) => {
            const keywordDropdown = document.getElementById('keywordDropdown');
            
            if (!searchTerm) {
                keywordDropdown.classList.remove('show');
                return;
            }
            
            // 过滤关键词
            const filteredKeywords = allKeywords.filter(keyword => 
                keyword.text.toLowerCase().includes(searchTerm) &&
                !selectedKeywords.some(selected => selected.id === keyword.id)
            );
            
            // 显示下拉框
            if (filteredKeywords.length > 0) {
                keywordDropdown.innerHTML = filteredKeywords.map(keyword => `
                    <div class="keyword-item ${selectedKeywords.length >= maxKeywords ? 'disabled' : ''}" 
                         onclick="selectKeyword('${keyword.id}', '${keyword.text}')">
                        ${keyword.text}
                    </div>
                `).join('');
                keywordDropdown.classList.add('show');
            } else {
                keywordDropdown.innerHTML = '<div class="no-keywords-found">未找到匹配的关键词</div>';
                keywordDropdown.classList.add('show');
            }
        };
        
        // 选择关键词
        const selectKeyword = (keywordId, keywordText) => {
            // 检查是否已达到最大数量
            if (selectedKeywords.length >= maxKeywords) {
                alert(`最多只能选择${maxKeywords}个关键词`);
                return;
            }
            
            // 检查是否已选择
            if (selectedKeywords.some(keyword => keyword.id === keywordId)) {
                return;
            }
            
            // 添加到已选择列表
            selectedKeywords.push({ id: keywordId, text: keywordText });
            
            // 更新显示
            updateSelectedKeywordsDisplay();
            
            // 清空搜索框并隐藏下拉框
            document.getElementById('keywordSearch').value = '';
            document.getElementById('keywordDropdown').classList.remove('show');
        };
        
        // 移除关键词
        const removeKeyword = (keywordId) => {
            const keywordElement = document.querySelector(`[data-keyword-id="${keywordId}"]`);
            if (keywordElement) {
                keywordElement.classList.add('removing');
                setTimeout(() => {
                    selectedKeywords = selectedKeywords.filter(keyword => keyword.id !== keywordId);
                    updateSelectedKeywordsDisplay();
                }, 300);
            } else {
                selectedKeywords = selectedKeywords.filter(keyword => keyword.id !== keywordId);
                updateSelectedKeywordsDisplay();
            }
        };

        // 更新已选择关键词的显示
        const updateSelectedKeywordsDisplay = () => {
            const selectedKeywordsContainer = document.getElementById('selectedKeywords');

            // 更新容器样式
            if (selectedKeywords.length === 0) {
                selectedKeywordsContainer.innerHTML = '';
                selectedKeywordsContainer.classList.add('empty');
                selectedKeywordsContainer.classList.remove('not-empty');
                return;
            } else {
                selectedKeywordsContainer.classList.remove('empty');
                selectedKeywordsContainer.classList.add('not-empty');
            }

            // 获取当前显示的关键词ID
            const currentKeywordIds = Array.from(selectedKeywordsContainer.querySelectorAll('.keyword-tag'))
                .map(tag => tag.getAttribute('data-keyword-id'));

            // 找出新添加的关键词
            const newKeywords = selectedKeywords.filter(keyword =>
                !currentKeywordIds.includes(keyword.id)
            );

            // 重新渲染所有关键词
            selectedKeywordsContainer.innerHTML = selectedKeywords.map((keyword, index) => `
                <div class="keyword-tag ${newKeywords.some(nk => nk.id === keyword.id) ? 'new-keyword' : ''}"
                     data-keyword-id="${keyword.id}">
                    ${keyword.text}
                    <span class="remove-keyword" onclick="removeKeyword('${keyword.id}')">&times;</span>
                </div>
            `).join('');

            // 移除动画类，避免重复触发
            setTimeout(() => {
                selectedKeywordsContainer.querySelectorAll('.new-keyword').forEach(tag => {
                    tag.classList.remove('new-keyword');
                });
            }, 400);
        };
        
        // 测试函数
        const showSelectedKeywords = () => {
            console.log('已选择的关键词:', selectedKeywords);
            alert('已选择的关键词: ' + selectedKeywords.map(k => k.text).join(', '));
        };
        
        const clearAllKeywords = () => {
            selectedKeywords = [];
            updateSelectedKeywordsDisplay();
        };
    </script>
</body>
</html>
