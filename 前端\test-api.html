<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
</head>
<body>
    <h1>API测试页面</h1>
    <button onclick="testCarouselsAPI()">测试轮播图API</button>
    <div id="result"></div>

    <script>
        function testCarouselsAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试...';
            
            fetch('/api/carousels')
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                resultDiv.innerHTML = `
                    <h3>API响应成功</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            })
            .catch(error => {
                console.error('API错误:', error);
                resultDiv.innerHTML = `
                    <h3>API错误</h3>
                    <p style="color: red;">${error.message}</p>
                `;
            });
        }
    </script>
</body>
</html>
