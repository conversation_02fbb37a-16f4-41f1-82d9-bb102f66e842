<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品详情 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="js/blacklist-handler.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 0;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #333;
        }

        .logo-img {
            width: 40px;
            height: 40px;
            margin-right: 10px;
            border-radius: 8px;
        }

        .logo-text {
            font-size: 20px;
            font-weight: bold;
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-back {
            background: #3498db;
            color: white;
        }

        .btn-back:hover {
            background: #2980b9;
        }

        .btn-home {
            background: #2ecc71;
            color: white;
        }

        .btn-home:hover {
            background: #27ae60;
        }

        /* Main Content */
        main {
            padding: 30px 0;
            min-height: calc(100vh - 100px);
        }

        .product-detail-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .product-detail-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto;
            gap: 40px;
            padding: 40px;
        }

        .product-media-tabs {
            grid-column: 1;
            grid-row: 1 / 3;
        }

        .product-info {
            grid-column: 2;
            grid-row: 1;
        }

        .product-actions {
            grid-column: 2;
            grid-row: 2;
            align-self: end;
        }

        /* Product Images */
        .product-images {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .main-image {
            width: 100%;
            height: 400px;
            border-radius: 12px;
            overflow: hidden;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .main-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .thumbnail-images {
            display: flex;
            gap: 10px;
            overflow-x: auto;
        }

        .thumbnail {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .thumbnail.active {
            border-color: #3498db;
        }

        .thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Product Info */
        .product-info {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .product-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            line-height: 1.3;
        }

        .product-price {
            display: flex;
            align-items: baseline;
            gap: 15px;
        }

        .current-price {
            font-size: 32px;
            font-weight: bold;
            color: #e74c3c;
        }

        .original-price {
            font-size: 18px;
            color: #999;
            text-decoration: line-through;
        }

        .product-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: #f8f9fa;
            border-radius: 8px;
            gap: 20px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .product-actions-inline {
            display: flex;
            gap: 12px;
            flex-shrink: 0;
        }

        .product-actions-inline .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 6px;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            letter-spacing: 0.5px;
        }

        .product-actions-inline .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        .product-actions-inline .action-btn:hover::before {
            left: 100%;
        }

        .product-actions-inline .action-btn i {
            font-size: 16px;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }

        .product-actions-inline .btn-add-cart {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
            color: #722ed1;
            border: 2px solid transparent;
            background-clip: padding-box;
            box-shadow:
                0 2px 8px rgba(114, 46, 209, 0.15),
                0 1px 3px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            position: relative;
        }

        .product-actions-inline .btn-add-cart::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 10px;
            padding: 2px;
            background: linear-gradient(135deg, #722ed1, #9254de, #b37feb);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            -webkit-mask-composite: xor;
        }

        .product-actions-inline .btn-add-cart:hover {
            background: linear-gradient(135deg, #722ed1 0%, #9254de 50%, #b37feb 100%);
            color: #fff;
            transform: translateY(-2px) scale(1.02);
            box-shadow:
                0 8px 25px rgba(114, 46, 209, 0.4),
                0 4px 12px rgba(114, 46, 209, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .product-actions-inline .btn-add-cart:active {
            transform: translateY(-1px) scale(1.01);
            box-shadow:
                0 4px 15px rgba(114, 46, 209, 0.3),
                0 2px 8px rgba(114, 46, 209, 0.2);
        }

        .product-actions-inline .btn-buy-now {
            background: linear-gradient(135deg, #722ed1 0%, #9254de 50%, #b37feb 100%);
            color: #fff;
            border: 2px solid transparent;
            box-shadow:
                0 4px 15px rgba(114, 46, 209, 0.3),
                0 2px 8px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                inset 0 -1px 0 rgba(0, 0, 0, 0.1);
        }

        .product-actions-inline .btn-buy-now:hover {
            background: linear-gradient(135deg, #5a1ea6 0%, #722ed1 50%, #9254de 100%);
            transform: translateY(-2px) scale(1.02);
            box-shadow:
                0 12px 30px rgba(114, 46, 209, 0.5),
                0 6px 20px rgba(114, 46, 209, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                inset 0 -1px 0 rgba(0, 0, 0, 0.1);
        }

        .product-actions-inline .btn-buy-now:active {
            transform: translateY(-1px) scale(1.01);
            box-shadow:
                0 6px 20px rgba(114, 46, 209, 0.4),
                0 3px 12px rgba(114, 46, 209, 0.3);
        }

        .meta-label {
            font-weight: bold;
            color: #666;
        }

        .meta-value {
            color: #333;
        }

        .product-description {
            margin-top: 0;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid #e9ecef;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            max-height: 280px;
            overflow: hidden;
            position: relative;
            transition: all 0.3s ease;
        }

        .product-description:hover {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
            transform: translateY(-1px);
        }

        .description-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            padding: 20px 20px 15px 20px;
            color: #2c3e50;
            background: linear-gradient(90deg, #3498db, #2980b9);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            border-bottom: 2px solid #e9ecef;
            position: relative;
        }

        .description-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 20px;
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, #3498db, #2980b9);
        }

        .description-content {
            color: #4a5568;
            line-height: 1.8;
            padding: 20px;
            max-height: 200px;
            overflow-y: auto;
            font-size: 15px;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .description-content p {
            margin: 0 0 12px 0;
            text-align: justify;
        }

        .description-content p:last-child {
            margin-bottom: 0;
        }

        .description-content strong {
            color: #2c3e50;
            font-weight: 600;
        }

        .description-content em {
            color: #3498db;
            font-style: normal;
            font-weight: 500;
        }

        /* 自定义滚动条样式 */
        .description-content::-webkit-scrollbar {
            width: 8px;
        }

        .description-content::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
            margin: 5px 0;
        }

        .description-content::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #3498db, #2980b9);
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .description-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, #2980b9, #1f5f8b);
            transform: scaleY(1.1);
        }

        /* 添加渐变遮罩效果 */
        .product-description::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 20px;
            background: linear-gradient(transparent, rgba(248, 249, 250, 0.8));
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .product-description.has-scroll::after {
            opacity: 1;
        }

        /* 款式选择样式 */
        .style-selection {
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 1px solid #e9ecef;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }

        .style-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .style-options {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
        }

        .style-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            text-decoration: none;
            color: inherit;
        }

        .style-option:hover {
            border-color: #3498db;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.2);
        }

        .style-option.current {
            border-color: #27ae60;
            background: #e8f5e8;
        }

        .style-option-image {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            object-fit: cover;
            margin-bottom: 6px;
        }

        .style-option-name {
            font-size: 12px;
            text-align: center;
            line-height: 1.3;
            color: #2c3e50;
            font-weight: 500;
        }

        .style-option-price {
            font-size: 11px;
            color: #e74c3c;
            font-weight: 600;
            margin-top: 4px;
        }

        /* 分页控件样式 */
        .style-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 15px;
        }

        .pagination-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: white;
            color: #6c757d;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            text-decoration: none;
        }

        .pagination-btn:hover:not(.disabled) {
            border-color: #3498db;
            color: #3498db;
            background: #f8f9fa;
            transform: translateY(-1px);
        }

        .pagination-btn.disabled {
            opacity: 0.4;
            cursor: not-allowed;
            pointer-events: none;
        }

        .pagination-info {
            font-size: 14px;
            color: #6c757d;
            font-weight: 500;
            min-width: 80px;
            text-align: center;
        }

        .product-video {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .product-video:hover {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
            transform: translateY(-1px);
        }

        .product-video h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .product-video h4 i {
            margin-right: 8px;
            color: #722ed1;
        }

        /* 媒体标签页样式 */
        .product-media-tabs {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            height: fit-content;
        }

        .media-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .media-tab-button {
            flex: 1;
            padding: 15px 12px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            min-height: 50px;
        }

        .media-tab-button:hover {
            background: rgba(114, 46, 209, 0.1);
            color: #722ed1;
        }

        .media-tab-button.active {
            background: white;
            color: #722ed1;
            font-weight: 600;
        }

        .media-tab-button.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #722ed1, #9254de);
        }

        .media-tab-content {
            display: none;
            min-height: 400px;
        }

        .media-tab-content.active {
            display: block;
        }

        /* 图片标签页内容 */
        .tab-images-content {
            padding: 0;
        }

        .tab-images-content .product-images {
            margin: 0;
        }

        .tab-images-content .main-image {
            width: 100%;
            height: 400px;
            border-radius: 0;
            overflow: hidden;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .tab-images-content .main-image img {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            object-fit: contain;
            transition: transform 0.3s ease;
        }

        .tab-images-content .thumbnail-images {
            padding: 20px 15px;
            display: flex;
            gap: 15px;
            overflow-x: auto;
            background: #f8f9fa;
            justify-content: center;
            align-items: center;
            min-height: 100px;
        }

        .tab-images-content .thumbnail-images img {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            object-fit: contain;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .tab-images-content .thumbnail-images img:hover,
        .tab-images-content .thumbnail-images img.active {
            border-color: #722ed1;
            transform: scale(1.08);
            box-shadow: 0 4px 16px rgba(114, 46, 209, 0.3);
        }

        /* 视频标签页内容 */
        .tab-video-content {
            padding: 20px;
        }

        .tab-video-content .video-container {
            border-radius: 8px;
            overflow: hidden;
            background: #000;
            min-height: 300px;
        }

        /* 描述标签页内容 */
        .tab-description-content .product-description {
            margin: 0;
            border: none;
            box-shadow: none;
            border-radius: 0;
            background: white;
        }

        .video-container {
            border-radius: 8px;
            overflow: hidden;
            background: #000;
        }

        .video-container iframe {
            width: 100%;
            height: 300px;
            border: none;
        }

        .product-actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .action-btn {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-add-cart {
            background: #f39c12;
            color: white;
        }

        .btn-add-cart:hover {
            background: #e67e22;
        }

        .btn-buy-now {
            background: #e74c3c;
            color: white;
        }

        .btn-buy-now:hover {
            background: #c0392b;
        }

        /* Loading State */
        .loading {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .loading i {
            font-size: 48px;
            margin-bottom: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Error State */
        .error {
            text-align: center;
            padding: 60px 20px;
            color: #e74c3c;
        }

        .error i {
            font-size: 48px;
            margin-bottom: 20px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .product-detail-content {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto;
                gap: 20px;
                padding: 20px;
            }

            .product-media-tabs {
                grid-column: 1;
                grid-row: 1;
            }

            .product-info {
                grid-column: 1;
                grid-row: 2;
            }

            .product-actions {
                grid-column: 1;
                grid-row: 3;
            }

            .media-tab-button {
                padding: 12px 8px;
                font-size: 12px;
                gap: 4px;
            }

            .media-tab-content {
                min-height: 300px;
            }

            .tab-images-content .main-image {
                height: 300px;
            }

            .tab-video-content {
                padding: 15px;
            }

            .tab-video-content .video-container {
                min-height: 200px;
            }

            .product-description {
                margin-top: 0;
                max-height: 220px;
            }

            .description-title {
                font-size: 18px;
                padding: 15px 15px 12px 15px;
            }

            .description-title::after {
                left: 15px;
                width: 30px;
            }

            .description-content {
                padding: 15px;
                font-size: 14px;
                max-height: 160px;
            }

            .product-title {
                font-size: 24px;
            }

            .current-price {
                font-size: 28px;
            }

            .product-meta {
                padding: 12px 15px;
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }

            .meta-item {
                justify-content: center;
            }

            .product-actions-inline {
                justify-content: center;
                gap: 10px;
            }

            .product-actions-inline .action-btn {
                flex: 1;
                padding: 10px 12px;
                font-size: 13px;
                min-width: 0;
                border-radius: 10px;
                font-weight: 600;
            }

            .product-actions-inline .action-btn i {
                font-size: 14px;
            }

            .product-actions-inline .btn-add-cart:hover,
            .product-actions-inline .btn-buy-now:hover {
                transform: translateY(-1px) scale(1.01);
            }

            .product-actions-inline .btn-add-cart:active,
            .product-actions-inline .btn-buy-now:active {
                transform: translateY(0) scale(1);
            }

            .product-actions {
                flex-direction: column;
            }
        }

        /* 平板设备适配 */
        @media (max-width: 1024px) and (min-width: 769px) {
            .product-description {
                max-height: 240px;
            }

            .description-content {
                max-height: 180px;
            }
        }

        /* 小屏幕优化 */
        @media (max-width: 480px) {
            .product-description {
                margin-top: 10px;
                max-height: 200px;
            }

            .description-title {
                font-size: 16px;
                padding: 12px 12px 10px 12px;
            }

            .description-content {
                padding: 12px;
                font-size: 13px;
                max-height: 140px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">
                    <img src="images/logo.png" alt="金舟国际物流" class="logo-img" onerror="this.style.display='none'">
                    <span class="logo-text">金舟国际物流</span>
                </a>
                <div class="nav-buttons">
                    <button class="nav-btn btn-back" onclick="goBack()">
                        <i class="fas fa-arrow-left"></i> 返回
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <div class="container">
            <div class="product-detail-container">
                <div id="productDetailContent">
                    <!-- 商品详情内容将通过JavaScript动态加载 -->
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <h3>正在加载商品详情...</h3>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 分页相关变量
        let styleData = {
            style: null,
            relatedProducts: [],
            currentProductId: null,
            currentPage: 1,
            itemsPerPage: 8,
            totalPages: 1
        };

        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 加载商品详情
        function loadProductDetail() {
            const productId = getUrlParameter('id');
            
            if (!productId) {
                showError('商品ID不存在');
                return;
            }

            // 从服务器获取商品详情
            fetch(`/api/products/${productId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.product) {
                    renderProductDetail(data.product);
                } else {
                    showError('商品不存在或已下架');
                }
            })
            .catch(error => {
                console.error('加载商品详情失败:', error);
                showError('网络错误，请稍后重试');
            });
        }

        // 渲染商品详情
        function renderProductDetail(product) {
            const container = document.getElementById('productDetailContent');

            // 处理图片 - 优先使用 mainImage，然后是 images 数组
            let images = [];
            let mainImage = '';

            if (product.mainImage) {
                // 如果有主图，使用主图作为第一张图片
                mainImage = product.mainImage;
                images = [{ url: product.mainImage }];

                // 如果还有其他图片，添加到数组中
                if (product.images && product.images.length > 0) {
                    const additionalImages = product.images.filter(img => img.url !== product.mainImage);
                    images = images.concat(additionalImages);
                }
            } else if (product.images && product.images.length > 0) {
                // 如果没有主图但有图片数组，使用第一张作为主图
                images = product.images;
                mainImage = images[0].url;
            } else {
                // 如果都没有，使用占位图
                mainImage = 'https://via.placeholder.com/400x400/f0f0f0/999999?text=暂无图片';
                images = [];
            }

            // 生成缩略图
            const thumbnailsHtml = images.map((img, index) => `
                <img src="${img.url}" alt="商品图片${index + 1}"
                     class="${index === 0 ? 'active' : ''}"
                     onclick="changeMainImage('${img.url}')"
                     onerror="this.style.display='none'">
            `).join('');

            // 检查是否有视频或描述内容
            const hasVideo = product.videoUrl && product.videoUrl.trim();
            const hasDescription = product.description && product.description.trim();

            container.innerHTML = `
                <div class="product-detail-content">
                    <div class="product-media-tabs">
                        <div class="media-tabs">
                            <button class="media-tab-button active" onclick="switchMediaTab('images')">
                                <i class="fas fa-images"></i>
                                商品图片
                            </button>
                            ${hasVideo ? `<button class="media-tab-button" onclick="switchMediaTab('video')">
                                <i class="fas fa-video"></i>
                                商品视频
                            </button>` : ''}
                            ${hasDescription ? `<button class="media-tab-button" onclick="switchMediaTab('description')">
                                <i class="fas fa-file-text"></i>
                                商品描述
                            </button>` : ''}
                        </div>

                        <!-- 图片标签页 -->
                        <div id="imagesTab" class="media-tab-content active">
                            <div class="tab-images-content">
                                <div class="product-images">
                                    <div class="main-image">
                                        <img id="mainProductImage" src="${mainImage}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/400x400/f0f0f0/999999?text=暂无图片'">
                                    </div>
                                    ${images.length > 1 ? `<div class="thumbnail-images">${thumbnailsHtml}</div>` : ''}
                                </div>
                            </div>
                        </div>

                        ${hasVideo ? `
                        <!-- 视频标签页 -->
                        <div id="videoTab" class="media-tab-content">
                            <div class="tab-video-content">
                                <div id="videoContainer" class="video-container">
                                    <!-- 视频内容将通过JavaScript动态加载 -->
                                </div>
                            </div>
                        </div>
                        ` : ''}

                        ${hasDescription ? `
                        <!-- 描述标签页 -->
                        <div id="descriptionTab" class="media-tab-content">
                            <div class="tab-description-content">
                                <div class="product-description" id="productDescription">
                                    <div class="description-title">商品描述</div>
                                    <div class="description-content" id="descriptionContent">${formatDescription(product.description)}</div>
                                </div>
                            </div>
                        </div>
                        ` : ''}
                    </div>

                    <div class="product-info">
                        <h1 class="product-title">${product.name}</h1>
                        <div class="product-price">
                            <span class="current-price">¥${parseFloat(product.price).toFixed(2)}</span>
                            <span class="original-price">¥${(parseFloat(product.price) * 1.3).toFixed(2)}</span>
                        </div>
                        <div class="product-meta">
                            <div class="meta-item">
                                <span class="meta-label">库存:</span>
                                <span class="meta-value">${product.stock || '有库存'}</span>
                            </div>
                            <div class="product-actions-inline">
                                <button class="action-btn btn-add-cart" onclick="addToCart('${product.id}')">
                                    <i class="fas fa-shopping-cart"></i> 加入购物车
                                </button>
                                <button class="action-btn btn-buy-now" onclick="buyNow('${product.id}')">
                                    <i class="fas fa-bolt"></i> 立即购买
                                </button>
                            </div>
                        </div>

                        <!-- 款式选择区域 -->
                        <div id="styleSelection" class="style-selection" style="display: none;">
                            <!-- 款式选择内容将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
            `;

            // 更新页面标题
            document.title = `${product.name} - 金舟国际物流`;

            // 初始化描述区域的滚动检测
            initDescriptionScroll();

            // 加载款式选择
            loadStyleSelection(product.id);

            // 加载商品视频
            loadProductVideo(product);
        }

        // 格式化商品描述文本
        function formatDescription(description) {
            if (!description) return '';

            // 保留原始格式，只进行基本的HTML转义和样式处理
            let formatted = description
                // HTML转义
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                // 处理特殊标记（如果有的话）
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                // 处理数字列表
                .replace(/(\d+[\.\)]\s*)/g, '<strong>$1</strong>');

            return formatted;
        }

        // 初始化描述区域的滚动检测
        function initDescriptionScroll() {
            const descriptionElement = document.getElementById('productDescription');
            const contentElement = document.getElementById('descriptionContent');

            if (!descriptionElement || !contentElement) return;

            // 检查是否需要滚动
            function checkScroll() {
                const hasScroll = contentElement.scrollHeight > contentElement.clientHeight;
                if (hasScroll) {
                    descriptionElement.classList.add('has-scroll');
                } else {
                    descriptionElement.classList.remove('has-scroll');
                }
            }

            // 初始检查
            setTimeout(checkScroll, 100);

            // 监听窗口大小变化
            window.addEventListener('resize', checkScroll);
        }

        // 显示错误信息
        function showError(message) {
            const container = document.getElementById('productDetailContent');
            container.innerHTML = `
                <div class="error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>${message}</h3>
                    <button onclick="location.reload()" style="margin-top: 20px; background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">重新加载</button>
                </div>
            `;
        }

        // 切换主图
        function changeMainImage(imageUrl, thumbnail) {
            const mainImage = document.getElementById('mainProductImage');
            mainImage.src = imageUrl;
            
            // 更新缩略图激活状态
            document.querySelectorAll('.thumbnail').forEach(thumb => thumb.classList.remove('active'));
            thumbnail.classList.add('active');
        }

        // 加入购物车
        function addToCart(productId) {
            alert(`商品 ${productId} 已加入购物车！`);
        }

        // 立即购买
        function buyNow(productId) {
            alert('即将跳转到购买页面');
        }

        // 智能返回函数
        function goBack() {
            // 检查是否有历史记录可以返回
            if (window.history.length > 1) {
                // 尝试使用 history.back()
                window.history.back();

                // 设置一个超时检查，如果页面没有变化，则使用备用方案
                setTimeout(() => {
                    // 如果页面仍然是当前页面，说明 history.back() 没有生效
                    if (window.location.href.includes('product-detail.html')) {
                        handleBackupReturn();
                    }
                }, 100);
            } else {
                // 没有历史记录，直接使用备用方案
                handleBackupReturn();
            }
        }

        // 备用返回方案
        function handleBackupReturn() {
            // 首先检查 URL 参数中的来源信息
            const urlParams = new URLSearchParams(window.location.search);
            const from = urlParams.get('from');

            if (from) {
                switch (from) {
                    case 'admin':
                        window.location.href = 'admin-dashboard.html';
                        return;
                    case 'recommend':
                        // 检查是否有搜索参数需要保留
                        const returnUrl = buildReturnUrl();
                        window.location.href = returnUrl;
                        return;
                    case 'carousel':
                        // 尝试从referrer中获取轮播图ID
                        const carouselUrl = buildCarouselReturnUrl();
                        window.location.href = carouselUrl;
                        return;
                    case 'hotsales':
                        window.location.href = 'hot-sales.html';
                        return;
                    default:
                        window.location.href = 'recommend.html';
                        return;
                }
            }

            // 如果没有 from 参数，检查 referrer 来判断来源
            const referrer = document.referrer;

            if (referrer) {
                if (referrer.includes('admin-dashboard.html')) {
                    // 来自管理员页面
                    window.location.href = 'admin-dashboard.html';
                } else if (referrer.includes('recommend.html')) {
                    // 来自推荐页面，尝试重建原始URL
                    const returnUrl = buildReturnUrlFromReferrer(referrer);
                    window.location.href = returnUrl;
                } else if (referrer.includes('hot-sales.html')) {
                    // 来自热销页面
                    window.location.href = 'hot-sales.html';
                } else if (referrer.includes('carousel-products.html')) {
                    // 来自轮播图商品页面
                    window.location.href = 'recommend.html';
                } else if (referrer.includes('index.html')) {
                    // 来自首页
                    window.location.href = 'index.html';
                } else {
                    // 其他情况，返回到推荐页面
                    window.location.href = 'recommend.html';
                }
            } else {
                // 没有 referrer，默认返回推荐页面
                window.location.href = 'recommend.html';
            }
        }

        // 构建返回URL，保留搜索参数和页码
        function buildReturnUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const returnParams = new URLSearchParams();

            // 保留搜索相关的参数
            const searchParams = ['search', 'category', 'categoryName', 'keyword'];
            searchParams.forEach(param => {
                const value = urlParams.get(param);
                if (value) {
                    returnParams.set(param, value);
                }
            });

            // 保留页码参数
            const page = urlParams.get('page');
            if (page && page !== '1') {
                returnParams.set('returnPage', page);
            }

            // 构建返回URL
            const baseUrl = 'recommend.html';
            if (returnParams.toString()) {
                return `${baseUrl}?${returnParams.toString()}`;
            } else {
                return baseUrl;
            }
        }

        // 构建轮播图商品页面返回URL
        function buildCarouselReturnUrl() {
            // 首先尝试从referrer中获取轮播图ID
            const referrer = document.referrer;
            if (referrer && referrer.includes('carousel-products.html')) {
                try {
                    const referrerUrl = new URL(referrer);
                    const carouselId = referrerUrl.searchParams.get('id');
                    if (carouselId) {
                        return `carousel-products.html?id=${carouselId}`;
                    }
                } catch (error) {
                    console.error('解析referrer URL失败:', error);
                }
            }

            // 如果无法从referrer获取ID，返回基本的轮播图商品页面
            return 'carousel-products.html';
        }

        // 从referrer URL中提取参数并构建返回URL
        function buildReturnUrlFromReferrer(referrer) {
            try {
                const referrerUrl = new URL(referrer);
                const referrerParams = referrerUrl.searchParams;
                const returnParams = new URLSearchParams();

                // 保留搜索相关的参数
                const searchParams = ['search', 'category', 'categoryName', 'keyword'];
                searchParams.forEach(param => {
                    const value = referrerParams.get(param);
                    if (value) {
                        returnParams.set(param, value);
                    }
                });

                // 从当前URL获取页码参数（因为referrer可能没有页码信息）
                const currentParams = new URLSearchParams(window.location.search);
                const page = currentParams.get('page');
                if (page && page !== '1') {
                    returnParams.set('returnPage', page);
                }

                // 构建返回URL
                const baseUrl = 'recommend.html';
                if (returnParams.toString()) {
                    return `${baseUrl}?${returnParams.toString()}`;
                } else {
                    return baseUrl;
                }
            } catch (error) {
                console.error('解析referrer URL失败:', error);
                return 'recommend.html';
            }
        }

        // 加载款式选择
        async function loadStyleSelection(productId) {
            try {
                const response = await fetch(`/api/styles/product/${productId}`);
                const data = await response.json();

                if (data.success && data.style && data.relatedProducts.length > 0) {
                    renderStyleSelection(data.style, data.relatedProducts, productId);
                }
            } catch (error) {
                console.error('加载款式选择失败:', error);
            }
        }

        // 渲染款式选择
        function renderStyleSelection(style, relatedProducts, currentProductId) {
            // 保存数据到全局变量
            styleData.style = style;
            styleData.relatedProducts = relatedProducts;
            styleData.currentProductId = currentProductId;
            styleData.currentPage = 1;
            styleData.totalPages = Math.ceil(relatedProducts.length / styleData.itemsPerPage);

            // 渲染当前页面
            renderStylePage();
        }

        // 渲染指定页面的款式
        function renderStylePage() {
            const container = document.getElementById('styleSelection');
            const { style, relatedProducts, currentProductId, currentPage, itemsPerPage, totalPages } = styleData;

            // 计算当前页面的商品范围
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = Math.min(startIndex + itemsPerPage, relatedProducts.length);
            const currentPageProducts = relatedProducts.slice(startIndex, endIndex);

            // 生成当前页面的款式选项HTML
            const styleOptionsHtml = currentPageProducts.map(product => {
                const mainImage = product.images && product.images.length > 0
                    ? product.images[0].url
                    : 'https://via.placeholder.com/60x60/f0f0f0/999999?text=无图';

                return `
                    <a href="product-detail.html?id=${product.id}" class="style-option ${product.id === currentProductId ? 'current' : ''}">
                        <img src="${mainImage}" alt="${product.name}" class="style-option-image">
                        <div class="style-option-name">${product.name}</div>
                        <div class="style-option-price">¥${parseFloat(product.price).toFixed(2)}</div>
                    </a>
                `;
            }).join('');

            // 生成分页控件HTML（只有当总页数大于1时才显示）
            const paginationHtml = totalPages > 1 ? `
                <div class="style-pagination">
                    <button class="pagination-btn ${currentPage === 1 ? 'disabled' : ''}" onclick="changeStylePage(${currentPage - 1})">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="pagination-info">
                        ${currentPage} / ${totalPages}
                    </div>
                    <button class="pagination-btn ${currentPage === totalPages ? 'disabled' : ''}" onclick="changeStylePage(${currentPage + 1})">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            ` : '';

            container.innerHTML = `
                <div class="style-title">
                    <i class="fas fa-palette"></i>
                    ${style.name}
                </div>
                <div class="style-options">
                    ${styleOptionsHtml}
                </div>
                ${paginationHtml}
            `;

            container.style.display = 'block';
        }

        // 切换款式页面
        function changeStylePage(newPage) {
            if (newPage < 1 || newPage > styleData.totalPages) {
                return;
            }

            styleData.currentPage = newPage;
            renderStylePage();
        }

        // 媒体标签页切换函数
        function switchMediaTab(tabName) {
            // 移除所有标签按钮的active类
            document.querySelectorAll('.media-tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // 隐藏所有标签内容
            document.querySelectorAll('.media-tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 激活当前标签按钮和内容
            event.target.classList.add('active');

            if (tabName === 'images') {
                document.getElementById('imagesTab').classList.add('active');
            } else if (tabName === 'video') {
                document.getElementById('videoTab').classList.add('active');
            } else if (tabName === 'description') {
                document.getElementById('descriptionTab').classList.add('active');
            }
        }

        // 图片切换函数
        function changeMainImage(imageSrc) {
            const mainImage = document.getElementById('mainProductImage');
            if (mainImage) {
                mainImage.src = imageSrc;
            }

            // 更新缩略图的active状态
            document.querySelectorAll('.thumbnail-images img').forEach(img => {
                img.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // 加载商品视频
        function loadProductVideo(product) {
            const videoContainer = document.getElementById('videoContainer');

            if (product.videoUrl && product.videoUrl.trim() && videoContainer) {
                const videoEmbed = generateVideoEmbed(product.videoUrl);
                videoContainer.innerHTML = videoEmbed;
            }
        }

        // 生成视频嵌入代码
        function generateVideoEmbed(videoUrl) {
            if (!videoUrl) return '';

            // 本地上传的视频文件
            if (videoUrl.startsWith('/uploads/')) {
                return `<video width="100%" height="300" controls style="border-radius: 8px;">
                    <source src="${videoUrl}" type="video/mp4">
                    <source src="${videoUrl}" type="video/webm">
                    <source src="${videoUrl}" type="video/ogg">
                    您的浏览器不支持视频播放。
                </video>`;
            }

            // YouTube
            if (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be')) {
                let videoId = '';
                if (videoUrl.includes('youtu.be/')) {
                    videoId = videoUrl.split('youtu.be/')[1].split('?')[0];
                } else if (videoUrl.includes('watch?v=')) {
                    videoId = videoUrl.split('watch?v=')[1].split('&')[0];
                }
                if (videoId) {
                    return `<iframe width="100%" height="300" src="https://www.youtube.com/embed/${videoId}" frameborder="0" allowfullscreen></iframe>`;
                }
            }

            // Bilibili
            if (videoUrl.includes('bilibili.com')) {
                const bvMatch = videoUrl.match(/BV[a-zA-Z0-9]+/);
                if (bvMatch) {
                    const bvid = bvMatch[0];
                    return `<iframe width="100%" height="300" src="//player.bilibili.com/player.html?bvid=${bvid}" frameborder="0" allowfullscreen></iframe>`;
                }
            }

            // 优酷
            if (videoUrl.includes('youku.com')) {
                const idMatch = videoUrl.match(/id_([a-zA-Z0-9]+)/);
                if (idMatch) {
                    const videoId = idMatch[1];
                    return `<iframe width="100%" height="300" src="https://player.youku.com/embed/${videoId}" frameborder="0" allowfullscreen></iframe>`;
                }
            }

            // 腾讯视频
            if (videoUrl.includes('v.qq.com')) {
                const vidMatch = videoUrl.match(/\/([a-zA-Z0-9]+)\.html/);
                if (vidMatch) {
                    const vid = vidMatch[1];
                    return `<iframe width="100%" height="300" src="https://v.qq.com/txp/iframe/player.html?vid=${vid}" frameborder="0" allowfullscreen></iframe>`;
                }
            }

            // 抖音 - 不支持嵌入，显示特殊提示
            if (videoUrl.includes('douyin.com') || videoUrl.includes('v.douyin.com')) {
                return `<div style="padding: 30px; background: linear-gradient(135deg, #ff6b6b, #ff8e8e); color: white; border-radius: 12px; text-align: center;">
                    <div style="font-size: 64px; margin-bottom: 20px;">🎵</div>
                    <h3 style="margin: 0 0 15px 0; font-size: 20px; font-weight: 600;">抖音视频</h3>
                    <p style="margin: 0 0 20px 0; font-size: 16px; opacity: 0.9; line-height: 1.5;">这是一个抖音视频链接<br>请在抖音APP中观看完整内容</p>
                    <a href="${videoUrl}" target="_blank" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; text-decoration: none; padding: 12px 24px; border-radius: 25px; font-size: 16px; font-weight: 500; transition: all 0.3s ease; border: 2px solid rgba(255,255,255,0.3);">
                        <i class="fas fa-external-link-alt" style="margin-right: 8px;"></i>在抖音中打开
                    </a>
                </div>`;
            }

            // 快手 - 不支持嵌入
            if (videoUrl.includes('kuaishou.com') || videoUrl.includes('v.kuaishou.com')) {
                return `<div style="padding: 30px; background: linear-gradient(135deg, #ff6600, #ff8833); color: white; border-radius: 12px; text-align: center;">
                    <div style="font-size: 64px; margin-bottom: 20px;">⚡</div>
                    <h3 style="margin: 0 0 15px 0; font-size: 20px; font-weight: 600;">快手视频</h3>
                    <p style="margin: 0 0 20px 0; font-size: 16px; opacity: 0.9; line-height: 1.5;">这是一个快手视频链接<br>请在快手APP中观看完整内容</p>
                    <a href="${videoUrl}" target="_blank" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; text-decoration: none; padding: 12px 24px; border-radius: 25px; font-size: 16px; font-weight: 500; transition: all 0.3s ease; border: 2px solid rgba(255,255,255,0.3);">
                        <i class="fas fa-external-link-alt" style="margin-right: 8px;"></i>在快手中打开
                    </a>
                </div>`;
            }

            // 小红书 - 不支持嵌入
            if (videoUrl.includes('xiaohongshu.com') || videoUrl.includes('xhslink.com')) {
                return `<div style="padding: 30px; background: linear-gradient(135deg, #ff2442, #ff6b8a); color: white; border-radius: 12px; text-align: center;">
                    <div style="font-size: 64px; margin-bottom: 20px;">📖</div>
                    <h3 style="margin: 0 0 15px 0; font-size: 20px; font-weight: 600;">小红书内容</h3>
                    <p style="margin: 0 0 20px 0; font-size: 16px; opacity: 0.9; line-height: 1.5;">这是一个小红书内容链接<br>请在小红书APP中观看完整内容</p>
                    <a href="${videoUrl}" target="_blank" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; text-decoration: none; padding: 12px 24px; border-radius: 25px; font-size: 16px; font-weight: 500; transition: all 0.3s ease; border: 2px solid rgba(255,255,255,0.3);">
                        <i class="fas fa-external-link-alt" style="margin-right: 8px;"></i>在小红书中打开
                    </a>
                </div>`;
            }

            // 如果无法识别，显示通用链接
            return `<div style="padding: 30px; background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 12px; text-align: center;">
                <div style="font-size: 64px; margin-bottom: 20px; color: #6c757d;">🎬</div>
                <h3 style="margin: 0 0 15px 0; font-size: 20px; color: #495057; font-weight: 600;">外部视频链接</h3>
                <p style="margin: 0 0 20px 0; font-size: 16px; color: #6c757d; line-height: 1.5;">此视频需要在原网站观看</p>
                <a href="${videoUrl}" target="_blank" style="display: inline-block; background: #007bff; color: white; text-decoration: none; padding: 12px 24px; border-radius: 8px; font-size: 16px; font-weight: 500; transition: all 0.3s ease;">
                    <i class="fas fa-play" style="margin-right: 8px;"></i>观看视频
                </a>
                <div style="margin-top: 20px; padding: 15px; background: #e9ecef; border-radius: 6px; font-size: 14px; color: #6c757d; word-break: break-all; font-family: monospace;">
                    ${videoUrl}
                </div>
            </div>`;
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadProductDetail();
        });
    </script>
</body>
</html>
