<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服中心 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background-color: #0c4da2;
            color: white;
            padding: 20px 0;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-bottom: 10px;
            border-radius: 10px;
            background-color: white;
            padding: 5px;
        }
        
        .sidebar-title {
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .cs-badge {
            display: inline-block;
            background-color: #28a745;
            color: white;
            font-size: 12px;
            padding: 3px 10px;
            border-radius: 20px;
            margin-top: 5px;
        }
        
        .nav-menu {
            list-style: none;
        }
        
        .nav-item {
            margin-bottom: 5px;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .nav-link:hover, .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .nav-icon {
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            padding: 30px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .page-title {
            font-size: 24px;
            color: #0c4da2;
        }
        
        .header-actions {
            display: flex;
            align-items: center;
        }
        
        .cs-info {
            display: flex;
            align-items: center;
            margin-right: 20px;
        }
        
        .cs-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #28a745;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .cs-name {
            font-weight: bold;
        }
        
        .logout-btn {
            background-color: #f5f5f5;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
        }
        
        .logout-btn:hover {
            background-color: #e0e0e0;
        }
        
        .logout-icon {
            margin-right: 5px;
        }
        
        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        
        .stat-card {
            display: flex;
            flex-direction: column;
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .stat-title {
            color: #666;
            font-size: 16px;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(40, 167, 69, 0.1);
            color: #28a745;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #28a745;
        }
        
        .tickets-list {
            list-style: none;
        }
        
        .ticket-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .ticket-item:last-child {
            border-bottom: none;
        }
        
        .ticket-status {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 15px;
        }
        
        .status-new {
            background-color: #dc3545;
        }
        
        .status-processing {
            background-color: #ffc107;
        }
        
        .status-resolved {
            background-color: #28a745;
        }
        
        .ticket-info {
            flex: 1;
        }
        
        .ticket-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .ticket-meta {
            font-size: 12px;
            color: #666;
        }
        
        .ticket-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-view, .btn-reply {
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn-view {
            background-color: #e9f0fd;
            color: #0c4da2;
        }
        
        .btn-reply {
            background-color: #e9f7ef;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏导航 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="sidebar-title">金舟国际物流</h1>
                <span class="cs-badge">客服中心</span>
            </div>
            
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#" class="nav-link active" data-page="dashboard">
                        <span class="nav-icon"><i class="fas fa-tachometer-alt"></i></span>
                        <span class="nav-text">工作台</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="tickets">
                        <span class="nav-icon"><i class="fas fa-ticket-alt"></i></span>
                        <span class="nav-text">工单管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="chat">
                        <span class="nav-icon"><i class="fas fa-comments"></i></span>
                        <span class="nav-text">在线客服</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="customers">
                        <span class="nav-icon"><i class="fas fa-users"></i></span>
                        <span class="nav-text">客户管理</span>
                    </a>
                </li>
            </ul>
        </aside>
        
        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="header">
                <h2 class="page-title" id="pageTitle">客服工作台</h2>
                
                <div class="header-actions">
                    <div class="cs-info">
                        <div class="cs-avatar" id="csInitial">C</div>
                        <span class="cs-name" id="csName">客服</span>
                    </div>
                    
                    <button class="logout-btn" id="logoutBtn">
                        <span class="logout-icon"><i class="fas fa-sign-out-alt"></i></span>
                        退出
                    </button>
                </div>
            </div>
            
            <!-- 工作台内容 -->
            <div id="dashboardContent" class="page-content active">
                <div class="dashboard-cards">
                    <div class="card stat-card">
                        <div class="stat-header">
                            <div class="stat-title">待处理工单</div>
                            <div class="stat-icon"><i class="fas fa-clock"></i></div>
                        </div>
                        <div class="stat-value" id="pendingTickets">0</div>
                    </div>
                    
                    <div class="card stat-card">
                        <div class="stat-header">
                            <div class="stat-title">今日已处理</div>
                            <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                        </div>
                        <div class="stat-value" id="resolvedToday">0</div>
                    </div>
                    
                    <div class="card stat-card">
                        <div class="stat-header">
                            <div class="stat-title">在线用户</div>
                            <div class="stat-icon"><i class="fas fa-user-circle"></i></div>
                        </div>
                        <div class="stat-value" id="onlineUsers">0</div>
                    </div>
                </div>
                
                <div class="card">
                    <h3 style="margin-bottom: 15px; color: #0c4da2;">最新工单</h3>
                    <ul class="tickets-list" id="recentTickets">
                        <li class="ticket-item">
                            <div class="ticket-status status-new"></div>
                            <div class="ticket-info">
                                <div class="ticket-title">订单物流查询</div>
                                <div class="ticket-meta">
                                    <span>用户ID: U12345</span> | 
                                    <span>提交时间: 2023-07-08 14:30</span>
                                </div>
                            </div>
                            <div class="ticket-actions">
                                <button class="btn-view">查看</button>
                                <button class="btn-reply">回复</button>
                            </div>
                        </li>
                        
                        <li class="ticket-item">
                            <div class="ticket-status status-processing"></div>
                            <div class="ticket-info">
                                <div class="ticket-title">退款申请处理</div>
                                <div class="ticket-meta">
                                    <span>用户ID: U12346</span> | 
                                    <span>提交时间: 2023-07-08 13:15</span>
                                </div>
                            </div>
                            <div class="ticket-actions">
                                <button class="btn-view">查看</button>
                                <button class="btn-reply">回复</button>
                            </div>
                        </li>
                        
                        <li class="ticket-item">
                            <div class="ticket-status status-resolved"></div>
                            <div class="ticket-info">
                                <div class="ticket-title">商品信息咨询</div>
                                <div class="ticket-meta">
                                    <span>用户ID: U12347</span> | 
                                    <span>提交时间: 2023-07-08 10:45</span> |
                                    <span>已解决</span>
                                </div>
                            </div>
                            <div class="ticket-actions">
                                <button class="btn-view">查看</button>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 其他页面内容 -->
            <div id="ticketsContent" class="page-content" style="display:none;">
                <div class="card">
                    <h3>工单管理</h3>
                    <p>在这里可以管理所有客户工单。</p>
                </div>
            </div>
            
            <div id="chatContent" class="page-content" style="display:none;">
                <div class="card">
                    <h3>在线客服</h3>
                    <p>在这里可以与用户进行实时交流。</p>
                </div>
            </div>
            
            <div id="customersContent" class="page-content" style="display:none;">
                <div class="card">
                    <h3>客户管理</h3>
                    <p>在这里可以查看和管理客户信息。</p>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查客服登录状态
            const checkCSAuth = () => {
                const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');
                
                if (!adminData.isCustomerService) {
                    // 不是客服账号，重定向到登录页
                    window.location.href = 'admin-login.html';
                    return null;
                }
                
                return adminData;
            };
            
            // 初始化页面
            const initPage = () => {
                const csData = checkCSAuth();
                if (!csData) return;
                
                // 设置客服信息
                const csInitial = document.getElementById('csInitial');
                const csName = document.getElementById('csName');
                
                if (csData.username) {
                    csName.textContent = csData.name || csData.username;
                    csInitial.textContent = csData.name ? 
                        csData.name.charAt(0).toUpperCase() : 
                        csData.username.charAt(0).toUpperCase();
                }
                
                // 设置菜单切换
                setupMenuNavigation();
                
                // 加载客服数据
                loadCSData();
            };
            
            // 设置菜单导航
            const setupMenuNavigation = () => {
                const navLinks = document.querySelectorAll('.nav-link');
                
                navLinks.forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        
                        // 获取目标页面
                        const targetPage = this.getAttribute('data-page');
                        
                        // 更新导航菜单高亮
                        navLinks.forEach(link => {
                            link.classList.remove('active');
                        });
                        this.classList.add('active');
                        
                        // 隐藏所有内容
                        document.querySelectorAll('.page-content').forEach(content => {
                            content.style.display = 'none';
                        });
                        
                        // 显示目标内容
                        document.getElementById(`${targetPage}Content`).style.display = 'block';
                        
                        // 更新页面标题
                        const pageTitle = document.getElementById('pageTitle');
                        if (targetPage === 'dashboard') {
                            pageTitle.textContent = '客服工作台';
                        } else if (targetPage === 'tickets') {
                            pageTitle.textContent = '工单管理';
                        } else if (targetPage === 'chat') {
                            pageTitle.textContent = '在线客服';
                        } else if (targetPage === 'customers') {
                            pageTitle.textContent = '客户管理';
                        }
                    });
                });
            };
            
            // 加载客服数据
            const loadCSData = () => {
                // 这里将来可以从API获取真实数据
                // 现在先使用模拟数据
                document.getElementById('pendingTickets').textContent = '12';
                document.getElementById('resolvedToday').textContent = '8';
                document.getElementById('onlineUsers').textContent = '24';
            };
            
                            // 设置退出登录
                document.getElementById('logoutBtn').addEventListener('click', function() {
                    // 清除登录信息
                    sessionStorage.removeItem('loggedInAdmin');
                    // 清除欢迎标记
                    sessionStorage.removeItem('welcomeShown');
                    // 跳转到客服登录页面
                    window.location.href = 'customer-service-login.html';
                });
            
            // 初始化页面
            initPage();
        });
    </script>
</body>
</html> 