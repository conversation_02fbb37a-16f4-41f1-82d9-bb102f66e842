<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试搜索功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>商品分类关联功能测试</h1>
        
        <div class="test-section">
            <h3>1. 测试获取所有商品</h3>
            <button class="test-button" onclick="testGetAllProducts()">获取所有商品</button>
            <div id="allProductsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 测试按关键词搜索</h3>
            <button class="test-button" onclick="testSearchByKeyword('红木家具')">搜索"红木家具"(有商品)</button>
            <button class="test-button" onclick="testSearchByKeyword('儿童家具')">搜索"儿童家具"(无商品)</button>
            <button class="test-button" onclick="testSearchByKeyword('不存在的关键词')">搜索"不存在的关键词"</button>
            <div id="keywordSearchResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 测试按分类搜索</h3>
            <button class="test-button" onclick="testSearchByCategory('1753523684066')">搜索"家具"分类(有商品)</button>
            <button class="test-button" onclick="testSearchByCategory('1753525037752')">搜索"家1"分类(无商品)</button>
            <div id="categorySearchResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 测试通用搜索</h3>
            <button class="test-button" onclick="testGeneralSearch('红木')">通用搜索"红木"</button>
            <button class="test-button" onclick="testGeneralSearch('家具')">通用搜索"家具"</button>
            <div id="generalSearchResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. 页面跳转测试</h3>
            <button class="test-button" onclick="testPageNavigation()">测试分类页面跳转</button>
            <button class="test-button" onclick="testKeywordNavigation()">测试关键词跳转(有商品)</button>
            <button class="test-button" onclick="testNoResultNavigation()">测试关键词跳转(无商品)</button>
        </div>
    </div>

    <script>
        async function testGetAllProducts() {
            try {
                const response = await fetch('/api/products');
                const data = await response.json();
                document.getElementById('allProductsResult').textContent = 
                    `成功获取 ${data.products?.length || 0} 个商品:\n` + 
                    JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('allProductsResult').textContent = 
                    `错误: ${error.message}`;
            }
        }

        async function testSearchByKeyword(keyword) {
            try {
                const response = await fetch(`/api/products?keyword=${encodeURIComponent(keyword)}`);
                const data = await response.json();
                document.getElementById('keywordSearchResult').textContent = 
                    `关键词"${keyword}"搜索结果 (${data.products?.length || 0} 个商品):\n` + 
                    JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('keywordSearchResult').textContent = 
                    `错误: ${error.message}`;
            }
        }

        async function testSearchByCategory(categoryId) {
            try {
                const response = await fetch(`/api/products?category=${categoryId}`);
                const data = await response.json();
                document.getElementById('categorySearchResult').textContent = 
                    `分类"${categoryId}"搜索结果 (${data.products?.length || 0} 个商品):\n` + 
                    JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('categorySearchResult').textContent = 
                    `错误: ${error.message}`;
            }
        }

        async function testGeneralSearch(searchTerm) {
            try {
                const response = await fetch(`/api/products?search=${encodeURIComponent(searchTerm)}`);
                const data = await response.json();
                document.getElementById('generalSearchResult').textContent = 
                    `通用搜索"${searchTerm}"结果 (${data.products?.length || 0} 个商品):\n` + 
                    JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('generalSearchResult').textContent = 
                    `错误: ${error.message}`;
            }
        }

        function testPageNavigation() {
            // 测试跳转到分类页面
            window.open('/前端/product-categories-display.html', '_blank');
        }

        function testKeywordNavigation() {
            // 测试跳转到商品页面并搜索关键词(有商品)
            window.open('/前端/recommend.html?search=红木家具', '_blank');
        }

        function testNoResultNavigation() {
            // 测试跳转到商品页面并搜索关键词(无商品)
            window.open('/前端/recommend.html?search=儿童家具', '_blank');
        }
    </script>
</body>
</html>
