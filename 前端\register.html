<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .page-title {
            color: #0c4da2;
            font-size: 32px;
            margin-bottom: 0;
        }
        
        .gold {
            color: #D4AF37;
        }
        
        .back-button {
            display: inline-block;
            color: #0c4da2;
            text-decoration: none;
            font-size: 16px;
            background-color: #fff;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background-color: #f5f5f5;
        }
        
        .register-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .register-options {
            display: flex;
            flex-direction: column;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .option-title {
            color: #0c4da2;
            font-size: 24px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        .register-methods {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .register-method {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 45%;
            min-width: 200px;
            padding: 30px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }
        
        .method-wechat {
            background-color: #f5f5f5;
            border: 2px solid #07C160;
        }
        
        .method-wechat:hover {
            background-color: #e6f7ef;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(7, 193, 96, 0.2);
        }
        
        .method-email {
            background-color: #f5f5f5;
            border: 2px solid #0c4da2;
        }
        
        .method-email:hover {
            background-color: #e6eff9;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(12, 77, 162, 0.2);
        }
        
        .method-icon {
            font-size: 50px;
            margin-bottom: 15px;
        }
        
        .method-wechat .method-icon {
            color: #07C160;
        }
        
        .method-email .method-icon {
            color: #0c4da2;
        }
        
        .method-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .method-description {
            font-size: 14px;
            text-align: center;
            color: #666;
        }
        
        .divider {
            display: flex;
            align-items: center;
            margin: 30px 0;
        }
        
        .divider-line {
            flex-grow: 1;
            height: 1px;
            background-color: #ddd;
        }
        
        .divider-text {
            padding: 0 15px;
            color: #888;
            font-size: 14px;
        }
        
        .email-form {
            display: none;
            flex-direction: column;
            margin-top: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #0c4da2;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            border-color: #0c4da2;
            outline: none;
        }
        
        /* 验证码输入框和按钮的样式 */
        .verification-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .verification-group input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .verification-group input:focus {
            border-color: #0c4da2;
            outline: none;
        }
        
        .send-code-btn {
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 0 15px;
            border-radius: 5px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 120px;
        }
        
        .send-code-btn:hover {
            background-color: #083778;
        }
        
        .send-code-btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        
        .submit-button {
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .submit-button:hover {
            background-color: #083778;
        }
        
        .login-link {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
        }
        
        .login-link a {
            color: #0c4da2;
            text-decoration: none;
            font-weight: bold;
        }
        
        .login-link a:hover {
            text-decoration: underline;
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-container">
                <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="page-title">用户注册</h1>
            </div>
            <a href="login.html" class="back-button">返回登录</a>
        </div>
        
        <div class="register-container">
            <div class="option-title">选择注册方式</div>
            
            <div class="register-methods">
                <a href="#" class="register-method method-wechat" onclick="hideEmailForm(); alert('正在跳转到微信二维码扫描页面...')">
                    <div class="method-icon">
                        <i class="fab fa-weixin"></i>
                    </div>
                    <div class="method-title">微信注册</div>
                    <div class="method-description">使用微信扫描二维码快速注册账号</div>
                </a>
                
                <a href="email-register.html" class="register-method method-email">
                    <div class="method-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="method-title">邮箱注册</div>
                    <div class="method-description">使用您的电子邮箱注册新账号</div>
                </a>
            </div>
            
            <div id="emailForm" class="email-form">
                <div class="divider">
                    <div class="divider-line"></div>
                    <div class="divider-text">邮箱注册</div>
                    <div class="divider-line"></div>
                </div>
                
                <form>
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" placeholder="请设置用户名" maxlength="10" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">电子邮箱</label>
                        <input type="email" id="email" name="email" placeholder="请输入您的邮箱" maxlength="320" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="verificationCode">邮箱验证码</label>
                        <div class="verification-group">
                            <input type="text" id="verificationCode" name="verificationCode" placeholder="请输入验证码" maxlength="6" required>
                            <button type="button" id="sendCodeBtn" class="send-code-btn">发送验证码</button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" placeholder="请设置密码" maxlength="64" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirmPassword">确认密码</label>
                        <input type="password" id="confirmPassword" name="confirmPassword" placeholder="请再次输入密码" maxlength="64" required>
                    </div>
                    
                    <button type="submit" class="submit-button">注册账号</button>
                </form>
            </div>
            
            <div class="login-link">
                已有账号？<a href="login.html">立即登录</a>
            </div>
        </div>
        
        <footer>
            <p>&copy; 2023 金舟国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </footer>
    </div>
    
    <script>
        function showEmailForm() {
            const emailForm = document.getElementById('emailForm');
            emailForm.style.display = 'flex';
            // Scroll to the form
            emailForm.scrollIntoView({ behavior: 'smooth' });
        }
        
        // Add function to hide email form when clicking on WeChat option
        function hideEmailForm() {
            const emailForm = document.getElementById('emailForm');
            emailForm.style.display = 'none';
        }
        
        // 监听所有设置了maxlength属性的输入框
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('input[maxlength]').forEach(input => {
                // 记录上一次显示提示的时间
                let lastAlertTime = 0;

                // 使用keydown事件，可以捕获到用户尝试输入但被maxlength阻止的情况
                input.addEventListener('keydown', function(e) {
                    const maxLength = parseInt(this.getAttribute('maxlength'));
                    const currentTime = new Date().getTime();
                    
                    // 如果已经达到最大长度且不是功能键（如退格键、方向键等）
                    if (this.value.length >= maxLength && 
                        !e.ctrlKey && !e.altKey && 
                        e.key.length === 1 && 
                        currentTime - lastAlertTime > 2000) { // 限制提示频率，至少间隔2秒
                        
                        alert(`输入已达到最大限制${maxLength}个字符`);
                        lastAlertTime = currentTime;
                    }
                });
                
                // 保留input事件监听，用于处理粘贴等操作
                input.addEventListener('input', function(e) {
                    const maxLength = parseInt(this.getAttribute('maxlength'));
                    const currentTime = new Date().getTime();
                    
                    if (this.value.length >= maxLength && currentTime - lastAlertTime > 2000) {
                        alert(`输入已达到最大限制${maxLength}个字符`);
                        lastAlertTime = currentTime;
                    }
                });
            });
        });
        
        // 验证码发送功能
        document.addEventListener('DOMContentLoaded', function() {
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            const emailInput = document.getElementById('email');
            let currentTimer = null; // 保存当前计时器引用

            if (sendCodeBtn && emailInput) {
                sendCodeBtn.addEventListener('click', function() {
                    const email = emailInput.value.trim();

                    // 验证邮箱格式
                    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailPattern.test(email)) {
                        alert('请输入有效的邮箱地址');
                        emailInput.focus();
                        return;
                    }

                    // 如果已有计时器在运行，先清除
                    if (currentTimer) {
                        clearInterval(currentTimer);
                    }

                    // 禁用按钮并开始倒计时
                    let countdown = 60;
                    sendCodeBtn.disabled = true;
                    sendCodeBtn.textContent = `${countdown}秒后重新发送`;

                    currentTimer = setInterval(function() {
                        countdown--;
                        if (sendCodeBtn) { // 确保元素仍然存在
                            sendCodeBtn.textContent = `${countdown}秒后重新发送`;

                            if (countdown <= 0) {
                                clearInterval(currentTimer);
                                currentTimer = null;
                                sendCodeBtn.disabled = false;
                                sendCodeBtn.textContent = '发送验证码';
                            }
                        } else {
                            clearInterval(currentTimer);
                            currentTimer = null;
                        }
                    }, 1000);

                    // 模拟发送验证码
                    alert(`验证码已发送至邮箱: ${email}\n(这是一个模拟，实际验证码为: 123456)`);
                });
            }
            
            // 表单提交验证
            const registerForm = document.querySelector('.email-form form');
            if (registerForm) {
                registerForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const username = document.getElementById('username').value.trim();
                    const email = document.getElementById('email').value.trim();
                    const verificationCode = document.getElementById('verificationCode').value.trim();
                    const password = document.getElementById('password').value;
                    const confirmPassword = document.getElementById('confirmPassword').value;
                    
                    // 验证用户名
                    if (username.length < 3) {
                        alert('用户名至少需要3个字符');
                        return;
                    }
                    
                    if (username.length > 10) {
                        alert('用户名最多10个字符');
                        return;
                    }
                    
                    // 验证邮箱
                    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailPattern.test(email)) {
                        alert('请输入有效的邮箱地址');
                        return;
                    }
                    
                    // 验证验证码
                    if (verificationCode !== '123456') { // 模拟验证码检查
                        alert('验证码错误');
                        return;
                    }
                    
                    if (verificationCode.length > 6) {
                        alert('验证码最多6个字符');
                        return;
                    }
                    
                    // 验证密码
                    const passwordPattern = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,64}$/;
                    if (!passwordPattern.test(password)) {
                        alert('密码必须为8-64位，且同时包含英文字母和数字');
                        return;
                    }
                    
                    // 验证两次密码是否一致
                    if (password !== confirmPassword) {
                        alert('两次输入的密码不一致');
                        return;
                    }
                    
                    // 模拟注册成功
                    alert('注册成功！即将跳转到登录页面...');
                    window.location.href = 'login.html';
                });
            }
        });
    </script>
</body>
</html> 