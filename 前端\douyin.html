<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正在跳转到金舟国际物流官方抖音</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            padding: 40px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #0c4da2;
            font-size: 28px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .gold {
            color: #D4AF37;
        }
        
        .douyin-icon {
            width: 80px;
            height: 80px;
            margin: 20px auto;
            background: linear-gradient(to bottom right, #00f2ea, #ff0050);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }
        
        p {
            margin: 15px 0;
            color: #666;
        }
        
        .loading {
            display: inline-block;
            width: 50px;
            height: 50px;
            border: 5px solid rgba(0, 242, 234, 0.2);
            border-radius: 50%;
            border-top-color: #ff0050;
            animation: spin 1s ease-in-out infinite;
            margin: 20px 0;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .direct-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background: linear-gradient(to right, #00f2ea, #ff0050);
            color: #fff;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .direct-link:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }
        
        .back-btn {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #0c4da2;
            color: #fff;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        
        .back-btn:hover {
            background-color: #083778;
        }
        
        #countdown {
            font-weight: bold;
            background: linear-gradient(to right, #00f2ea, #ff0050);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    </style>
    <script>
        // 页面加载后执行跳转倒计时
        window.onload = function() {
            let seconds = 3;
            const countdownElement = document.getElementById('countdown');
            const timer = setInterval(function() {
                seconds--;
                countdownElement.textContent = seconds;
                if (seconds <= 0) {
                    clearInterval(timer);
                    window.location.href = 'https://v.douyin.com/biwJSENdiW8/';
                }
            }, 1000);
        };
    </script>
</head>
<body>
    <div class="container">
        <h1>
            <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
            <span><span class="gold">金舟</span>国际物流</span>
        </h1>
        <div class="douyin-icon">抖</div>
        <p>正在跳转到我们的官方抖音账号...</p>
        <p>页面将在 <span id="countdown">3</span> 秒后自动跳转</p>
        <div class="loading"></div>
        <p>如果页面没有自动跳转，请点击下方链接：</p>
        <a href="https://v.douyin.com/biwJSENdiW8/" class="direct-link" target="_blank" rel="noopener noreferrer">前往抖音账号</a>
        <br>
        <a href="main.html" class="back-btn">返回官网</a>
    </div>
</body>
</html> 