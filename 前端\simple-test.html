<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0c4da2;
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:disabled {
            background-color: #cccccc;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .countdown {
            font-size: 14px;
            color: #666;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Email Verification Test</h1>
        
        <div class="form-group">
            <label for="email">Email Address:</label>
            <input type="email" id="email" placeholder="Enter your email">
        </div>
        
        <div class="form-group">
            <label for="code">Verification Code:</label>
            <div style="display: flex; gap: 10px;">
                <input type="text" id="code" placeholder="Enter verification code">
                <button id="sendBtn">Send Code</button>
                <span id="countdown" class="countdown"></span>
            </div>
        </div>
        
        <button id="verifyBtn">Verify Code</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const sendBtn = document.getElementById('sendBtn');
        const verifyBtn = document.getElementById('verifyBtn');
        const emailInput = document.getElementById('email');
        const codeInput = document.getElementById('code');
        const resultDiv = document.getElementById('result');
        const countdownSpan = document.getElementById('countdown');
        
        let countdown = 0;
        const countdownInterval = 1000; // 1 second
        const countdownDuration = 60; // 60 seconds
        
        function startCountdown() {
            sendBtn.disabled = true;
            countdown = countdownDuration;
            updateCountdown();
            
            const timer = setInterval(() => {
                countdown--;
                updateCountdown();
                
                if (countdown <= 0) {
                    clearInterval(timer);
                    sendBtn.disabled = false;
                    countdownSpan.textContent = '';
                }
            }, countdownInterval);
        }
        
        function updateCountdown() {
            countdownSpan.textContent = `(${countdown}s)`;
        }
        
        function showResult(message, isSuccess) {
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
            resultDiv.className = isSuccess ? 'result success' : 'result error';
            
            // Auto hide after 5 seconds
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 5000);
        }
        
        sendBtn.addEventListener('click', async () => {
            const email = emailInput.value.trim();
            
            if (!email) {
                showResult('Please enter your email address', false);
                return;
            }
            
                         try {
                const response = await fetch(`http://*************:8080/send?email=${encodeURIComponent(email)}`);
                const result = await response.text();
                
                showResult(result, true);
                startCountdown();
            } catch (error) {
                showResult(`Error: ${error.message}`, false);
            }
        });
        
        verifyBtn.addEventListener('click', async () => {
            const email = emailInput.value.trim();
            const code = codeInput.value.trim();
            
            if (!email || !code) {
                showResult('Please enter both email and verification code', false);
                return;
            }
            
                         try {
                const response = await fetch(`http://*************:8080/verify?email=${encodeURIComponent(email)}&code=${encodeURIComponent(code)}`);
                const result = await response.text();
                
                showResult(result, result === 'Verification successful');
                
                if (result === 'Verification successful') {
                    // Clear code input on success
                    codeInput.value = '';
                }
            } catch (error) {
                showResult(`Error: ${error.message}`, false);
            }
        });
    </script>
</body>
</html> 