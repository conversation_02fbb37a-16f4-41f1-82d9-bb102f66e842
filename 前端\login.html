<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/blacklist-handler.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .page-title {
            color: #0c4da2;
            font-size: 32px;
            margin-bottom: 0;
        }
        
        .gold {
            color: #D4AF37;
        }
        
        .back-button {
            display: inline-block;
            color: #0c4da2;
            text-decoration: none;
            font-size: 16px;
            background-color: #fff;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background-color: #f5f5f5;
        }
        
        .login-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .login-tabs {
            display: flex;
            margin-bottom: 25px;
            border-bottom: 1px solid #eee;
        }
        
        .login-tab {
            flex: 1;
            text-align: center;
            padding: 10px 0;
            font-size: 16px;
            font-weight: bold;
            color: #666;
            cursor: pointer;
            position: relative;
        }
        
        .login-tab.active {
            color: #0c4da2;
        }
        
        .login-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #0c4da2;
        }
        
        .login-form {
            display: flex;
            flex-direction: column;
        }
        
        .login-methods {
            display: none;
        }
        
        .login-methods.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #0c4da2;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            border-color: #0c4da2;
            outline: none;
        }
        
        .submit-button {
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .submit-button:hover {
            background-color: #083778;
        }
        
        .wechat-login {
            text-align: center;
            padding: 20px 0;
        }
        
        .wechat-qrcode {
            display: inline-block;
            background-color: #f5f5f5;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .wechat-icon {
            font-size: 100px;
            color: #07C160;
            margin-bottom: 15px;
        }
        
        .wechat-text {
            color: #666;
            font-size: 16px;
            margin-bottom: 10px;
        }
        

        .register-link {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .register-link a {
            color: #0c4da2;
            text-decoration: none;
            font-weight: bold;
        }
        
        .register-link a:hover {
            text-decoration: underline;
        }
        
        .forgot-password {
            text-align: right;
            margin-top: 5px;
            font-size: 14px;
        }
        
        .forgot-password a {
            color: #0c4da2;
            text-decoration: none;
        }
        
        .forgot-password a:hover {
            text-decoration: underline;
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            color: #666;
            font-size: 14px;
        }
        
        /* 优雅的通知系统 */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            max-width: 350px;
        }
        
        .toast {
            display: flex;
            align-items: center;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            margin-bottom: 15px;
            padding: 12px 20px;
            overflow: hidden;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        .toast.toast-success {
            border-left: 5px solid #28a745;
        }
        
        .toast.toast-error {
            border-left: 5px solid #dc3545;
        }
        
        .toast.toast-info {
            border-left: 5px solid #17a2b8;
        }
        
        .toast.toast-warning {
            border-left: 5px solid #ffc107;
        }
        
        .toast-icon {
            margin-right: 15px;
            font-size: 20px;
        }
        
        .toast-success .toast-icon {
            color: #28a745;
        }
        
        .toast-error .toast-icon {
            color: #dc3545;
        }
        
        .toast-info .toast-icon {
            color: #17a2b8;
        }
        
        .toast-warning .toast-icon {
            color: #ffc107;
        }
        
        .toast-content {
            flex: 1;
        }
        
        .toast-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .toast-message {
            font-size: 14px;
            color: #666;
        }
        
        .toast-close {
            color: #999;
            font-size: 18px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .toast-close:hover {
            color: #333;
        }
        
        .toast-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background-color: rgba(0, 0, 0, 0.1);
            width: 100%;
        }
        
        .toast-progress-bar {
            height: 3px;
            width: 100%;
        }
        
        .toast-success .toast-progress-bar {
            background-color: #28a745;
        }
        
        .toast-error .toast-progress-bar {
            background-color: #dc3545;
        }
        
        .toast-info .toast-progress-bar {
            background-color: #17a2b8;
        }
        
        .toast-warning .toast-progress-bar {
            background-color: #ffc107;
        }
        
        /* 模态对话框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background-color: #fff;
            margin: 15% auto;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
            position: relative;
            max-width: 420px;
            transform: scale(0.8);
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .modal.show .modal-content {
            transform: scale(1);
            opacity: 1;
        }
        
        .modal-title {
            color: #0c4da2;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
            font-size: 20px;
        }
        
        .modal-text {
            margin-bottom: 25px;
            text-align: center;
            color: #555;
            font-size: 16px;
        }
        
        .modal-btn {
            display: block;
            width: 100%;
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .modal-btn:hover {
            background-color: #083778;
        }
        
        .modal-icon {
            display: block;
            text-align: center;
            font-size: 48px;
            margin-bottom: 20px;
            color: #0c4da2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-container">
                <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img" id="logo-img">
                <h1 class="page-title">用户登录</h1>
            </div>
            <a href="main.html" class="back-button">返回首页</a>
        </div>
        
        <div class="login-container">
            <div class="login-tabs">
                <div class="login-tab active" onclick="switchTab('account')">账号密码登录</div>
                <div class="login-tab" onclick="switchTab('wechat')">微信登录</div>
            </div>
            
            <div id="accountLogin" class="login-methods active">
                <form class="login-form" id="loginForm">
                    <div class="form-group">
                        <label for="username">用户名/邮箱</label>
                        <input type="text" id="username" name="username" placeholder="请输入用户名或邮箱" maxlength="320" autocomplete="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" placeholder="请输入密码" maxlength="64" autocomplete="current-password" required>
                        <div class="forgot-password">
                            <a href="forgot-password.html">忘记密码？</a>
                        </div>
                    </div>
                    
                    <button type="submit" class="submit-button" id="submitBtn">登录</button>
                </form>
            </div>
            
            <div id="wechatLogin" class="login-methods">
                <div class="wechat-login">
                    <div class="wechat-qrcode">
                        <i class="fab fa-weixin wechat-icon"></i>
                    </div>
                    <div class="wechat-text">请使用微信扫描二维码登录</div>
                    <div class="wechat-text">或在微信中搜索"金舟国际物流"小程序</div>
                </div>
            </div>
            
            <div class="register-link">
                还没有账号？<a href="register.html">立即注册</a>
            </div>
        </div>
        
        <!-- 错误信息容器 -->
        <div id="errorContainer" style="display: none; background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin-top: 10px; text-align: center;">
            <p id="errorMessage"></p>
        </div>

        <footer>
            <p>&copy; 2023 <span id="jinzhou-text">金舟</span>国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </footer>
    </div>
    
    <!-- 模态弹窗 -->
    <div id="message-modal" class="modal">
        <div class="modal-content">
            <div class="modal-icon" id="modal-icon"><i class="fas fa-check-circle"></i></div>
            <h3 class="modal-title" id="modal-title"></h3>
            <p class="modal-text" id="modal-text"></p>
            <button class="modal-btn" id="modal-btn">确定</button>
        </div>
    </div>
    
    <!-- 优雅提示的容器 -->
    <div class="toast-container" id="toast-container"></div>

    <script>
        // API地址 - 后端服务地址
        const API_BASE_URL = 'http://localhost:8080';

        // 添加logo点击计数器
        let logoClickCount = 0;
        const logoImg = document.getElementById('logo-img');
        
        if(logoImg) {
            logoImg.addEventListener('click', function() {
                logoClickCount++;
                if(logoClickCount === 5) {
                    // 重置计数器
                    logoClickCount = 0;
                    // 跳转到客服登录页面
                    window.location.href = 'customer-service-login.html';
                }
            });
        }

        // 监听所有设置了maxlength属性的输入框
        document.addEventListener('DOMContentLoaded', function() {
            
            document.querySelectorAll('input[maxlength]').forEach(input => {
                // 记录上一次显示提示的时间
                let lastToastTime = 0;

                // 使用keydown事件，可以捕获到用户尝试输入但被maxlength阻止的情况
                input.addEventListener('keydown', function(e) {
                    const maxLength = parseInt(this.getAttribute('maxlength'));
                    const currentTime = new Date().getTime();
                    
                    // 如果已经达到最大长度且不是功能键（如退格键、方向键等）
                    if (this.value.length >= maxLength && 
                        !e.ctrlKey && !e.altKey && 
                        e.key.length === 1 && 
                        currentTime - lastToastTime > 1000) { // 限制提示频率，至少间隔1秒
                        
                        showToast(`输入已达到最大限制${maxLength}个字符`, 'warning', '字符限制');
                        lastToastTime = currentTime;
                    }
                });
                
                // 保留input事件监听，用于处理粘贴等操作
                input.addEventListener('input', function(e) {
                    const maxLength = parseInt(this.getAttribute('maxlength'));
                    const currentTime = new Date().getTime();
                    
                    if (this.value.length >= maxLength && currentTime - lastToastTime > 1000) {
                        showToast(`输入已达到最大限制${maxLength}个字符`, 'warning', '字符限制');
                        lastToastTime = currentTime;
                    }
                });
            });

            // 检查用户是否被拉黑
            BlacklistHandler.checkUserBlacklisted();
        });

        // 显示模态弹窗的函数
        function showModal(title, text, callback, type = 'success') {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-text').textContent = text;
            
            // 设置图标
            const modalIcon = document.getElementById('modal-icon');
            if (type === 'success') {
                modalIcon.innerHTML = '<i class="fas fa-check-circle" style="color: #28a745;"></i>';
            } else if (type === 'error') {
                modalIcon.innerHTML = '<i class="fas fa-times-circle" style="color: #dc3545;"></i>';
            } else if (type === 'info') {
                modalIcon.innerHTML = '<i class="fas fa-info-circle" style="color: #17a2b8;"></i>';
            } else if (type === 'warning') {
                modalIcon.innerHTML = '<i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>';
            }
            
            const modal = document.getElementById('message-modal');
            modal.style.display = 'block';
            
            // 添加显示类以触发动画
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
            
            document.getElementById('modal-btn').onclick = function() {
                modal.classList.remove('show');
                
                // 等待动画完成后隐藏
                setTimeout(() => {
                    modal.style.display = 'none';
                    if (callback) callback();
                }, 300);
            };
        }

        // 显示优雅的toast通知
        function showToast(message, type = 'info', title = '', duration = 5000) {
            const container = document.getElementById('toast-container');
            
            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            
            // 设置图标
            let iconClass = 'info-circle';
            if (type === 'success') iconClass = 'check-circle';
            if (type === 'error') iconClass = 'times-circle';
            if (type === 'warning') iconClass = 'exclamation-triangle';
            
            // 构建toast内容
            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="fas fa-${iconClass}"></i>
                </div>
                <div class="toast-content">
                    ${title ? `<div class="toast-title">${title}</div>` : ''}
                    <div class="toast-message">${message}</div>
                </div>
                <div class="toast-close">
                    <i class="fas fa-times"></i>
                </div>
                <div class="toast-progress">
                    <div class="toast-progress-bar"></div>
                </div>
            `;
            
            // 添加到容器
            container.appendChild(toast);
            
            // 触发动画
            setTimeout(() => {
                toast.classList.add('show');
                
                // 设置进度条动画
                const progressBar = toast.querySelector('.toast-progress-bar');
                progressBar.style.transition = `width ${duration}ms linear`;
                progressBar.style.width = '0';
            }, 10);
            
            // 设置关闭事件
            const closeBtn = toast.querySelector('.toast-close');
            closeBtn.addEventListener('click', () => {
                removeToast(toast);
            });
            
            // 自动关闭
            const closeTimeout = setTimeout(() => {
                removeToast(toast);
            }, duration);
            
            // 移除Toast的函数
            function removeToast(toast) {
                clearTimeout(closeTimeout);
                toast.classList.remove('show');
                
                // 动画结束后移除DOM
                setTimeout(() => {
                    if (container.contains(toast)) {
                        container.removeChild(toast);
                    }
                }, 300);
            }
        }

        // API请求函数
        async function callAPI(endpoint, data, method = 'POST') {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };
                
                if (method === 'POST') {
                    options.body = JSON.stringify(data);
                } else if (method === 'GET' && data) {
                    endpoint = `${endpoint}?${new URLSearchParams(data)}`;
                }
                
                const url = `${API_BASE_URL}/api/${endpoint}`;
                const response = await fetch(url, options);
                const result = await response.json();
                return result;
            } catch (error) {
                console.error('API请求失败:', error);
                return { success: false, message: '服务器连接失败，请检查后端服务是否运行' };
            }
        }

        function switchTab(tab) {
            // Remove active class from all tabs and content
            document.querySelectorAll('.login-tab').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.login-methods').forEach(item => {
                item.classList.remove('active');
            });
            
            // Add active class to selected tab and content
            if (tab === 'account') {
                document.querySelector('.login-tab:nth-child(1)').classList.add('active');
                document.getElementById('accountLogin').classList.add('active');
            } else if (tab === 'wechat') {
                document.querySelector('.login-tab:nth-child(2)').classList.add('active');
                document.getElementById('wechatLogin').classList.add('active');
            }
        }

        // 用户登录功能 - 只保留一个登录处理逻辑
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const usernameInput = document.getElementById('username');
                const passwordInput = document.getElementById('password');

                if (!usernameInput || !passwordInput) {
                    showToast('登录表单元素未找到，请刷新页面重试', 'error', '系统错误');
                    return;
                }

                const username = usernameInput.value.trim();
                const password = passwordInput.value.trim();

                // 基本验证
                if (!username) {
                    showToast('请输入用户名或邮箱', 'warning', '验证失败');
                    usernameInput.focus();
                    return;
                }

                if (!password) {
                    showToast('请输入密码', 'warning', '验证失败');
                    passwordInput.focus();
                    return;
                }

                // 禁用登录按钮，防止重复提交
                const submitButton = document.getElementById('submitBtn');
                const originalBtnText = submitButton.textContent;
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';

                try {
                    // 调用后端API进行登录验证
                    const result = await callAPI('login', { username, password });

                    if (result.success) {
                        // 登录成功，存储用户信息
                        const userData = {
                            username: result.user.username,
                            email: result.user.email || '',
                            id: result.user.id,
                            isLoggedIn: true,
                            loginTime: new Date().toISOString()
                        };

                        sessionStorage.setItem('loggedInUser', JSON.stringify(userData));

                        // 获取URL参数中的returnUrl，如果存在则跳转回原页面
                        const urlParams = new URLSearchParams(window.location.search);
                        const returnUrl = urlParams.get('returnUrl');

                        // 显示成功消息并跳转
                        showModal('登录成功', '欢迎回来！正在跳转...', function() {
                            if (returnUrl) {
                                window.location.href = returnUrl;
                            } else {
                                window.location.href = 'dashboard.html';
                            }
                        }, 'success');
                    } else {
                        // 检查是否因为账号被拉黑
                        if (result.blacklisted) {
                            // 显示被拉黑的提示信息
                            BlacklistHandler.showBlacklistedMessage(result.message || BlacklistHandler.DEFAULT_BLACKLIST_MESSAGE);
                        } else {
                            // 登录失败
                            document.getElementById('errorMessage').textContent = result.message || '用户名或密码错误';
                            document.getElementById('errorContainer').style.display = 'block';
                            showToast(result.message || '用户名或密码错误', 'error', '登录失败');
                            passwordInput.value = ''; // 清空密码字段
                            passwordInput.focus();
                        }
                    }
                } catch (error) {
                    console.error('登录失败:', error);
                    showToast('登录过程中出现错误，请稍后重试', 'error', '系统错误');
                } finally {
                    // 恢复按钮状态
                    submitButton.disabled = false;
                    submitButton.textContent = originalBtnText;
                }
            });
        }
    </script>
</body>
</html> 