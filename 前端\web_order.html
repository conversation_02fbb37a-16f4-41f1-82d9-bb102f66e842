<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网页下单 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/order-limit.js"></script>
    <script src="js/blacklist-handler.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 20px 15px;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 25px;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .page-title {
            color: #0c4da2;
            font-size: 32px;
            margin-bottom: 0;
        }
        
        .back-button {
            display: inline-block;
            color: #0c4da2;
            text-decoration: none;
            font-size: 16px;
            background-color: #fff;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background-color: #f5f5f5;
        }
        
        .order-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin: 15px 0;
        }
        
        .steps-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            position: relative;
        }
        
        .steps-container::before {
            content: '';
            position: absolute;
            height: 2px;
            background-color: #e0e0e0;
            top: 15px;
            left: 0;
            right: 0;
            z-index: 1;
        }
        
        .step {
            position: relative;
            z-index: 2;
            text-align: center;
            width: 50%;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #f0f0f0;
            color: #999;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-weight: bold;
            border: 2px solid #e0e0e0;
        }
        
        .step.active .step-number {
            background-color: #0c4da2;
            color: #fff;
            border-color: #0c4da2;
        }
        
        .step.completed .step-number {
            background-color: #4CAF50;
            color: #fff;
            border-color: #4CAF50;
        }
        
        .step-label {
            font-size: 14px;
            color: #999;
        }
        
        .step.active .step-label {
            color: #0c4da2;
            font-weight: bold;
        }
        
        .step.completed .step-label {
            color: #4CAF50;
        }
        
        .form-container {
            margin-top: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            border-color: #0c4da2;
            outline: none;
        }
        
        .address-form {
            padding-top: 10px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        
        .buttons-container {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #0c4da2;
            color: #fff;
            border: none;
        }
        
        .btn-primary:hover {
            background-color: #083778;
        }
        
        .btn-outline {
            background-color: #fff;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .btn-outline:hover {
            background-color: #f5f5f5;
        }
        
        .required::after {
            content: '*';
            color: #f44336;
            margin-left: 4px;
        }
        
        .saved-addresses-title {
            color: #0c4da2;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .saved-addresses {
            margin-bottom: 30px;
        }
        
        /* 添加滚动条样式，限制高度 */
        #savedAddressesList .saved-addresses {
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 15px;
        }
        
        .address-card {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #f5f9ff;
            position: relative;
        }
        
        /* 添加地址卡片两列布局样式 */
        .address-card-content {
            display: flex;
            flex-direction: column;
        }
        
        .address-top-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .address-left-col {
            flex: 1;
            padding-right: 10px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }
        
        .address-right-col {
            flex: 1;
            padding-left: 10px;
            border-left: 1px dashed #ddd;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }
        
        /* 详细地址行样式 */
        .address-bottom-row {
            border-top: 1px dashed #ddd;
            padding-top: 8px;
            width: 100%;
        }
        
        /* 添加地址字段样式 */
        .address-field {
            display: flex;
            margin-bottom: 5px;
            align-items: flex-start;
        }
        
        .address-field-label {
            font-weight: bold;
            min-width: 80px;
        }
        
        .address-field-value {
            flex: 1;
            word-break: break-all; /* 允许在任意字符间断行 */
            overflow-wrap: break-word; /* 确保长单词也能断行 */
        }
        
        /* 详细地址特殊样式 */
        .address-field.address-detail .address-field-value {
            max-width: none; /* 移除最大宽度限制 */
        }
        
        .address-card strong {
            display: block;
            margin-bottom: 5px;
        }
        
        .address-card div {
            margin-bottom: 3px;
            color: #666;
        }
        
        .address-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
        }
        
        .address-actions button {
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
            font-size: 14px;
            padding: 2px 5px;
            border-radius: 3px;
            transition: all 0.2s;
        }
        
        .address-actions button:hover {
            background-color: #e0e0e0;
            color: #333;
        }
        
        .address-actions .edit-btn {
            color: #0c4da2;
        }
        
        .address-actions .delete-btn {
            color: #f44336;
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            color: #666;
            font-size: 14px;
        }
        
        .address-form h3 {
            color: #0c4da2;
            margin-bottom: 20px;
        }
        
        .address-form h3 i {
            color: #0c4da2;
            margin-right: 8px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        /* 添加浮动弹窗样式 */
        .popup-container {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .popup-content {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            width: 90%;
            max-width: 500px;
            padding: 20px;
            position: relative;
        }
        
        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
        }
        
        .popup-title {
            font-size: 20px;
            font-weight: bold;
            color: #0c4da2;
        }
        
        .popup-close {
            font-size: 22px;
            color: #999;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
        }
        
        .popup-close:hover {
            color: #333;
        }
        
        .popup-footer {
            margin-top: 15px;
            text-align: center;
        }
        
        .btn-add-address {
            background-color: #0c4da2;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s;
        }
        
        .btn-add-address:hover {
            background-color: #083778;
        }
        
        /* 新增地址表单样式 */
        .new-address-form {
            display: none;
        }
        
        .new-address-form .form-group {
            margin-bottom: 6px;
        }
        
        .new-address-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .new-address-form input, 
        .new-address-form select,
        .new-address-form textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        
        .btn-cancel {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 8px 15px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .btn-cancel:hover {
            background-color: #e0e0e0;
        }
        
        .btn-save {
            background-color: #0c4da2;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 15px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .btn-save:hover {
            background-color: #083778;
        }
        
        /* 添加地址卡片选中样式 */
        .address-card.selected {
            border: 2px solid #0c4da2;
            background-color: #e6f0ff;
            box-shadow: 0 0 8px rgba(12, 77, 162, 0.3);
        }
        
        /* 更新样式 */
        .code-image-grid {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }
        .code-image-pair {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background-color: #f9f9f9;
            position: relative;
            width: calc(50% - 20px);
            margin-left: 10px;
            margin-right: 10px;
            box-sizing: border-box;
        }
        .code-section {
            margin-bottom: 15px;
        }
        .image-section {
            padding-top: 10px;
            border-top: 1px dashed #ddd;
        }
        .image-preview {
            max-width: 150px;
            max-height: 150px;
            cursor: pointer;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 3px;
        }
        .delete-pair-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: none;
            border: none;
            color: #f44336;
            cursor: pointer;
            font-size: 16px;
        }
        .delete-pair-btn:hover {
            color: #d32f2f;
        }
        @media (max-width: 768px) {
            .code-image-pair {
                width: 100%;
            }
        }
        /* Hide default file input text */
        input[type="file"] {
            color: transparent;
            position: relative;
            width: auto; /* 恢复为自动宽度 */
            overflow: hidden;
            z-index: 1;
            font-size: 0; /* 添加此行以隐藏文本 */
            padding-right: 0; /* 确保没有额外空间 */
        }
        input[type="file"]::file-selector-button {
            color: #333;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            margin-right: 0; /* 移除右边距 */
            cursor: pointer;
            transition: background-color 0.3s;
            z-index: 2;
            font-size: 14px; /* 恢复按钮文字大小 */
            width: auto; /* 恢复为自动宽度 */
        }
        input[type="file"]::file-selector-button:hover {
            background-color: #e0e0e0;
        }
        /* Completely hide the file name text */
        input[type="file"]::-webkit-file-upload-button {
            visibility: visible;
        }
        input[type="file"]::before {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            left: auto; /* 恢复为自动位置 */
            width: 0; /* 恢复为0宽度 */
            background-color: transparent;
            z-index: 3;
        }
        /* Additional rule to hide text */
        input[type="file"] + span {
            display: none !important;
        }
        /* 添加额外的规则以确保文本不可见 */
        input[type="file"]::after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 0; /* 恢复为0宽度 */
            background-color: transparent;
            z-index: 3;
        }
        /* 修改样式用于显示小缩略图 - 居中显示 */
        .thumbnail-preview {
            display: inline-block;
            vertical-align: middle;
            margin-left: 10px;
            position: absolute;
            left: 70%; /* 从65%修改为70%，将缩略图向右移动一点 */
            transform: translateX(-50%);
            z-index: 10;
        }
        .thumbnail-preview img {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .file-input-container {
            display: flex;
            align-items: center;
            position: relative;
            width: 100%;
        }
        
        /* 自定义下拉菜单样式 */
        .custom-select-wrapper {
            position: relative;
            width: 100%;
        }
        
        .hidden-select {
            display: none;
        }
        
        .custom-select {
            position: relative;
            width: 100%;
            cursor: pointer;
        }
        
        .custom-select-trigger {
            display: block;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            font-size: 14px;
            position: relative;
        }
        
        .custom-select-trigger:after {
            content: "▼";
            font-size: 10px;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.3s;
        }
        
        .custom-select.opened .custom-select-trigger:after {
            transform: translateY(-50%) rotate(180deg);
        }
        
        .custom-options {
            position: absolute;
            display: none;
            top: 100%;
            left: 0;
            right: 0;
            background-color: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 100;
            max-height: 240px;
            overflow-y: auto;
        }
        
        .custom-select.opened .custom-options {
            display: block;
        }
        
        .custom-option {
            padding: 8px 10px;
            display: block;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .custom-option:hover {
            background-color: #f5f5f5;
        }
        
        .custom-option.selected {
            background-color: #e6f0ff;
        }
        
        .custom-option-group {
            border-top: 1px solid #eee;
        }
        
        .custom-option-group-label {
            padding: 8px 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            font-weight: bold;
            background-color: #f5f5f5;
        }
        
        .custom-option-group-label span {
            display: inline-block;
        }
        
        .custom-option-group-label i {
            transition: transform 0.3s;
        }
        
        .custom-option-group.expanded .custom-option-group-label i {
            transform: rotate(180deg);
        }
        
        .custom-option-group-items {
            display: none;
            padding-left: 10px;
            background-color: #fafafa;
        }
        
        .custom-option-group.expanded .custom-option-group-items {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-container">
                <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="page-title">网页下单</h1>
            </div>
            <a href="dashboard.html" class="back-button">返回用户中心</a>
        </div>
        
        <div class="order-container">
            <div class="steps-container">
                <div class="step active">
                    <div class="step-number">1</div>
                    <div class="step-label">填写收货信息</div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-label">填写物流码</div>
                </div>
            </div>
            
            <div class="form-container">
                <div style="margin-bottom: 20px;">
                    <h2 style="font-size: 24px; color: #0c4da2; margin-top: 10px;"><i class="fas fa-map-marker-alt" style="font-size: 26px; margin-right: 10px; color: #0c4da2;"></i> 收件人信息</h2>
                    <div style="text-align: right; margin-top: -35px;">
                        <button type="button" id="savedAddressBtn" class="btn btn-outline" style="margin-left: 10px; padding: 8px 16px;" onclick="toggleSavedAddresses()">已保存的地址 <span id="addressCount" class="address-count" style="display: inline-block; background-color: #0c4da2; color: white; border-radius: 50%; width: 20px; height: 20px; font-size: 12px; line-height: 20px; text-align: center; margin-left: 5px;">0</span></button>
                    </div>
                </div>
                
                <form class="address-form" id="addressForm">
                    <div id="savedAddressesContainer" style="display: none; margin-bottom: 20px;">
                        <div class="saved-addresses">
                            <div class="address-card" onclick="selectAddress(this)">
                                <strong>张三</strong>
                                <div>13800138000</div>
                                <div>广东省广州市天河区体育西路123号</div>
                                <div>510000</div>
                                <div class="address-actions">
                                    <button class="edit-btn" onclick="editAddress(this, event)"><i class="fas fa-edit"></i></button>
                                    <button class="delete-btn" onclick="deleteAddress(this, event)"><i class="fas fa-trash"></i></button>
                                </div>
                            </div>
                            <div class="address-card" onclick="selectAddress(this)">
                                <strong>李四</strong>
                                <div>13900139000</div>
                                <div>广东省深圳市南山区科技园456号</div>
                                <div>518000</div>
                                <div class="address-actions">
                                    <button class="edit-btn" onclick="editAddress(this, event)"><i class="fas fa-edit"></i></button>
                                    <button class="delete-btn" onclick="deleteAddress(this, event)"><i class="fas fa-trash"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="name" class="required">收件人姓名</label>
                        <input type="text" id="name" name="name" placeholder="请输入收件人姓名" required maxlength="57">
                    </div>
                    
                    <div class="form-group">
                        <label for="phone" class="required">联系电话</label>
                        <input type="tel" id="phone" name="phone" placeholder="请输入联系电话" required maxlength="30">
                    </div>
                    
                    <div class="form-group">
                        <label for="country" class="required">国家</label>
                        <div class="custom-select-wrapper">
                            <select id="country" name="country" class="hidden-select" required>
                                <option value="">请选择国家</option>
                                <option value="美国">美国</option>
                                <option value="加拿大">加拿大</option>
                                <option value="澳洲">澳洲</option>
                                <option value="阿联酋">阿联酋</option>
                                <option value="沙特">沙特</option>
                                <option value="巴基斯坦">巴基斯坦</option>
                                <option value="伊朗">伊朗</option>
                                <option value="卡塔尔">卡塔尔</option>
                                <option value="新加坡">新加坡</option>
                                <option value="马来西亚">马来西亚</option>
                                <option value="泰国">泰国</option>
                                <option value="菲律宾">菲律宾</option>
                                <option value="印尼">印尼</option>
                                <option value="越南">越南</option>
                                <option value="柬埔寨">柬埔寨</option>
                                <option value="缅甸">缅甸</option>
                                <option value="德国">德国</option>
                                <option value="法国">法国</option>
                                <option value="西班牙">西班牙</option>
                                <option value="意大利">意大利</option>
                                <option value="波兰">波兰</option>
                                <option value="捷克">捷克</option>
                                <option value="奥地利">奥地利</option>
                                <option value="匈牙利">匈牙利</option>
                                <option value="斯洛伐克">斯洛伐克</option>
                                <option value="立陶宛">立陶宛</option>
                                <option value="荷兰">荷兰</option>
                                <option value="拉脱维亚">拉脱维亚</option>
                                <option value="克罗地亚">克罗地亚</option>
                                <option value="丹麦">丹麦</option>
                                <option value="爱沙尼亚">爱沙尼亚</option>
                                <option value="比利时">比利时</option>
                                <option value="卢森堡">卢森堡</option>
                                <option value="斯洛文尼亚">斯洛文尼亚</option>
                                <option value="瑞典">瑞典</option>
                                <option value="保加利亚">保加利亚</option>
                                <option value="罗马尼亚">罗马尼亚</option>
                                <option value="芬兰">芬兰</option>
                                <option value="葡萄牙">葡萄牙</option>
                                <option value="希腊">希腊</option>
                                <option value="爱尔兰">爱尔兰</option>
                            </select>
                            <div class="custom-select" id="customCountrySelect">
                                <div class="custom-select-trigger">请选择国家</div>
                                <div class="custom-options">
                                                                    <span class="custom-option" data-value="美国">美国</span>
                                <span class="custom-option" data-value="加拿大">加拿大</span>
                                <span class="custom-option" data-value="澳洲">澳洲</span>
                                <div class="custom-option-group">
                                    <div class="custom-option-group-label">
                                        <span>欧洲</span>
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="custom-option-group-items">
                                        <span class="custom-option" data-value="德国">德国</span>
                                        <span class="custom-option" data-value="法国">法国</span>
                                        <span class="custom-option" data-value="西班牙">西班牙</span>
                                        <span class="custom-option" data-value="意大利">意大利</span>
                                        <span class="custom-option" data-value="波兰">波兰</span>
                                        <span class="custom-option" data-value="捷克">捷克</span>
                                        <span class="custom-option" data-value="奥地利">奥地利</span>
                                        <span class="custom-option" data-value="匈牙利">匈牙利</span>
                                        <span class="custom-option" data-value="斯洛伐克">斯洛伐克</span>
                                        <span class="custom-option" data-value="立陶宛">立陶宛</span>
                                        <span class="custom-option" data-value="荷兰">荷兰</span>
                                        <span class="custom-option" data-value="拉脱维亚">拉脱维亚</span>
                                        <span class="custom-option" data-value="克罗地亚">克罗地亚</span>
                                        <span class="custom-option" data-value="丹麦">丹麦</span>
                                        <span class="custom-option" data-value="爱沙尼亚">爱沙尼亚</span>
                                        <span class="custom-option" data-value="比利时">比利时</span>
                                        <span class="custom-option" data-value="卢森堡">卢森堡</span>
                                        <span class="custom-option" data-value="斯洛文尼亚">斯洛文尼亚</span>
                                        <span class="custom-option" data-value="瑞典">瑞典</span>
                                        <span class="custom-option" data-value="保加利亚">保加利亚</span>
                                        <span class="custom-option" data-value="罗马尼亚">罗马尼亚</span>
                                        <span class="custom-option" data-value="芬兰">芬兰</span>
                                        <span class="custom-option" data-value="葡萄牙">葡萄牙</span>
                                        <span class="custom-option" data-value="希腊">希腊</span>
                                        <span class="custom-option" data-value="爱尔兰">爱尔兰</span>
                                    </div>
                                </div>
                                <div class="custom-option-group">
                                    <div class="custom-option-group-label">
                                        <span>中东</span>
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="custom-option-group-items">
                                        <span class="custom-option" data-value="阿联酋">阿联酋</span>
                                        <span class="custom-option" data-value="沙特">沙特</span>
                                        <span class="custom-option" data-value="巴基斯坦">巴基斯坦</span>
                                        <span class="custom-option" data-value="伊朗">伊朗</span>
                                        <span class="custom-option" data-value="卡塔尔">卡塔尔</span>
                                    </div>
                                </div>
                                <div class="custom-option-group">
                                    <div class="custom-option-group-label">
                                        <span>东南亚</span>
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="custom-option-group-items">
                                        <span class="custom-option" data-value="新加坡">新加坡</span>
                                        <span class="custom-option" data-value="马来西亚">马来西亚</span>
                                        <span class="custom-option" data-value="泰国">泰国</span>
                                        <span class="custom-option" data-value="菲律宾">菲律宾</span>
                                        <span class="custom-option" data-value="印尼">印尼</span>
                                        <span class="custom-option" data-value="越南">越南</span>
                                        <span class="custom-option" data-value="柬埔寨">柬埔寨</span>
                                        <span class="custom-option" data-value="缅甸">缅甸</span>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="city" class="required">城市</label>
                            <input type="text" id="city" name="city" placeholder="请输入城市" required maxlength="30">
                        </div>
                        <div class="form-group">
                            <label for="zipCode" class="required">邮政编码</label>
                            <input type="text" id="zipCode" name="zipCode" placeholder="请输入邮政编码" required maxlength="20">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="address" class="required">详细地址</label>
                        <textarea id="address" name="address" rows="3" placeholder="详细入户地址" required maxlength="189"></textarea>
                    </div>
                </form>
            </div>
            
            <div class="buttons-container">
                <button class="btn btn-outline" onclick="window.location.href='dashboard.html'">取消</button>
                <button class="btn btn-primary" onclick="nextStep()">下一步</button>
            </div>
        </div>
        
        <footer>
            <p>&copy; 2023 金舟国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </footer>
    </div>

    <!-- 添加浮动弹窗 -->
    <div class="popup-container" id="addressPopup">
        <div class="popup-content">
            <div class="popup-header">
                <div class="popup-title">已保存的地址</div>
                <button class="popup-close" onclick="closeAddressPopup()">&times;</button>
            </div>
            
            <!-- 已保存的地址列表 -->
            <div id="savedAddressesList">
                <div class="saved-addresses">
                    <div class="address-card" onclick="selectAddressFromPopup(this)">
                        <strong>张三</strong>
                        <div>13800138000</div>
                        <div>广东省广州市天河区体育西路123号</div>
                        <div>510000</div>
                        <div class="address-actions">
                            <button class="edit-btn" onclick="editAddress(this, event)"><i class="fas fa-edit"></i></button>
                            <button class="delete-btn" onclick="deleteAddress(this, event)"><i class="fas fa-trash"></i></button>
                        </div>
                    </div>
                    <div class="address-card" onclick="selectAddressFromPopup(this)">
                        <strong>李四</strong>
                        <div>13900139000</div>
                        <div>广东省深圳市南山区科技园456号</div>
                        <div>518000</div>
                        <div class="address-actions">
                            <button class="edit-btn" onclick="editAddress(this, event)"><i class="fas fa-edit"></i></button>
                            <button class="delete-btn" onclick="deleteAddress(this, event)"><i class="fas fa-trash"></i></button>
                        </div>
                    </div>
                </div>
                
                <div style="margin: 10px 0; padding: 8px; background-color: #f5f5f5; border-radius: 5px; font-size: 13px; color: #666; text-align: center; position: relative;" id="addressLimitTip">
                    <i class="fas fa-info-circle" style="color: #0c4da2; margin-right: 5px;"></i>
                    最多可保存20个地址，当前已保存 <span id="addressCountInPopup" style="font-weight: bold; color: #0c4da2;">0</span> 个
                    <button class="close-tip-btn" onclick="hideAddressLimitTip(event)" style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #999; cursor: pointer; font-size: 14px; padding: 0;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="popup-footer">
                    <div style="display: flex; gap: 10px;">
                        <button class="btn-add-address" onclick="showAddAddressForm()">添加新地址</button>
                        <button class="btn-add-address" style="background-color: #4CAF50;" onclick="useSelectedAddress()">一键添加地址</button>
                    </div>
                </div>
            </div>
            
            <!-- 新增地址表单 -->
            <div class="new-address-form" id="newAddressForm" style="display: none;">
                <div class="form-group">
                    <label for="newName">收件人姓名</label>
                    <input type="text" id="newName" placeholder="请输入收件人姓名" maxlength="57">
                </div>
                <div class="form-group">
                    <label for="newPhone">联系电话</label>
                    <input type="tel" id="newPhone" placeholder="请输入联系电话" maxlength="30">
                </div>
                <div class="form-group">
                    <label for="newCountry">国家</label>
                    <div class="custom-select-wrapper">
                        <select id="newCountry" class="hidden-select">
                            <option value="">请选择国家</option>
                            <option value="美国">美国</option>
                            <option value="加拿大">加拿大</option>
                            <option value="澳洲">澳洲</option>
                            <option value="新加坡">新加坡</option>
                            <option value="马来西亚">马来西亚</option>
                            <option value="泰国">泰国</option>
                            <option value="菲律宾">菲律宾</option>
                            <option value="印尼">印尼</option>
                            <option value="越南">越南</option>
                            <option value="柬埔寨">柬埔寨</option>
                            <option value="缅甸">缅甸</option>
                            <option value="阿联酋">阿联酋</option>
                            <option value="沙特">沙特</option>
                            <option value="巴基斯坦">巴基斯坦</option>
                            <option value="伊朗">伊朗</option>
                            <option value="卡塔尔">卡塔尔</option>
                            <option value="德国">德国</option>
                            <option value="法国">法国</option>
                            <option value="西班牙">西班牙</option>
                            <option value="意大利">意大利</option>
                            <option value="波兰">波兰</option>
                            <option value="捷克">捷克</option>
                            <option value="奥地利">奥地利</option>
                            <option value="匈牙利">匈牙利</option>
                            <option value="斯洛伐克">斯洛伐克</option>
                            <option value="立陶宛">立陶宛</option>
                            <option value="荷兰">荷兰</option>
                            <option value="拉脱维亚">拉脱维亚</option>
                            <option value="克罗地亚">克罗地亚</option>
                            <option value="丹麦">丹麦</option>
                            <option value="爱沙尼亚">爱沙尼亚</option>
                            <option value="比利时">比利时</option>
                            <option value="卢森堡">卢森堡</option>
                            <option value="斯洛文尼亚">斯洛文尼亚</option>
                            <option value="瑞典">瑞典</option>
                            <option value="保加利亚">保加利亚</option>
                            <option value="罗马尼亚">罗马尼亚</option>
                            <option value="芬兰">芬兰</option>
                            <option value="葡萄牙">葡萄牙</option>
                            <option value="希腊">希腊</option>
                            <option value="爱尔兰">爱尔兰</option>
                        </select>
                        <div class="custom-select" id="customNewCountrySelect">
                            <div class="custom-select-trigger">请选择国家</div>
                            <div class="custom-options">
                                <span class="custom-option" data-value="美国">美国</span>
                                <span class="custom-option" data-value="加拿大">加拿大</span>
                                <span class="custom-option" data-value="澳洲">澳洲</span>
                                <div class="custom-option-group">
                                    <div class="custom-option-group-label">
                                        <span>欧洲</span>
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="custom-option-group-items">
                                        <span class="custom-option" data-value="德国">德国</span>
                                        <span class="custom-option" data-value="法国">法国</span>
                                        <span class="custom-option" data-value="西班牙">西班牙</span>
                                        <span class="custom-option" data-value="意大利">意大利</span>
                                        <span class="custom-option" data-value="波兰">波兰</span>
                                        <span class="custom-option" data-value="捷克">捷克</span>
                                        <span class="custom-option" data-value="奥地利">奥地利</span>
                                        <span class="custom-option" data-value="匈牙利">匈牙利</span>
                                        <span class="custom-option" data-value="斯洛伐克">斯洛伐克</span>
                                        <span class="custom-option" data-value="立陶宛">立陶宛</span>
                                        <span class="custom-option" data-value="荷兰">荷兰</span>
                                        <span class="custom-option" data-value="拉脱维亚">拉脱维亚</span>
                                        <span class="custom-option" data-value="克罗地亚">克罗地亚</span>
                                        <span class="custom-option" data-value="丹麦">丹麦</span>
                                        <span class="custom-option" data-value="爱沙尼亚">爱沙尼亚</span>
                                        <span class="custom-option" data-value="比利时">比利时</span>
                                        <span class="custom-option" data-value="卢森堡">卢森堡</span>
                                        <span class="custom-option" data-value="斯洛文尼亚">斯洛文尼亚</span>
                                        <span class="custom-option" data-value="瑞典">瑞典</span>
                                        <span class="custom-option" data-value="保加利亚">保加利亚</span>
                                        <span class="custom-option" data-value="罗马尼亚">罗马尼亚</span>
                                        <span class="custom-option" data-value="芬兰">芬兰</span>
                                        <span class="custom-option" data-value="葡萄牙">葡萄牙</span>
                                        <span class="custom-option" data-value="希腊">希腊</span>
                                        <span class="custom-option" data-value="爱尔兰">爱尔兰</span>
                                    </div>
                                </div>
                                <div class="custom-option-group">
                                    <div class="custom-option-group-label">
                                        <span>中东</span>
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="custom-option-group-items">
                                        <span class="custom-option" data-value="阿联酋">阿联酋</span>
                                        <span class="custom-option" data-value="沙特">沙特</span>
                                        <span class="custom-option" data-value="巴基斯坦">巴基斯坦</span>
                                        <span class="custom-option" data-value="伊朗">伊朗</span>
                                        <span class="custom-option" data-value="卡塔尔">卡塔尔</span>
                                    </div>
                                </div>
                                <div class="custom-option-group">
                                    <div class="custom-option-group-label">
                                        <span>东南亚</span>
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="custom-option-group-items">
                                        <span class="custom-option" data-value="新加坡">新加坡</span>
                                        <span class="custom-option" data-value="马来西亚">马来西亚</span>
                                        <span class="custom-option" data-value="泰国">泰国</span>
                                        <span class="custom-option" data-value="菲律宾">菲律宾</span>
                                        <span class="custom-option" data-value="印尼">印尼</span>
                                        <span class="custom-option" data-value="越南">越南</span>
                                        <span class="custom-option" data-value="柬埔寨">柬埔寨</span>
                                        <span class="custom-option" data-value="缅甸">缅甸</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="newCity">城市</label>
                    <input type="text" id="newCity" placeholder="请输入城市" maxlength="30">
                </div>
                <div class="form-group">
                    <label for="newZipCode">邮政编码</label>
                    <input type="text" id="newZipCode" placeholder="请输入邮政编码" maxlength="20">
                </div>
                <div class="form-group">
                    <label for="newAddress">详细地址</label>
                    <textarea id="newAddress" placeholder="详细入户地址" rows="3" maxlength="189"></textarea>
                </div>
                <div class="form-actions">
                    <button class="btn-cancel" onclick="cancelAddAddress()">取消</button>
                    <button class="btn-save" onclick="saveNewAddress()">保存地址</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 自定义下拉菜单功能
        document.addEventListener("DOMContentLoaded", function() {
            // 初始化所有自定义下拉菜单
            initCustomDropdowns();
            
            // 点击其他区域关闭所有下拉菜单
            document.addEventListener("click", function(e) {
                if (!e.target.closest('.custom-select-wrapper')) {
                    closeAllCustomSelects();
                }
            });
            
            // 检查用户是否被拉黑
            BlacklistHandler.checkUserBlacklisted();
            
            // 定期检查用户状态
            setInterval(BlacklistHandler.checkUserBlacklisted, 30000);
            
            function initCustomDropdowns() {
                // 主表单下拉菜单
                initCustomSelect('customCountrySelect', 'country');
                // 弹窗表单下拉菜单
                initCustomSelect('customNewCountrySelect', 'newCountry');
            }
            
            function initCustomSelect(customSelectId, originalSelectId) {
                const customSelect = document.getElementById(customSelectId);
                const originalSelect = document.getElementById(originalSelectId);
                
                if (!customSelect || !originalSelect) return;
                
                const trigger = customSelect.querySelector('.custom-select-trigger');
                const options = customSelect.querySelectorAll('.custom-option');
                const optionGroups = customSelect.querySelectorAll('.custom-option-group-label');
                
                // 设置初始值
                if (originalSelect.value) {
                    trigger.textContent = originalSelect.options[originalSelect.selectedIndex].text;
                }
                
                // 打开/关闭下拉菜单
                trigger.addEventListener('click', function() {
                    customSelect.classList.toggle('opened');
                });
                
                // 选择选项时
                options.forEach(option => {
                    option.addEventListener('click', function(e) {
                        e.stopPropagation();
                        
                        const value = this.getAttribute('data-value');
                        const text = this.textContent;
                        
                        // 更新显示文本
                        trigger.textContent = text;
                        
                        // 更新原始下拉菜单的值
                        originalSelect.value = value;
                        
                        // 关闭下拉菜单
                        customSelect.classList.remove('opened');
                        
                        // 标记选中项
                        options.forEach(opt => opt.classList.remove('selected'));
                        this.classList.add('selected');
                        
                        // 触发change事件，以便其他可能监听此事件的代码可以响应
                        const event = new Event('change');
                        originalSelect.dispatchEvent(event);
                    });
                });
                
                // 展开/折叠选项组
                optionGroups.forEach(group => {
                    group.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const parentGroup = this.closest('.custom-option-group');
                        
                        // 检查是否为欧洲组、中东组或东南亚组
                        const groupText = this.querySelector('span').textContent.trim();
                        if (groupText === "欧洲" || groupText === "中东" || groupText === "东南亚") {
                            if (!parentGroup.classList.contains('expanded')) {
                                // 如果是分组且未展开，则展开
                                parentGroup.classList.add('expanded');
                            } else {
                                // 已经展开则折叠
                                parentGroup.classList.remove('expanded');
                            }
                        } else {
                            // 正常展开/折叠
                            parentGroup.classList.toggle('expanded');
                        }
                    });
                });
            }
            
            function closeAllCustomSelects() {
                const openSelects = document.querySelectorAll('.custom-select.opened');
                openSelects.forEach(select => {
                    select.classList.remove('opened');
                });
            }
        });
        
        // 图片压缩函数
        function compressImage(file, callback, pairIndex) {
            // 创建图像对象
            const img = new Image();
            const reader = new FileReader();
            
            reader.onload = function(e) {
                img.src = e.target.result;
                
                img.onload = function() {
                    // 创建Canvas
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    
                    // 计算压缩后的尺寸
                    let width = img.width;
                    let height = img.height;
                    const maxDimension = 1200; // 最大尺寸
                    
                    // 如果图片尺寸超出了最大限制
                    if (width > maxDimension || height > maxDimension) {
                        if (width > height) {
                            height = Math.round(height * maxDimension / width);
                            width = maxDimension;
                        } else {
                            width = Math.round(width * maxDimension / height);
                            height = maxDimension;
                        }
                    }
                    
                    // 设置Canvas尺寸
                    canvas.width = width;
                    canvas.height = height;
                    
                    // 绘制到Canvas
                    ctx.drawImage(img, 0, 0, width, height);
                    
                    // 根据文件大小确定压缩质量
                    let quality = 0.7; // 默认质量70%
                    
                    if (file.size > 5 * 1024 * 1024) { // 大于5MB
                        quality = 0.5;
                    } else if (file.size > 2 * 1024 * 1024) { // 大于2MB
                        quality = 0.6;
                    } else if (file.size > 1 * 1024 * 1024) { // 大于1MB
                        quality = 0.7;
                    } else {
                        // 对于1MB以下的图片也进行适度压缩
                        quality = 0.8;
                    }
                    
                    // 将Canvas导出为Blob
                    canvas.toBlob(
                        function(blob) {
                            // 创建新的文件对象
                            const compressedFile = new File(
                                [blob], 
                                file.name, 
                                {
                                    type: 'image/jpeg',
                                    lastModified: Date.now()
                                }
                            );
                            
                            console.log(`图片压缩: ${Math.round(file.size/1024)}KB → ${Math.round(blob.size/1024)}KB (${Math.round((1 - blob.size/file.size) * 100)}% 压缩率)`);
                            
                            // 显示压缩信息
                            if (pairIndex) {
                                showCompressionInfo(file.size, blob.size, pairIndex);
                            }
                            
                            // 返回压缩后的文件
                            callback(compressedFile);
                        }, 
                        'image/jpeg', 
                        quality
                    );
                };
            };
            
            reader.readAsDataURL(file);
        }

        // 上传图片到服务器
        function uploadImageToServer(file, pairIndex, callback) {
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }

            if (!loggedInUser || !loggedInUser.username) {
                callback(false, { message: '用户未登录' });
                return;
            }

            // 创建 FormData 对象
            const formData = new FormData();
            formData.append('image', file);
            formData.append('username', loggedInUser.username);
            formData.append('logisticsCode', pairIndex ? `物流码${pairIndex}` : '');

            // 发送上传请求
            fetch('/api/upload-image', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('图片上传成功:', data.filename);
                    callback(true, data);
                } else {
                    console.error('图片上传失败:', data.message);
                    callback(false, data);
                }
            })
            .catch(error => {
                console.error('图片上传错误:', error);
                callback(false, { message: '网络错误，请稍后重试' });
            });
        }

        // 删除图片函数
        function deleteImage(filename, pairDiv) {
            if (!filename) {
                alert('无法删除图片：文件名不存在');
                return;
            }

            if (!confirm('确定要删除这张图片吗？')) {
                return;
            }

            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }

            if (!loggedInUser || !loggedInUser.username) {
                alert('用户未登录，无法删除图片');
                return;
            }

            // 发送删除请求到服务器
            fetch(`/api/delete-image/${filename}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: loggedInUser.username
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 清除前端显示
                    const thumbnailPreview = pairDiv.querySelector('.thumbnail-preview');
                    if (thumbnailPreview) {
                        thumbnailPreview.innerHTML = '';
                        thumbnailPreview.style.display = 'none';
                    }

                    const previewDiv = pairDiv.querySelector('.preview-container');
                    if (previewDiv) {
                        previewDiv.innerHTML = '';
                    }

                    // 清除保存的图片信息
                    const codeInput = pairDiv.querySelector('.logistics-code');
                    if (codeInput) {
                        delete codeInput.dataset.imageUrl;
                        delete codeInput.dataset.imageFilename;
                    }

                    // 重置文件输入
                    const fileInput = pairDiv.querySelector('.image-upload');
                    if (fileInput) {
                        fileInput.value = '';
                    }

                    // 保存更新后的数据
                    saveLogisticsData();

                    console.log('图片删除成功');
                } else {
                    alert('删除图片失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('删除图片错误:', error);
                alert('删除图片时发生网络错误，请稍后重试');
            });
        }

        // 保存表单数据到localStorage
        function saveFormData() {
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            // 如果找不到登录用户，则不保存表单数据
            if (!loggedInUser || !loggedInUser.username) {
                console.warn('No logged in user found, cannot save form data');
                return;
            }
            
            const formData = {
                name: document.getElementById('name').value,
                phone: document.getElementById('phone').value,
                country: document.getElementById('country').value,
                city: document.getElementById('city').value,
                zipCode: document.getElementById('zipCode').value,
                address: document.getElementById('address').value
            };
            
            // 使用API保存到服务器
            fetch('/api/save-form-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: loggedInUser.username,
                    formData: formData
                })
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    console.warn('Failed to save form data:', data.message);
                }
            })
            .catch(error => {
                console.error('Error saving form data:', error);
            });
        }

        // 从localStorage加载表单数据
        function loadFormData() {
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            // 如果找不到登录用户，则不加载表单数据
            if (!loggedInUser || !loggedInUser.username) {
                console.warn('No logged in user found, cannot load form data');
                return;
            }
            
            // 使用API从服务器获取数据
            fetch(`/api/get-form-data?username=${encodeURIComponent(loggedInUser.username)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    const formData = data.data;
                    document.getElementById('name').value = formData.name || '';
                    document.getElementById('phone').value = formData.phone || '';
                    document.getElementById('country').value = formData.country || '';
                    document.getElementById('city').value = formData.city || '';
                    document.getElementById('zipCode').value = formData.zipCode || '';
                    document.getElementById('address').value = formData.address || '';
                    
                    // 更新自定义下拉菜单显示
                    if (formData.country) {
                        const customCountrySelect = document.getElementById('customCountrySelect');
                        if (customCountrySelect) {
                            const trigger = customCountrySelect.querySelector('.custom-select-trigger');
                            if (trigger) {
                                trigger.textContent = formData.country;
                            }
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error loading form data:', error);
            });
        }

        // 清除保存的表单数据
        function clearFormData() {
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            // 如果找不到登录用户，则不清除表单数据
            if (!loggedInUser || !loggedInUser.username) {
                console.warn('No logged in user found, cannot clear form data');
                return;
            }
            
            // 使用API删除服务器上的数据
            fetch(`/api/delete-form-data?username=${encodeURIComponent(loggedInUser.username)}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    console.warn('Failed to clear form data:', data.message);
                }
            })
            .catch(error => {
                console.error('Error clearing form data:', error);
            });
        }

        // 清除物流码数据
        function clearLogisticsData() {
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            // 如果找不到登录用户，则不清除物流码数据
            if (!loggedInUser || !loggedInUser.username) {
                console.warn('No logged in user found, cannot clear logistics data');
                return;
            }
            
            // 清除本地缓存
            window.logisticsDataCache = null;
            
            // 使用API删除服务器上的数据
            fetch(`/api/delete-logistics-data?username=${encodeURIComponent(loggedInUser.username)}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    console.warn('Failed to clear logistics data:', data.message);
                }
            })
            .catch(error => {
                console.error('Error clearing logistics data:', error);
            });
        }

        // 清除所有下单相关数据
        function clearAllOrderData() {
            // 清除表单数据
            clearFormData();
            // 清除物流码数据
            clearLogisticsData();
            
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            // 如果找不到登录用户，则不清除当前步骤信息
            if (!loggedInUser || !loggedInUser.username) {
                console.warn('No logged in user found, cannot clear current step');
                return;
            }
            
            // 使用API删除服务器上的数据
            fetch(`/api/delete-current-step?username=${encodeURIComponent(loggedInUser.username)}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    console.warn('Failed to clear current step:', data.message);
                }
            })
            .catch(error => {
                console.error('Error clearing current step:', error);
            });
        }
        
        // 隐藏地址限制提示
        function hideAddressLimitTip(event) {
            event.stopPropagation(); // 阻止事件冒泡
            
            // 创建确认对话框
            const confirmDialog = document.createElement('div');
            confirmDialog.className = 'popup-container';
            confirmDialog.style.display = 'flex';
            confirmDialog.style.zIndex = '2000'; // 确保在最上层
            
            const dialogContent = document.createElement('div');
            dialogContent.className = 'popup-content';
            dialogContent.style.maxWidth = '400px';
            
            // 对话框标题
            const dialogHeader = document.createElement('div');
            dialogHeader.className = 'popup-header';
            dialogHeader.innerHTML = '<div class="popup-title">确认提示</div>';
            
            // 对话框内容
            const dialogBody = document.createElement('div');
            dialogBody.style.padding = '15px 0';
            dialogBody.style.textAlign = 'center';
            
            const messageP = document.createElement('p');
            messageP.textContent = '您确认不再显示此提示吗？';
            messageP.style.margin = '0 0 10px 0';
            
            dialogBody.appendChild(messageP);
            
            // 对话框按钮
            const dialogFooter = document.createElement('div');
            dialogFooter.style.display = 'flex';
            dialogFooter.style.justifyContent = 'center';
            dialogFooter.style.gap = '15px';
            dialogFooter.style.marginTop = '15px';
            
            // 否按钮
            const noButton = document.createElement('button');
            noButton.className = 'btn btn-outline';
            noButton.textContent = '否';
            noButton.onclick = function() {
                // 清除所有下单相关数据
                clearAllOrderData();
                
                // 删除所有已上传的图片
                deleteAllUploadedImages();
                
                // 关闭对话框
                document.body.removeChild(confirmDialog);
                // 跳转
                window.location.href = redirectUrl;
            };
            
            // 是按钮
            const yesButton = document.createElement('button');
            yesButton.className = 'btn btn-primary';
            yesButton.textContent = '是';
            yesButton.onclick = function() {
                // 获取当前登录用户信息
                let loggedInUser = null;
                try {
                    const userDataStr = sessionStorage.getItem('loggedInUser');
                    if (userDataStr) {
                        loggedInUser = JSON.parse(userDataStr);
                    }
                } catch (error) {
                    console.warn('Failed to get user data:', error);
                }
                
                if (loggedInUser && loggedInUser.username) {
                    // 永久隐藏提示（保存到服务器）
                    fetch('/api/save-address-limit-tip-state', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: loggedInUser.username,
                            state: true
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (!data.success) {
                            console.warn('Failed to save address limit tip state:', data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error saving address limit tip state:', error);
                    });
                }
                
                document.getElementById('addressLimitTip').style.display = 'none';
                // 关闭对话框
                document.body.removeChild(confirmDialog);
            };
            
            // 组装对话框
            dialogFooter.appendChild(noButton);
            dialogFooter.appendChild(yesButton);
            
            dialogContent.appendChild(dialogHeader);
            dialogContent.appendChild(dialogBody);
            dialogContent.appendChild(dialogFooter);
            
            confirmDialog.appendChild(dialogContent);
            
            // 显示对话框
            document.body.appendChild(confirmDialog);
        }

        // 返回用户中心时的确认提示
        function confirmSaveFormData(redirectUrl) {
            console.log("confirmSaveFormData called with URL:", redirectUrl);
            // 检查表单是否有内容
            const formData = {
                name: document.getElementById('name')?.value || '',
                phone: document.getElementById('phone')?.value || '',
                country: document.getElementById('country')?.value || '',
                city: document.getElementById('city')?.value || '',
                zipCode: document.getElementById('zipCode')?.value || '',
                address: document.getElementById('address')?.value || ''
            };
            
            // 检查是否有物流码数据
            const logisticsData = loadLogisticsData();
            
            const hasContent = Object.values(formData).some(value => value.trim() !== '') || 
                              (logisticsData && logisticsData.length > 0);
            console.log("Form has content:", hasContent);
            
            if (hasContent) {
                // 创建确认对话框
                const confirmDialog = document.createElement('div');
                confirmDialog.className = 'popup-container';
                confirmDialog.style.display = 'flex';
                
                const dialogContent = document.createElement('div');
                dialogContent.className = 'popup-content';
                dialogContent.style.maxWidth = '400px';
                
                // 对话框标题
                const dialogHeader = document.createElement('div');
                dialogHeader.className = 'popup-header';
                dialogHeader.innerHTML = '<div class="popup-title">确认提示</div>';
                
                // 对话框内容
                const dialogBody = document.createElement('div');
                dialogBody.style.padding = '15px 0';
                dialogBody.style.textAlign = 'center';
                
                // 修改文本内容，确保文字不会断行
                const messageP1 = document.createElement('p');
                messageP1.textContent = '是否保留已填写的内容？';
                messageP1.style.margin = '0 0 10px 0';
                
                const messageP2 = document.createElement('p');
                messageP2.textContent = '选择"是"将保存当前填写的信息，下次下单时可以继续使用。';
                messageP2.style.margin = '0';
                messageP2.style.lineHeight = '1.6';
                messageP2.style.whiteSpace = 'normal';
                messageP2.style.wordBreak = 'keep-all';
                messageP2.style.wordWrap = 'break-word';
                
                dialogBody.appendChild(messageP1);
                dialogBody.appendChild(messageP2);
                
                // 对话框按钮
                const dialogFooter = document.createElement('div');
                dialogFooter.style.display = 'flex';
                dialogFooter.style.justifyContent = 'center';
                dialogFooter.style.gap = '15px';
                dialogFooter.style.marginTop = '15px';
                
                // 否按钮
                const noButton = document.createElement('button');
                noButton.className = 'btn btn-outline';
                noButton.textContent = '否';
                noButton.onclick = function() {
                    // 清除所有下单相关数据
                    clearAllOrderData();
                    
                    // 删除所有已上传的图片
                    deleteAllUploadedImages();
                    
                    // 关闭对话框
                    document.body.removeChild(confirmDialog);
                    // 跳转
                    window.location.href = redirectUrl;
                };
                
                // 是按钮
                const yesButton = document.createElement('button');
                yesButton.className = 'btn btn-primary';
                yesButton.textContent = '是';
                yesButton.onclick = function() {
                    // 保存表单数据
                    saveFormData();
                    // 保存物流码数据（如果在第二步）
                    if (document.getElementById('logisticsCodeContainer') && 
                        document.getElementById('logisticsCodeContainer').style.display !== 'none') {
                        saveLogisticsData();
                    }
                    // 关闭对话框
                    document.body.removeChild(confirmDialog);
                    // 跳转
                    window.location.href = redirectUrl;
                };
                
                // 组装对话框
                dialogFooter.appendChild(noButton);
                dialogFooter.appendChild(yesButton);
                
                dialogContent.appendChild(dialogHeader);
                dialogContent.appendChild(dialogBody);
                dialogContent.appendChild(dialogFooter);
                
                confirmDialog.appendChild(dialogContent);
                
                // 显示对话框
                document.body.appendChild(confirmDialog);
            } else {
                // 如果没有内容，直接跳转
                window.location.href = redirectUrl;
            }
        }

        // 保存物流码数据到localStorage
        function saveLogisticsData() {
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            // 如果找不到登录用户，则不保存物流码数据
            if (!loggedInUser || !loggedInUser.username) {
                console.warn('No logged in user found, cannot save logistics data');
                return;
            }
            
            const pairs = document.querySelectorAll('.code-image-pair');
            const logisticsData = [];
            
            pairs.forEach((pair, index) => {
                const codeInput = pair.querySelector('.logistics-code');
                const codeValue = codeInput ? codeInput.value : '';
                
                // 获取商品名称
                const productNameInput = pair.querySelector('.product-name');
                const productNameValue = productNameInput ? productNameInput.value : '';
                
                // 获取商品价格
                const priceInput = pair.querySelector('.product-price');
                const priceValue = priceInput ? priceInput.value : '';
                
                // 获取运输方式
                const shippingMethodSelect = pair.querySelector('.shipping-method');
                const shippingMethodValue = shippingMethodSelect ? shippingMethodSelect.value : '';
                
                // 获取图片信息
                let imageUrl = null;
                let imageFilename = null;

                // 优先从 dataset 获取服务器上的图片信息
                if (codeInput && codeInput.dataset.imageUrl) {
                    imageUrl = codeInput.dataset.imageUrl;
                    imageFilename = codeInput.dataset.imageFilename;
                } else {
                    // 兼容旧的 base64 数据（如果存在）
                    const imagePreview = pair.querySelector('.image-preview');
                    if (imagePreview && imagePreview.src && !imagePreview.src.startsWith('/uploads/')) {
                        imageUrl = imagePreview.src; // base64 数据
                    }
                }

                logisticsData.push({
                    code: codeValue,
                    productName: productNameValue,
                    price: priceValue,
                    shippingMethod: shippingMethodValue,
                    imageUrl: imageUrl,
                    imageFilename: imageFilename
                });
            });
            
            // 使用API保存到服务器
            fetch('/api/save-logistics-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: loggedInUser.username,
                    logisticsData: logisticsData
                })
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    console.warn('Failed to save logistics data:', data.message);
                }
            })
            .catch(error => {
                console.error('Error saving logistics data:', error);
            });
        }

        // 从localStorage加载物流码数据
        function loadLogisticsData() {
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            // 如果找不到登录用户，则不加载物流码数据
            if (!loggedInUser || !loggedInUser.username) {
                console.warn('No logged in user found, cannot load logistics data');
                return null;
            }
            
            // 检查缓存中是否有数据
            if (window.logisticsDataCache) {
                return window.logisticsDataCache;
            }
            
            // 异步从服务器获取数据，但同步返回本地缓存
            fetch(`/api/get-logistics-data?username=${encodeURIComponent(loggedInUser.username)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    // 缓存数据以便后续使用
                    window.logisticsDataCache = data.data;
                    
                    // 如果这是第一次调用，而且数据已返回，尝试更新界面
                    const container = document.getElementById('codeImageContainer');
                    if (container && data.data.length > 0) {
                        // 等待DOM更新后恢复数据
                        setTimeout(() => {
                            restoreLogisticsData();
                        }, 100);
                    }
                }
            })
            .catch(error => {
                console.error('Error loading logistics data:', error);
            });
            
            // 初始返回空数组
            return window.logisticsDataCache || [];
        }
        
        // 修改restoreLogisticsData函数以正确恢复图片显示和运输方式
function restoreLogisticsData() {
    const logisticsData = loadLogisticsData();
    if (!logisticsData || !logisticsData.length) return;
    
    const container = document.getElementById('codeImageContainer');
    if (!container) return;
    
    // 确保物流码对的数量与数据匹配
    const currentPairs = container.querySelectorAll('.code-image-pair');
    if (currentPairs.length < logisticsData.length) {
        // 如果当前的对数少于保存的数据，添加更多对
        for (let i = currentPairs.length; i < logisticsData.length; i++) {
            addCodeImagePair(container, i + 1);
        }
    }
    
    // 先确保运输方式选项是最新的
    updateShippingOptions();
    
    // 检查是否刚从第一步切换到第二步时更改了国家
    const countrySelect = document.getElementById('country');
    const changedCountry = countrySelect && sessionStorage.getItem('previousCountry') !== countrySelect.value;
    
    // 在DOM渲染完成后填充已保存的数据
    setTimeout(() => {
        const pairs = container.querySelectorAll('.code-image-pair');
        logisticsData.forEach((data, index) => {
            if (index < pairs.length) {
                // 恢复物流码
                const codeInput = pairs[index].querySelector('.logistics-code');
                if (codeInput) {
                    codeInput.value = data.code || '';
                }
                
                // 恢复商品名称
                const productNameInput = pairs[index].querySelector('.product-name');
                if (productNameInput) {
                    productNameInput.value = data.productName || '';
                }
                
                // 恢复商品价格
                const priceInput = pairs[index].querySelector('.product-price');
                if (priceInput) {
                    priceInput.value = data.price || '';
                }
                
                // 恢复运输方式 - 只有在未切换国家的情况下才恢复之前的值
                const shippingMethodSelect = pairs[index].querySelector('.shipping-method');
                if (shippingMethodSelect && !changedCountry) {
                    // 记住当前所选值
                    const savedShippingMethod = data.shippingMethod || '';
                    
                    // 确保根据国家设置了正确的运输方式选项
                    if (countrySelect && countrySelect.value) {
                        // 为这个特定的物流码设置运输方式选项
                        setupShippingOptionsForSelect(shippingMethodSelect, countrySelect.value);
                    }
                    
                    // 尝试恢复所选的运输方式
                    if (savedShippingMethod) {
                        // 检查是否有这个选项
                        let optionExists = false;
                        for (let i = 0; i < shippingMethodSelect.options.length; i++) {
                            if (shippingMethodSelect.options[i].value === savedShippingMethod) {
                                shippingMethodSelect.value = savedShippingMethod;
                                optionExists = true;
                                break;
                            }
                        }
                        
                        // 如果之前的值在新选项中不存在，则默认选择第一个有效选项
                        if (!optionExists && shippingMethodSelect.options.length > 1) {
                            shippingMethodSelect.selectedIndex = 1; // 选择第一个非空选项
                        }
                    }
                }
                
                // 恢复图片 - 优先使用服务器上的图片URL
                const imageUrl = data.imageUrl;
                if (imageUrl) {
                    // 保存图片信息到 dataset
                    if (codeInput) {
                        codeInput.dataset.imageUrl = imageUrl;
                        if (data.imageFilename) {
                            codeInput.dataset.imageFilename = data.imageFilename;
                        }
                    }

                    // 1. 恢复缩略图
                    const fileInputContainer = pairs[index].querySelector('.file-input-container');
                    if (fileInputContainer) {
                        let thumbnailPreview = fileInputContainer.querySelector('.thumbnail-preview');

                        if (!thumbnailPreview) {
                            thumbnailPreview = document.createElement('div');
                            thumbnailPreview.className = 'thumbnail-preview';
                            fileInputContainer.appendChild(thumbnailPreview);
                        }

                        // 清除原有内容
                        thumbnailPreview.innerHTML = '';

                        // 创建缩略图容器
                        const thumbnailContainer = document.createElement('div');
                        thumbnailContainer.style.position = 'relative';
                        thumbnailContainer.style.display = 'inline-block';

                        // 创建缩略图
                        const thumbnail = document.createElement('img');
                        thumbnail.src = imageUrl;
                        thumbnail.alt = '缩略图';
                        thumbnail.onclick = function() {
                            // 显示大图预览
                            const modal = document.getElementById('imagePreviewModal');
                            const modalImg = document.getElementById('previewImage');
                            modalImg.src = imageUrl;
                            modal.style.display = 'flex';
                        };

                        // 创建删除按钮（只有服务器上的图片才显示删除按钮）
                        if (data.imageFilename && imageUrl.startsWith('/uploads/')) {
                            const deleteBtn = document.createElement('button');
                            deleteBtn.innerHTML = '×';
                            deleteBtn.className = 'image-delete-btn';
                            deleteBtn.style.position = 'absolute';
                            deleteBtn.style.top = '-5px';
                            deleteBtn.style.right = '-5px';
                            deleteBtn.style.width = '20px';
                            deleteBtn.style.height = '20px';
                            deleteBtn.style.borderRadius = '50%';
                            deleteBtn.style.border = 'none';
                            deleteBtn.style.backgroundColor = '#ff4444';
                            deleteBtn.style.color = 'white';
                            deleteBtn.style.fontSize = '12px';
                            deleteBtn.style.cursor = 'pointer';
                            deleteBtn.style.display = 'flex';
                            deleteBtn.style.alignItems = 'center';
                            deleteBtn.style.justifyContent = 'center';
                            deleteBtn.title = '删除图片';

                            deleteBtn.onclick = function(e) {
                                e.stopPropagation();
                                deleteImage(data.imageFilename, pairs[index]);
                            };

                            thumbnailContainer.appendChild(deleteBtn);
                        }

                        // 组装缩略图
                        thumbnailContainer.appendChild(thumbnail);

                        // 显示缩略图
                        thumbnailPreview.style.display = 'inline-block';
                        thumbnailPreview.appendChild(thumbnailContainer);
                    }
                    
                    // 2. 恢复预览容器和大图预览
                    const imageSection = pairs[index].querySelector('.image-section');
                    if (imageSection) {
                        let previewDiv = imageSection.querySelector('.preview-container');
                        
                        if (!previewDiv) {
                            previewDiv = document.createElement('div');
                            previewDiv.className = 'preview-container';
                            previewDiv.style.marginTop = '10px';
                            imageSection.appendChild(previewDiv);
                        }
                        
                        // 清除原有内容
                        previewDiv.innerHTML = '';
                        
                        // 创建图片预览
                        const img = document.createElement('img');
                        img.src = imageUrl;
                        img.className = 'image-preview';
                        img.alt = '购买截图';
                        img.style.display = 'none'; // 初始隐藏大图预览

                        previewDiv.appendChild(img);
                    }
                }
            }
        });
        
        // 保存当前国家值，以便下次检测变化
        if (countrySelect) {
            sessionStorage.setItem('previousCountry', countrySelect.value);
        }
    }, 300); // 增加延迟确保DOM完全渲染
}
        
        // 添加物流码和图片对的函数
        function addCodeImagePair(container, index) {
            const pairDiv = document.createElement('div');
            pairDiv.className = 'code-image-pair';
            pairDiv.style.position = 'relative';
            
            // 物流码部分
            const codeSection = document.createElement('div');
            codeSection.className = 'code-section';
            codeSection.style.display = 'flex';
            codeSection.style.gap = '15px';
            codeSection.style.width = '100%';
            
            const codeLabel = document.createElement('label');
            codeLabel.textContent = `物流码${index}`;
            codeLabel.className = 'required';
            codeLabel.style.fontWeight = 'bold';
            codeLabel.style.display = 'block';
            codeLabel.style.marginBottom = '5px';
            
            const codeInputContainer = document.createElement('div');
            codeInputContainer.style.flex = '2'; // 占2/3空间
            
            const codeInput = document.createElement('input');
            codeInput.type = 'text';
            codeInput.className = 'logistics-code';
            codeInput.placeholder = '请输入物流码';
            codeInput.required = true;
            codeInput.maxLength = 21; // 设置最大长度为21个字符
            codeInput.style.width = '100%';
            codeInput.style.padding = '10px';
            codeInput.style.borderRadius = '5px';
            codeInput.style.border = '1px solid #ddd';
            codeInput.style.marginBottom = '15px';
            
            // 添加输入事件监听器，保存数据和验证长度
            codeInput.addEventListener('input', function() {
                // 检查输入长度是否超过21个字符
                if (this.value.length > 21) {
                    alert('物流码不能超过21个字符');
                    this.value = this.value.substring(0, 21); // 截断到21个字符
                }
                saveLogisticsData();
            });
            
            // 创建价格输入部分
            const priceContainer = document.createElement('div');
            priceContainer.style.flex = '1'; // 占1/3空间
            
            const priceLabel = document.createElement('label');
            priceLabel.textContent = '商品价格';
            priceLabel.className = 'required';
            priceLabel.style.fontWeight = 'bold';
            priceLabel.style.display = 'block';
            priceLabel.style.marginBottom = '5px';
            
            const priceInput = document.createElement('input');
            priceInput.type = 'number';
            priceInput.className = 'product-price';
            priceInput.placeholder = '请输入价格';
            priceInput.required = true;
            priceInput.min = '0';
            priceInput.max = '999999999'; // 设置最大值为9位数
            priceInput.step = '0.01';
            priceInput.style.width = '100%';
            priceInput.style.padding = '10px';
            priceInput.style.borderRadius = '5px';
            priceInput.style.border = '1px solid #ddd';
            priceInput.style.marginBottom = '15px';
            
            // 添加输入事件监听器，保存数据和验证最大值
            priceInput.addEventListener('input', function() {
                // 当用户输入时立即检查和处理
                let currentValue = this.value;
                
                // 移除非数字字符（保留小数点）
                currentValue = currentValue.replace(/[^\d.]/g, '');
                
                // 确保只有一个小数点
                const parts = currentValue.split('.');
                if (parts.length > 2) {
                    currentValue = parts[0] + '.' + parts.slice(1).join('');
                }
                
                // 检查整数部分是否超过9位
                const integerPart = parts[0] || '';
                if (integerPart.length > 9) {
                    // 如果整数部分超过9位，截断为9位
                    currentValue = integerPart.substring(0, 9);
                    if (parts.length > 1) {
                        currentValue += '.' + parts[1];
                    }
                }
                
                // 检查数值是否超过最大值
                if (parseFloat(currentValue) > 999999999) {
                    currentValue = '999999999';
                    alert('商品价格不能超过9位数');
                }
                
                // 更新输入框的值
                this.value = currentValue;
                
                saveLogisticsData();
            });
            
            codeInputContainer.appendChild(codeLabel);
            codeInputContainer.appendChild(codeInput);
            
            priceContainer.appendChild(priceLabel);
            priceContainer.appendChild(priceInput);
            
            codeSection.appendChild(codeInputContainer);
            codeSection.appendChild(priceContainer);
            
            // 创建底部区域容器，用于放置图片上传、运输方式和商品名称
            const bottomSection = document.createElement('div');
            bottomSection.style.display = 'flex';
            bottomSection.style.gap = '0'; // 移除间距，由各元素自己控制
            bottomSection.style.alignItems = 'flex-start';
            bottomSection.style.marginTop = '15px';
            bottomSection.style.position = 'relative'; // 添加相对定位以便子元素可以绝对定位
            bottomSection.style.width = '100%'; // 确保容器占满整个宽度
            
            // 图片上传部分
            const imageSection = document.createElement('div');
            imageSection.className = 'image-section';
            imageSection.style.width = '150px'; // 固定宽度而不是使用flex
            imageSection.style.marginRight = '0'; // 移除右边距
            imageSection.style.zIndex = '1'; // 确保图片部分在最上层
            
            const imageLabel = document.createElement('label');
            imageLabel.textContent = '购买截图';
            imageLabel.className = 'required';
            imageLabel.style.fontWeight = 'bold';
            imageLabel.style.display = 'block';
            imageLabel.style.marginBottom = '5px';
            
            // 创建一个容器来包裹文件输入框，以便更好地控制其外观
            const fileInputContainer = document.createElement('div');
            fileInputContainer.style.position = 'relative';
            fileInputContainer.style.width = '100%';
            fileInputContainer.style.overflow = 'visible';
            fileInputContainer.style.display = 'flex';
            fileInputContainer.style.alignItems = 'center';
            fileInputContainer.style.marginBottom = '15px';
            fileInputContainer.className = 'file-input-container';
            
            // 创建左侧的文件选择区域
            const fileSelectArea = document.createElement('div');
            fileSelectArea.style.width = '120px';
            fileSelectArea.style.flexShrink = '0';
            
            const imageInput = document.createElement('input');
            imageInput.type = 'file';
            imageInput.className = 'image-upload';
            imageInput.accept = 'image/*';
            imageInput.required = true;
            imageInput.style.marginBottom = '0';
            imageInput.style.width = 'auto';
            imageInput.style.position = 'relative';
            imageInput.style.overflow = 'hidden';
            imageInput.style.fontSize = '0'; // 隐藏文字
            imageInput.style.paddingRight = '0'; // 确保没有额外空间
            imageInput.style.opacity = '1'; // 确保按钮可见
            imageInput.title = ''; // 移除默认的title提示
            
            // 将文件输入框添加到左侧区域
            fileSelectArea.appendChild(imageInput);
            
            // 添加一个隐藏层来覆盖文件名文本
            const hiddenLayer = document.createElement('div');
            hiddenLayer.style.position = 'absolute';
            hiddenLayer.style.top = '0';
            hiddenLayer.style.left = 'auto';
            hiddenLayer.style.right = '0';
            hiddenLayer.style.bottom = '0';
            hiddenLayer.style.backgroundColor = 'transparent';
            hiddenLayer.style.zIndex = '10';
            hiddenLayer.style.width = '0';
            fileSelectArea.appendChild(hiddenLayer);
            
            // 将左侧区域添加到容器
            fileInputContainer.appendChild(fileSelectArea);
            
            // 创建缩略图预览容器 - 居中显示
            const thumbnailPreview = document.createElement('div');
            thumbnailPreview.className = 'thumbnail-preview';
            thumbnailPreview.style.display = 'none'; // 初始时隐藏
            fileInputContainer.appendChild(thumbnailPreview);
            
            // 创建大图预览容器
            const previewDiv = document.createElement('div');
            previewDiv.className = 'preview-container';
            previewDiv.style.marginTop = '10px';
            previewDiv.style.display = 'none'; // 隐藏大图预览
            
            // 为文件输入添加change事件监听器
            imageInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    // 检查文件大小，限制为5MB
                    if (file.size > 5 * 1024 * 1024) {
                        alert('图片大小不能超过5MB，请选择较小的图片或压缩后上传');
                        return;
                    }
                    
                    // 获取当前物流码对的索引
                    const pairDiv = this.closest('.code-image-pair');
                    if (!pairDiv) {
                        console.error('无法找到物流码对元素');
                        return;
                    }
                    
                    // 通过标签获取索引更可靠
                    const labelElement = pairDiv.querySelector('.code-section label');
                    let pairIndex = 1; // 默认索引
                    
                    if (labelElement && labelElement.textContent) {
                        // 从"物流码X"中提取数字
                        const match = labelElement.textContent.match(/\d+/);
                        if (match && match[0]) {
                            pairIndex = parseInt(match[0], 10);
                        }
                    } else {
                        // 备用方法：通过DOM位置确定索引
                        const allPairs = Array.from(document.querySelectorAll('.code-image-pair'));
                        pairIndex = allPairs.indexOf(pairDiv) + 1;
                    }
                    
                    console.log(`正在处理物流码${pairIndex}的图片`);
                    
                    // 显示上传进度
                    const progressDiv = document.createElement('div');
                    progressDiv.className = 'upload-progress';
                    progressDiv.style.marginTop = '10px';
                    progressDiv.innerHTML = '<div style="color: #007bff;">正在上传图片...</div>';
                    thumbnailPreview.appendChild(progressDiv);

                    // 使用图片压缩函数处理所有图片
                    compressImage(file, function(compressedFile) {
                        // 上传压缩后的文件到服务器
                        uploadImageToServer(compressedFile, pairIndex, function(success, result) {
                            // 移除进度提示
                            if (progressDiv.parentNode) {
                                progressDiv.parentNode.removeChild(progressDiv);
                            }

                            if (success) {
                                // 清除原有预览
                                thumbnailPreview.innerHTML = '';
                                previewDiv.innerHTML = '';

                                // 创建缩略图容器
                                const thumbnailContainer = document.createElement('div');
                                thumbnailContainer.style.position = 'relative';
                                thumbnailContainer.style.display = 'inline-block';

                                // 创建缩略图
                                const thumbnail = document.createElement('img');
                                thumbnail.src = result.fileUrl;
                                thumbnail.alt = '缩略图';
                                thumbnail.onclick = function() {
                                    // 显示大图预览
                                    const modal = document.getElementById('imagePreviewModal');
                                    const modalImg = document.getElementById('previewImage');
                                    modalImg.src = result.fileUrl;
                                    modal.style.display = 'flex';
                                };

                                // 创建删除按钮
                                const deleteBtn = document.createElement('button');
                                deleteBtn.innerHTML = '×';
                                deleteBtn.className = 'image-delete-btn';
                                deleteBtn.style.position = 'absolute';
                                deleteBtn.style.top = '-5px';
                                deleteBtn.style.right = '-5px';
                                deleteBtn.style.width = '20px';
                                deleteBtn.style.height = '20px';
                                deleteBtn.style.borderRadius = '50%';
                                deleteBtn.style.border = 'none';
                                deleteBtn.style.backgroundColor = '#ff4444';
                                deleteBtn.style.color = 'white';
                                deleteBtn.style.fontSize = '12px';
                                deleteBtn.style.cursor = 'pointer';
                                deleteBtn.style.display = 'flex';
                                deleteBtn.style.alignItems = 'center';
                                deleteBtn.style.justifyContent = 'center';
                                deleteBtn.title = '删除图片';

                                deleteBtn.onclick = function(e) {
                                    e.stopPropagation();
                                    deleteImage(result.filename, pairDiv);
                                };

                                // 组装缩略图
                                thumbnailContainer.appendChild(thumbnail);
                                thumbnailContainer.appendChild(deleteBtn);

                                // 显示缩略图
                                thumbnailPreview.style.display = 'inline-block';
                                thumbnailPreview.appendChild(thumbnailContainer);

                                // 创建大图预览（隐藏）
                                const img = document.createElement('img');
                                img.src = result.fileUrl;
                                img.className = 'image-preview';
                                img.alt = '购买截图';
                                img.style.display = 'none';
                                previewDiv.appendChild(img);

                                // 保存文件信息到物流码对象中
                                const codeInput = pairDiv.querySelector('.logistics-code');
                                if (codeInput) {
                                    codeInput.dataset.imageUrl = result.fileUrl;
                                    codeInput.dataset.imageFilename = result.filename;
                                }

                                // 保存数据状态
                                saveLogisticsData();
                            } else {
                                alert('图片上传失败：' + result.message);
                                // 清空文件输入
                                imageInput.value = '';
                            }
                        });
                    }, pairIndex);
                }
            });
            
            // 添加到图片区域
            imageSection.appendChild(imageLabel);
            imageSection.appendChild(fileInputContainer);
            imageSection.appendChild(previewDiv);
            
            // 添加品名输入字段 - 放在图片右侧
            const productNameSection = document.createElement('div');
            productNameSection.className = 'product-name-section';
            productNameSection.style.width = 'calc(50% - 75px)'; // 固定宽度而不是使用flex
            productNameSection.style.display = 'flex';
            productNameSection.style.flexDirection = 'column';
            productNameSection.style.justifyContent = 'flex-start';
            productNameSection.style.marginTop = '0';
            productNameSection.style.height = '100%';
            productNameSection.style.marginLeft = '0px'; // 从-15px改为0px，向右移动
            productNameSection.style.position = 'relative'; // 添加相对定位
            productNameSection.style.left = '5px'; // 从-5px改为5px，进一步向右移动
            productNameSection.style.zIndex = '2'; // 确保商品名称在图片之上
            
            const productNameLabel = document.createElement('label');
            productNameLabel.textContent = '商品名称';
            productNameLabel.className = 'required';
            productNameLabel.style.fontWeight = 'bold';
            productNameLabel.style.display = 'block';
            productNameLabel.style.marginBottom = '5px';
            
            const productNameInput = document.createElement('textarea');
            productNameInput.className = 'product-name';
            productNameInput.placeholder = '请输入商品名称';
            productNameInput.required = true;
            productNameInput.maxLength = 50; // 设置最大字符数为50
            productNameInput.style.width = '100%';
            productNameInput.style.padding = '10px';
            productNameInput.style.borderRadius = '5px';
            productNameInput.style.border = '1px solid #ddd';
            productNameInput.style.height = '42px'; // 初始高度
            productNameInput.style.resize = 'vertical'; // 允许垂直调整大小
            productNameInput.style.minHeight = '42px'; // 最小高度
            productNameInput.style.maxHeight = '150px'; // 最大高度
            productNameInput.style.overflowY = 'hidden'; // 隐藏垂直滚动条
            
            // 添加自动调整高度的功能
            productNameInput.addEventListener('input', function() {
                // 记录上一次输入长度
                if (!this.dataset.prevLength) {
                    this.dataset.prevLength = '0';
                }
                
                const prevLength = parseInt(this.dataset.prevLength);
                
                // 检查是否尝试输入更多字符
                if (this.value.length >= 50 && prevLength <= this.value.length) {
                    // 判断是否已经显示过警告，避免重复显示
                    if (!this.dataset.warningShown) {
                        alert('商品名称不能超过50个字符');
                        this.dataset.warningShown = 'true'; // 标记已显示警告
                        
                        // 5秒后重置标记，允许再次显示警告
                        setTimeout(() => {
                            this.dataset.warningShown = '';
                        }, 5000);
                    }
                    
                    this.value = this.value.substring(0, 50); // 截断为50个字符
                }
                
                // 更新记录的长度
                this.dataset.prevLength = this.value.length.toString();
                
                // 重置高度以获取正确的scrollHeight
                this.style.height = 'auto';
                // 根据内容设置新高度
                const newHeight = Math.min(this.scrollHeight, 150);
                this.style.height = newHeight + 'px';
                // 保存数据
                saveLogisticsData();
            });
            
            // 初始聚焦时也调整高度
            productNameInput.addEventListener('focus', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 150) + 'px';
            });
            
            productNameSection.appendChild(productNameLabel);
            productNameSection.appendChild(productNameInput);
            
            // 添加运输方式选择 - 放在商品名称右侧
            const shippingSection = document.createElement('div');
            shippingSection.className = 'shipping-method-section';
            shippingSection.style.width = 'calc(50% - 75px)'; // 固定宽度而不是使用flex
            shippingSection.style.display = 'flex';
            shippingSection.style.flexDirection = 'column';
            shippingSection.style.justifyContent = 'flex-start';
            shippingSection.style.marginLeft = '15px'; // 向右移动
            shippingSection.style.position = 'relative'; // 添加相对定位
            shippingSection.style.left = '10px'; // 使用left属性进一步向右移动
            shippingSection.style.zIndex = '3'; // 确保运输方式在商品名称之上
            
            const shippingLabel = document.createElement('label');
            shippingLabel.textContent = '运输方式';
            shippingLabel.className = 'required';
            shippingLabel.style.fontWeight = 'bold';
            shippingLabel.style.display = 'block';
            shippingLabel.style.marginBottom = '5px';
            
            const shippingSelect = document.createElement('select');
            shippingSelect.className = 'shipping-method';
            shippingSelect.required = true;
            shippingSelect.style.width = '100%';
            shippingSelect.style.padding = '10px';
            shippingSelect.style.borderRadius = '5px';
            shippingSelect.style.border = '1px solid #ddd';
            shippingSelect.style.height = '42px'; // 匹配文件选择按钮的高度
            
            // 添加运输方式选项 - 默认选项，后续会根据国家/地区动态更新
            const options = [
                { value: '', text: '请选择运输方式' },
                { value: 'air', text: '空运' },
                { value: 'sea', text: '海运' }
            ];
            
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.text;
                shippingSelect.appendChild(optionElement);
            });
            
            // 添加change事件监听器，保存数据
            shippingSelect.addEventListener('change', saveLogisticsData);
            
            shippingSection.appendChild(shippingLabel);
            shippingSection.appendChild(shippingSelect);
            
            // 将三个部分添加到底部区域容器
            bottomSection.appendChild(imageSection);
            bottomSection.appendChild(productNameSection);
            bottomSection.appendChild(shippingSection);
            
            // 如果不是第一组，添加删除按钮
            if (index > 1) {
                const deleteBtn = document.createElement('button');
                deleteBtn.type = 'button';
                deleteBtn.className = 'delete-pair-btn';
                deleteBtn.innerHTML = '<i class="fas fa-times"></i>';
                deleteBtn.title = '删除此组';
                deleteBtn.onclick = function() {
                    container.removeChild(pairDiv);
                    // 更新剩余组的序号
                    updateCodeIndices();
                    // 保存更新后的数据
                    saveLogisticsData();
                };
                pairDiv.appendChild(deleteBtn);
            }
            
            // 将两个部分添加到容器
            pairDiv.appendChild(codeSection);
            pairDiv.appendChild(bottomSection);
            
            // 将整个组添加到容器
            container.appendChild(pairDiv);
            
            // 更新运输方式选项，根据当前选择的国家/地区
            updateShippingOptions();
        }
        
        // 更新物流码序号
        function updateCodeIndices() {
            const pairs = document.querySelectorAll('.code-image-pair');
            pairs.forEach((pair, index) => {
                const label = pair.querySelector('.code-section label');
                label.textContent = `物流码${index + 1}`;
            });
        }
        
        // 验证物流码和图片
        function validateLogisticsCodes() {
            // 验证用户是否登录
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            if (!loggedInUser || !loggedInUser.username) {
                alert('请先登录再提交订单');
                window.location.href = 'login.html';
                return;
            }
            
            const codeInputs = document.querySelectorAll('.logistics-code');
            const productNameInputs = document.querySelectorAll('.product-name');
            const priceInputs = document.querySelectorAll('.product-price');
            const shippingMethodSelects = document.querySelectorAll('.shipping-method');
            const pairs = document.querySelectorAll('.code-image-pair');
            
            let isValid = true;
            
            // 检查每个物流码是否已填写
            codeInputs.forEach((input, index) => {
                if (!input.value.trim()) {
                    alert(`请填写物流码${index + 1}`);
                    input.value = '';
                    input.focus();
                    isValid = false;
                } else if (input.value.length > 21) {
                    alert(`物流码${index + 1}不能超过21个字符`);
                    input.value = input.value.substring(0, 21); // 截断到21个字符
                    isValid = false;
                }
            });
            
            // 检查每个商品名称是否已填写
            productNameInputs.forEach((input, index) => {
                if (!input.value.trim()) {
                    alert(`请填写物流码${index + 1}对应的商品名称`);
                    input.value = '';
                    input.focus();
                    isValid = false;
                } else if (input.value.length > 50) {
                    // 在这里只截断而不显示提示，因为已在输入时提示过
                    input.value = input.value.substring(0, 50); // 截断为50个字符
                    // 只有输入时没有提示过才在这里提示
                    if (!input.dataset.warningShown) {
                        alert(`物流码${index + 1}的商品名称不能超过50个字符`);
                    }
                    isValid = false;
                }
            });
            
            // 检查价格是否有效
            priceInputs.forEach((input, index) => {
                const price = parseFloat(input.value);
                if (isNaN(price) || price <= 0) {
                    alert(`请为物流码${index + 1}填写有效的商品价格`);
                    isValid = false;
                } else if (price > 999999999) {
                    alert(`物流码${index + 1}的商品价格不能超过9位数`);
                    input.value = 999999999;
                    isValid = false;
                }
            });
            
            // 检查每个运输方式是否已选择
            shippingMethodSelects.forEach((select, index) => {
                if (!select.value) {
                    alert(`请选择物流码${index + 1}对应的运输方式`);
                    isValid = false;
                }
            });
            
            // 检查每个图片是否已上传
            pairs.forEach((pair, index) => {
                const codeInput = pair.querySelector('.logistics-code');
                const imagePreview = pair.querySelector('.image-preview');

                // 检查是否有图片：优先检查dataset中的imageUrl，然后检查imagePreview的src
                let hasImage = false;
                if (codeInput && codeInput.dataset.imageUrl) {
                    hasImage = true;
                } else if (imagePreview && imagePreview.src && imagePreview.src !== window.location.href) {
                    hasImage = true;
                }

                if (!hasImage) {
                    alert(`请上传物流码${index + 1}对应的购买截图`);
                    isValid = false;
                }
            });
            
            if (isValid) {
                // 保存物流码数据
                saveLogisticsData();

                // 获取收货信息
                const shippingInfo = {
                    name: document.getElementById('name').value,
                    phone: document.getElementById('phone').value,
                    country: document.getElementById('country').value,
                    city: document.getElementById('city').value,
                    zipCode: document.getElementById('zipCode').value,
                    address: document.getElementById('address').value
                };

                // 直接从DOM收集物流码数据，确保数据是最新的
                const logisticsData = [];
                pairs.forEach((pair, index) => {
                    const codeInput = pair.querySelector('.logistics-code');
                    const productNameInput = pair.querySelector('.product-name');
                    const priceInput = pair.querySelector('.product-price');
                    const shippingMethodSelect = pair.querySelector('.shipping-method');

                    // 获取图片信息
                    let imageUrl = null;
                    let imageFilename = null;

                    // 优先从 dataset 获取服务器上的图片信息
                    if (codeInput && codeInput.dataset.imageUrl) {
                        imageUrl = codeInput.dataset.imageUrl;
                        imageFilename = codeInput.dataset.imageFilename;
                    } else {
                        // 兼容旧的 base64 数据（如果存在）
                        const imagePreview = pair.querySelector('.image-preview');
                        if (imagePreview && imagePreview.src && !imagePreview.src.startsWith('/uploads/')) {
                            imageUrl = imagePreview.src; // base64 数据
                        }
                    }

                    logisticsData.push({
                        code: codeInput ? codeInput.value : '',
                        productName: productNameInput ? productNameInput.value : '',
                        price: priceInput ? priceInput.value : '',
                        shippingMethod: shippingMethodSelect ? shippingMethodSelect.value : '',
                        imageUrl: imageUrl,
                        imageFilename: imageFilename
                    });
                });
                
                // 调试信息
                console.log('收集到的物流码数据:', logisticsData);
                console.log('物流码数据长度:', logisticsData.length);

                // 创建订单对象
                const order = {
                    id: generateOrderId(),
                    date: new Date().toISOString(),
                    status: '待处理',
                    shippingInfo: shippingInfo,
                    items: logisticsData,
                    totalItems: logisticsData.length
                };

                console.log('创建的订单对象:', order);
                
                // 保存订单到localStorage
                saveOrder(order);
                
                // 提交订单
                alert('订单已提交成功！');
                
                // 清除所有下单相关数据
                clearAllOrderData();
                
                // 刷新每日订单计数
                window.dispatchEvent(new CustomEvent('orderSubmitted'));
                
                // 延迟跳转到用户中心，确保用户有时间看到提交成功的提示
                setTimeout(function() {
                window.location.href = 'dashboard.html';
                }, 500);
            }
        }

        // 生成订单ID
        function generateOrderId() {
            // 生成基于时间戳和随机数的订单ID
            const timestamp = new Date().getTime();
            const random = Math.floor(Math.random() * 10000);
            return `JZ${timestamp}${random}`;
        }
        
        /**
 * 更新运输方式选项，根据当前选择的国家/地区
 * 更新所有运输方式选择框
 */
function updateShippingOptions() {
    // 获取当前选择的国家/地区
    const countrySelect = document.getElementById('country');
    if (!countrySelect) return;
    
    const selectedCountry = countrySelect.value;
    
    // 获取所有运输方式选择框
    const shippingSelects = document.querySelectorAll('.shipping-method');
    
    // 保存当前所有选择框的值
    const currentValues = Array.from(shippingSelects).map(select => select.value);
    
    // 针对每个运输方式选择框，应用相应的选项
    shippingSelects.forEach((select, index) => {
        // 使用辅助函数设置运输方式选项
        // 传入当前选择框和当前用户选择的国家/地区
        setupShippingOptionsForSelect(select, selectedCountry);
        
        // 如果在setupShippingOptionsForSelect中没有恢复到原值，
        // 手动尝试恢复之前保存的值
        if (currentValues[index] && select.value !== currentValues[index]) {
            // 查找对应的选项
            for (let i = 0; i < select.options.length; i++) {
                if (select.options[i].value === currentValues[index]) {
                    select.value = currentValues[index];
                    break;
                }
            }
        }
    });
}

/**
 * 为特定的选择框设置运输方式选项 - 辅助函数，与updateShippingOptions功能类似
 * @param {HTMLSelectElement} select - 要更新的选择框元素
 * @param {string} selectedCountry - 选中的国家/地区
 */
function setupShippingOptionsForSelect(select, selectedCountry) {
    if (!select) return;
    
    // 保存当前选择的值
    const currentValue = select.value;
    
    // 清空现有选项
    select.innerHTML = '';
    
    // 添加默认选项
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = '请选择运输方式';
    select.appendChild(defaultOption);
    
    // 根据国家/地区添加不同的选项
    if (selectedCountry === '美国' || selectedCountry === '加拿大' || selectedCountry === '澳洲') {
        // 添加八种运输方式
        const specialOptions = [
            { value: 'air_regular', text: '空运普货' },
            { value: 'air_sensitive', text: '空运敏货' },
            { value: 'sea_small_regular', text: '海运小件普货' },
            { value: 'sea_small_sensitive', text: '海运小件敏货' },
            { value: 'sea_full_regular', text: '海运整柜普货' },
            { value: 'sea_full_sensitive', text: '海运整柜敏货' },
            { value: 'sea_oversized_regular', text: '海运超大件普货' },
            { value: 'sea_oversized_sensitive', text: '海运超大件敏货' }
        ];
        
        specialOptions.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            select.appendChild(optionElement);
        });
    } else if (['德国', '拉脱维亚', '芬兰', '法国', '西班牙', '克罗地亚', '丹麦', '葡萄牙', '意大利', '波兰', 
               '爱沙尼亚', '希腊', '捷克', '比利时', '奥地利', '卢森堡', '爱尔兰', '匈牙利', '斯洛文尼亚', 
               '斯洛伐克', '瑞典', '立陶宛', '保加利亚', '荷兰', '罗马尼亚'].includes(selectedCountry)) {
        // 欧洲国家的12种运输方式
        const europeanOptions = [
            { value: 'air_regular', text: '空运普货' },
            { value: 'air_sensitive', text: '空运敏货' },
            { value: 'rail_full_regular', text: '铁运整柜普货' },
            { value: 'rail_full_sensitive', text: '铁运整柜敏货' },
            { value: 'rail_oversized_regular', text: '铁运超大件普货' },
            { value: 'rail_oversized_sensitive', text: '铁运超大件敏货' },
            { value: 'sea_full_regular', text: '海运整柜普货' },
            { value: 'sea_full_sensitive', text: '海运整柜敏货' },
            { value: 'sea_small_regular', text: '海运小件普货' },
            { value: 'sea_small_sensitive', text: '海运小件敏货' },
            { value: 'sea_oversized_regular', text: '海运超大件普货' },
            { value: 'sea_oversized_sensitive', text: '海运超大件敏货' }
        ];
        
        europeanOptions.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            select.appendChild(optionElement);
        });
    } else if (['阿联酋', '沙特', '巴基斯坦', '伊朗', '卡塔尔'].includes(selectedCountry)) {
        // 中东国家的8种运输方式
        const middleEastOptions = [
            { value: 'air_sensitive', text: '空运敏货' },
            { value: 'air_regular', text: '空运普货' },
            { value: 'sea_full_regular', text: '海运整柜普货' },
            { value: 'sea_full_sensitive', text: '海运整柜敏货' },
            { value: 'sea_small_regular', text: '海运小件普货' },
            { value: 'sea_small_sensitive', text: '海运小件敏货' },
            { value: 'sea_oversized_regular', text: '海运超大件普货' },
            { value: 'sea_oversized_sensitive', text: '海运超大件敏货' }
        ];
        
        middleEastOptions.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            select.appendChild(optionElement);
        });
    } else if (selectedCountry === '泰国') {
        // 泰国的10种运输方式
        const thailandOptions = [
            { value: 'land_small_regular', text: '陆运小件普货' },
            { value: 'land_small_sensitive', text: '陆运小件敏货' },
            { value: 'land_full_regular', text: '陆运整柜普货' },
            { value: 'land_full_sensitive', text: '陆运整柜敏货' },
            { value: 'land_oversized_regular', text: '陆运超大件普货' },
            { value: 'land_oversized_sensitive', text: '陆运超大件敏货' },
            { value: 'sea_full_regular', text: '海运整柜普货' },
            { value: 'sea_full_sensitive', text: '海运整柜敏货' },
            { value: 'sea_oversized_regular', text: '海运超大件普货' },
            { value: 'sea_oversized_sensitive', text: '海运超大件敏货' }
        ];
        
        thailandOptions.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            select.appendChild(optionElement);
        });
    } else if (selectedCountry === '越南') {
        // 越南的6种陆运运输方式
        const vietnamOptions = [
            { value: 'land_small_regular', text: '陆运小件普货' },
            { value: 'land_small_sensitive', text: '陆运小件敏货' },
            { value: 'land_full_regular', text: '陆运整柜普货' },
            { value: 'land_full_sensitive', text: '陆运整柜敏货' },
            { value: 'land_oversized_regular', text: '陆运超大件普货' },
            { value: 'land_oversized_sensitive', text: '陆运超大件敏货' }
        ];
        
        vietnamOptions.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            select.appendChild(optionElement);
        });
    } else if (selectedCountry === '柬埔寨') {
        // 柬埔寨的8种运输方式
        const cambodiaOptions = [
            { value: 'air_small_regular', text: '空运小件普货' },
            { value: 'air_small_sensitive', text: '空运小件敏货' },
            { value: 'land_small_regular', text: '陆运小件普货' },
            { value: 'land_small_sensitive', text: '陆运小件敏货' },
            { value: 'land_full_regular', text: '陆运整柜普货' },
            { value: 'land_full_sensitive', text: '陆运整柜敏货' },
            { value: 'land_oversized_regular', text: '陆运超大件普货' },
            { value: 'land_oversized_sensitive', text: '陆运超大件敏货' }
        ];
        
        cambodiaOptions.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            select.appendChild(optionElement);
        });
    } else if (['新加坡', '马来西亚', '菲律宾', '印尼', '缅甸'].includes(selectedCountry)) {
        // 其他东南亚国家的8种运输方式
        const southeastAsiaOptions = [
            { value: 'air_regular', text: '空运普货' },
            { value: 'air_sensitive', text: '空运敏货' },
            { value: 'sea_full_regular', text: '海运整柜普货' },
            { value: 'sea_full_sensitive', text: '海运整柜敏货' },
            { value: 'sea_small_regular', text: '海运小件普货' },
            { value: 'sea_small_sensitive', text: '海运小件敏货' },
            { value: 'sea_oversized_regular', text: '海运超大件普货' },
            { value: 'sea_oversized_sensitive', text: '海运超大件敏货' }
        ];
        
        southeastAsiaOptions.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            select.appendChild(optionElement);
        });
    } else {
        // 其他国家/地区只显示两种运输方式
        const regularOptions = [
            { value: 'air', text: '空运' },
            { value: 'sea', text: '海运' }
        ];
        
        regularOptions.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            select.appendChild(optionElement);
        });
    }
    
    // 尝试恢复之前选择的值
    if (currentValue && currentValue !== '') {
        // 检查是否可以在新的选项中找到之前的值
        let optionExists = false;
        for (let i = 0; i < select.options.length; i++) {
            if (select.options[i].value === currentValue) {
                select.value = currentValue;
                optionExists = true;
                break;
            }
        }
        
        // 如果之前的值在新选项中不存在，则默认选择第一个有效选项
        if (!optionExists && select.options.length > 1) {
            select.selectedIndex = 1; // 选择第一个非空选项
        }
    }
}

/**
 * 获取当前登录用户的今日订单数量
 */
function getDailyOrderCount() {
    // 获取当前日期（年-月-日格式）
    const today = new Date().toISOString().split('T')[0];
    
    // 获取当前登录用户
    let username = null;
    try {
        const userDataStr = sessionStorage.getItem('loggedInUser');
        if (userDataStr) {
            const userData = JSON.parse(userDataStr);
            username = userData.username;
        }
    } catch (error) {
        console.warn('Failed to get user data:', error);
    }
    
    if (!username) return 0;
    
    // 获取所有订单
    const orders = JSON.parse(localStorage.getItem('orders') || '[]');
            
            // 过滤出当前用户今天的订单
            const userTodayOrders = orders.filter(order => {
                // 检查用户名
                if (order.username !== username) return false;
                
                // 检查日期
                const orderDate = new Date(order.date).toISOString().split('T')[0];
                return orderDate === today;
            });
            
            return userTodayOrders.length;
        }

        // 保存订单到服务器
        function saveOrder(order) {
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                    
                    // 将用户信息添加到订单中
                    order.username = loggedInUser.username;
                    order.userEmail = loggedInUser.email || '';
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            // 使用API添加新订单
            fetch('/api/add-order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    order: order
                })
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    console.warn('Failed to save order:', data.message);
                }
            })
            .catch(error => {
                console.error('Error saving order:', error);
            });
        }

        // 从服务器加载订单
        function loadOrders() {
            // 检查缓存
            if (window.ordersCache) {
                return window.ordersCache;
            }
            
            // 异步从服务器获取订单，但同步返回缓存
            fetch('/api/get-orders')
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    // 更新缓存
                    window.ordersCache = result.data || [];
                    
                    // 触发订单更新事件，通知其他依赖订单数据的组件
                    window.dispatchEvent(new CustomEvent('ordersUpdated'));
                }
            })
            .catch(error => {
                console.error('Error loading orders:', error);
            });
            
            // 首次调用返回空数组
            return window.ordersCache || [];
        }
        
        // 导航到第一步：填写收货信息
        function navigateToStep1() {
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            // 清除localStorage中的步骤信息
            if (loggedInUser && loggedInUser.username) {
                // 使用API删除服务器上的数据，确保刷新后不会自动返回步骤2
                fetch(`/api/delete-current-step?username=${encodeURIComponent(loggedInUser.username)}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        console.warn('Failed to delete current step:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error deleting current step:', error);
                });
            }
            
            // 更新步骤指示器
            document.querySelectorAll('.step').forEach((step, index) => {
                step.classList.remove('active');
                step.classList.remove('completed');
                if (index === 0) {
                    step.classList.add('active');
                }
            });
            
            // 隐藏物流码容器
            const logisticsCodeContainer = document.getElementById('logisticsCodeContainer');
            if (logisticsCodeContainer) {
                logisticsCodeContainer.style.display = 'none';
            }
            
            // 显示地址表单
            document.querySelector('.form-container').style.display = 'block';
            
            // 恢复原始按钮
            const buttonsContainer = document.querySelector('.buttons-container');
            buttonsContainer.innerHTML = '';
            
            // 添加取消按钮
            const cancelButton = document.createElement('button');
            cancelButton.className = 'btn btn-outline';
            cancelButton.textContent = '取消';
            cancelButton.addEventListener('click', function() {
                confirmSaveFormData('dashboard.html');
            });
            
            // 添加下一步按钮
            const nextButton = document.createElement('button');
            nextButton.className = 'btn btn-primary';
            nextButton.textContent = '下一步';
            nextButton.onclick = function() {
                nextStep();
            };
            
            // 将按钮添加到按钮容器
            buttonsContainer.appendChild(cancelButton);
            buttonsContainer.appendChild(nextButton);
        }

        // 更新样式
        function updateCodeImageStyles() {
            // 这个函数现在不需要添加额外的CSS，因为我们已经在<style>块中添加了样式
            // 保留这个函数但不做任何操作，以确保代码调用有效
        }
        
        // 导航到第二步：填写物流码
        function navigateToStep2(saveState = true) {
            // 保存表单数据
            saveFormData();
            
            // 添加表单验证检查，确保即使通过刷新页面也不会跳过必填项验证
            const form = document.getElementById('addressForm');
            const nameInput = document.getElementById('name');
            const phoneInput = document.getElementById('phone');
            const countryInput = document.getElementById('country');
            const cityInput = document.getElementById('city');
            const zipCodeInput = document.getElementById('zipCode');
            const addressInput = document.getElementById('address');
            
            // 检查字段是否只有空白字符
            if (nameInput && nameInput.value.trim() === '') {
                alert('收件人姓名不能为空');
                navigateToStep1();
                nameInput.value = '';
                nameInput.focus();
                return;
            }
            
            if (phoneInput && phoneInput.value.trim() === '') {
                alert('联系电话不能为空');
                navigateToStep1();
                phoneInput.value = '';
                phoneInput.focus();
                return;
            }
            
            if (!countryInput.value) {
                alert('请选择国家');
                navigateToStep1();
                countryInput.focus();
                return;
            }
            
            if (cityInput && cityInput.value.trim() === '') {
                alert('城市不能为空');
                navigateToStep1();
                cityInput.value = '';
                cityInput.focus();
                return;
            }
            
            if (zipCodeInput && zipCodeInput.value.trim() === '') {
                alert('邮政编码不能为空');
                navigateToStep1();
                zipCodeInput.value = '';
                zipCodeInput.focus();
                return;
            }
            
            if (addressInput && addressInput.value.trim() === '') {
                alert('详细地址不能为空');
                navigateToStep1();
                addressInput.value = '';
                addressInput.focus();
                return;
            }
            
            if (!form.checkValidity()) {
                // 如果表单验证不通过，重定向回第一步
                navigateToStep1();
                // 显示验证错误提示
                alert('请完整填写收货地址信息！');
                // 触发浏览器的表单验证
                const submitButton = document.createElement('button');
                submitButton.type = 'submit';
                form.appendChild(submitButton);
                submitButton.click();
                form.removeChild(submitButton);
                return; // 中断后续操作
            }

            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            // 保存当前步骤到服务器
            if (saveState && loggedInUser && loggedInUser.username) {
                fetch('/api/save-current-step', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: loggedInUser.username,
                        step: '2'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        console.warn('Failed to save current step:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error saving current step:', error);
                });
            }
            
            // 更新步骤指示器
            document.querySelectorAll('.step').forEach((step, index) => {
                step.classList.remove('active');
                if (index === 0) {
                    step.classList.add('completed');
                } else if (index === 1) {
                    step.classList.add('active');
                }
            });
            
            // 检查国家是否发生变更
            const countrySelect = document.getElementById('country');
            const previousCountry = sessionStorage.getItem('previousCountry');
            const currentCountry = countrySelect ? countrySelect.value : '';
            const countryChanged = previousCountry && previousCountry !== currentCountry;
            
            // 如果国家发生变更，清除保存的物流码数据中的运输方式
            if (countryChanged && loggedInUser && loggedInUser.username) {
                // 首先从缓存或服务器获取当前物流码数据
                fetch(`/api/get-logistics-data?username=${encodeURIComponent(loggedInUser.username)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data && data.data.length > 0) {
                        // 清除所有运输方式选择
                        const updatedData = data.data.map(item => ({
                            ...item,
                            shippingMethod: '' // 重置运输方式为空
                        }));
                        
                        // 更新缓存
                        window.logisticsDataCache = updatedData;
                        
                        // 保存回服务器
                        fetch('/api/save-logistics-data', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                username: loggedInUser.username,
                                logisticsData: updatedData
                            })
                        })
                        .then(response => response.json())
                        .then(saveResult => {
                            if (!saveResult.success) {
                                console.warn('Failed to update logistics data after country change:', saveResult.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error updating logistics data after country change:', error);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading logistics data to update after country change:', error);
                });
            }
            
            // 更新previousCountry以便后续比较
            if (countrySelect) {
                sessionStorage.setItem('previousCountry', currentCountry);
            }
            
            // 隐藏当前表单
            document.querySelector('.form-container').style.display = 'none';
            
            // 检查是否已经存在物流码容器
            let logisticsCodeContainer = document.getElementById('logisticsCodeContainer');
            if (!logisticsCodeContainer) {
                // 创建物流码页面容器
                logisticsCodeContainer = document.createElement('div');
                logisticsCodeContainer.className = 'form-container';
                logisticsCodeContainer.id = 'logisticsCodeContainer';
                
                // 设置物流码页面标题
                const title = document.createElement('h2');
                title.style.fontSize = '24px';
                title.style.color = '#0c4da2';
                title.style.marginTop = '10px';
                title.style.marginBottom = '20px';
                title.innerHTML = '<i class="fas fa-barcode" style="font-size: 26px; margin-right: 10px; color: #0c4da2;"></i> 填写物流码';
                
                // 将标题添加到物流码容器
                logisticsCodeContainer.appendChild(title);
                
                // 添加提示文字
                const description = document.createElement('p');
                description.textContent = '请填写物流码并上传相应的购买截图，添加商品名称，并选择运输方式。每一个物流码对应一个商品和一张截图。';
                description.style.marginBottom = '20px';
                logisticsCodeContainer.appendChild(description);
                
                // 创建物流码和图片网格容器
                const codeImageContainer = document.createElement('div');
                codeImageContainer.id = 'codeImageContainer';
                codeImageContainer.className = 'code-image-grid';
                
                // 检查是否有保存的物流码数据
                const logisticsData = loadLogisticsData();
                if (logisticsData && logisticsData.length > 0) {
                    // 恢复已保存的数据
                    logisticsData.forEach((data, index) => {
                        addCodeImagePair(codeImageContainer, index + 1);
                    });
                    
                    // 在DOM渲染后填充数据 - 增加延迟确保DOM元素完全渲染
                    setTimeout(() => {
                        // 确保运输方式选项先更新，以便可以正确恢复选择的值
                        updateShippingOptions();
                        // 恢复所有保存的数据，包括运输方式
                        restoreLogisticsData();
                    }, 300);
                } else {
                    // 添加第一组物流码和图片输入
                    addCodeImagePair(codeImageContainer, 1);
                    // 确保新添加的物流码有正确的运输方式选项
                    setTimeout(() => {
                        updateShippingOptions();
                    }, 100);
                }
                
                // 添加"添加更多"按钮
                const addMoreButton = document.createElement('button');
                addMoreButton.type = 'button';
                addMoreButton.className = 'btn btn-outline';
                addMoreButton.style.marginTop = '15px';
                addMoreButton.innerHTML = '<i class="fas fa-plus"></i> 添加更多';
                addMoreButton.onclick = function() {
                    const pairCount = document.querySelectorAll('.code-image-pair').length;
                    // 限制最多只能添加100个
                    if (pairCount >= 100) {
                        alert('最多只能添加100个商品');
                        return;
                    }
                    addCodeImagePair(codeImageContainer, pairCount + 1);
                    // 更新新添加的物流码输入区域的运输方式选项
                    updateShippingOptions();
                };
                
                // 添加到容器
                logisticsCodeContainer.appendChild(codeImageContainer);
                logisticsCodeContainer.appendChild(addMoreButton);
                
                // 将物流码容器添加到页面
                document.querySelector('.order-container').insertBefore(
                    logisticsCodeContainer, 
                    document.querySelector('.buttons-container')
                );
                
                // 添加模态框用于图片预览
                const previewModal = document.createElement('div');
                previewModal.id = 'imagePreviewModal';
                previewModal.className = 'popup-container';
                previewModal.style.display = 'none';
                previewModal.style.zIndex = '2000';
                
                const previewContent = document.createElement('div');
                previewContent.className = 'popup-content';
                previewContent.style.padding = '10px';
                previewContent.style.maxWidth = '90%';
                
                const closeBtn = document.createElement('button');
                closeBtn.className = 'popup-close';
                closeBtn.innerHTML = '&times;';
                closeBtn.onclick = function() {
                    previewModal.style.display = 'none';
                };
                
                const previewImg = document.createElement('img');
                previewImg.id = 'previewImage';
                previewImg.style.width = '100%';
                previewImg.style.maxHeight = '80vh';
                previewImg.style.objectFit = 'contain';
                
                previewContent.appendChild(closeBtn);
                previewContent.appendChild(previewImg);
                previewModal.appendChild(previewContent);
                document.body.appendChild(previewModal);
                
                // 更新样式
                updateCodeImageStyles();
            } else {
                // 如果已存在，则显示
                logisticsCodeContainer.style.display = 'block';
                
                // 当切换国家后重新进入第二步时，重置所有运输方式选择框为默认值
                const shippingSelects = document.querySelectorAll('.shipping-method');
                shippingSelects.forEach(select => {
                    select.value = ''; // 重置为默认值"请选择运输方式"
                });
                
                // 无论何种情况，都尝试恢复数据
                setTimeout(() => {
                    restoreLogisticsData();
                }, 100);
            }
            
            // 更新按钮容器
            const buttonsContainer = document.querySelector('.buttons-container');
            buttonsContainer.innerHTML = '';
            
            // 添加上一步按钮
            const prevButton = document.createElement('button');
            prevButton.className = 'btn btn-outline';
            prevButton.textContent = '上一步';
            prevButton.onclick = navigateToStep1;
            
            // 添加提交订单按钮
            const submitButton = document.createElement('button');
            submitButton.className = 'btn btn-primary';
            submitButton.textContent = '提交订单';
            submitButton.onclick = function() {
                // 检查每日订单限制
                const dailyOrderCount = getDailyOrderCount ? getDailyOrderCount() : 0;
                if (dailyOrderCount >= 20) {
                    alert('您今日已达到下单上限(20单)，请明日再来下单。');
                    setTimeout(function() {
                        window.location.href = 'dashboard.html';
                    }, 100);
                    return;
                }
                validateLogisticsCodes();
            };
            
            // 将按钮添加到按钮容器
            buttonsContainer.appendChild(prevButton);
            buttonsContainer.appendChild(submitButton);
            
            // 添加一个默认的压缩信息示例，便于测试显示
            setTimeout(() => {
                // 验证是否有图像预览元素
                const hasImages = document.querySelectorAll('.image-preview').length > 0;
                if (!hasImages) {
                    // 添加示例压缩信息（仅在测试阶段使用）
                    const firstPairIndex = 1;
                    const originalSize = 1024 * 1024; // 1MB
                    const compressedSize = 512 * 1024; // 0.5MB
                    showCompressionInfo(originalSize, compressedSize, firstPairIndex);
                }
            }, 500);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 首先检查用户是否已登录
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to parse user data:', error);
            }
            
            // 如果用户未登录，重定向到登录页面
            if (!loggedInUser || !loggedInUser.username) {
                alert('请先登录再进行下单');
                window.location.href = 'login.html';
                return;
            }
            
            // 从服务器获取地址数据
            fetch(`/api/get-addresses?username=${encodeURIComponent(loggedInUser.username)}`)
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    // 将地址数据保存到缓存
                    window.savedAddressesCache = result.data || [];
                    
                    // 然后加载保存的地址并更新地址数量显示
                    loadSavedAddresses();
                } else {
                    console.warn('Failed to load addresses:', result.message);
                    // 初始化为空数组
                    window.savedAddressesCache = [];
                    loadSavedAddresses();
                }
            })
            .catch(error => {
                console.error('Error loading addresses:', error);
                // 初始化为空数组
                window.savedAddressesCache = [];
                loadSavedAddresses();
            });
            
            // 检查是否需要隐藏地址限制提示
            fetch(`/api/get-address-limit-tip-state?username=${encodeURIComponent(loggedInUser.username)}`)
            .then(response => response.json())
            .then(result => {
                if (result.success && result.hide) {
                    const addressLimitTip = document.getElementById('addressLimitTip');
                    if (addressLimitTip) {
                        addressLimitTip.style.display = 'none';
                    }
                }
            })
            .catch(error => {
                console.error('Error loading address limit tip state:', error);
            });
            
            // 为已有的地址卡片添加点击事件
            document.querySelectorAll('.address-card').forEach(card => {
                // 移除旧的onclick属性
                card.removeAttribute('onclick');
                
                // 添加新的事件监听器
                card.addEventListener('click', function(e) {
                    // 如果点击的是按钮或按钮内的元素，不处理选择逻辑
                    if (e.target.closest('.address-actions')) {
                        return;
                    }
                    selectAddressFromPopup(this);
                });
            });
            
            // 加载表单数据
            loadFormData();
            
            // 保存初始的国家选择值
            const initialCountrySelect = document.getElementById('country');
            if (initialCountrySelect) {
                sessionStorage.setItem('previousCountry', initialCountrySelect.value);
            }
            
            // 修改从服务器加载当前步骤的逻辑，增加表单验证
            fetch(`/api/get-current-step?username=${encodeURIComponent(loggedInUser.username)}`)
            .then(response => response.json())
            .then(result => {
                if (result.success && result.step === '2') {
                    // 在切换到第二步前，先验证第一步表单是否填写完整
                    const form = document.getElementById('addressForm');
                    if (!form.checkValidity()) {
                        // 如果表单验证不通过，不跳转到第二步
                        // 并清除保存的步骤状态
                        fetch(`/api/delete-current-step?username=${encodeURIComponent(loggedInUser.username)}`, {
                            method: 'DELETE'
                        });
                        return;
                    }
                    
                    // 如果表单验证通过，才跳转到第二步
                    navigateToStep2(false);
                }
            })
            .catch(error => {
                console.error('Error loading current step:', error);
            });
            
            // 确保在添加完所有dom元素后调用
            window.addEventListener('load', function() {
                // 从服务器获取当前步骤
                fetch(`/api/get-current-step?username=${encodeURIComponent(loggedInUser.username)}`)
                .then(response => response.json())
                .then(result => {
                    if (result.success && result.step === '2') {
                        // 再次确认表单验证通过
                        const form = document.getElementById('addressForm');
                        if (!form.checkValidity()) {
                            // 如果验证不通过，强制回到第一步
                            navigateToStep1();
                            return;
                        }
                        
                        // 确保物流码和图片被正确恢复 - 先更新运输方式选项，再恢复数据
                        setTimeout(() => {
                            // 首先确保运输方式选项根据国家/地区正确设置
                            updateShippingOptions();
                            // 然后恢复保存的物流码数据，包括之前选择的运输方式
                            setTimeout(restoreLogisticsData, 100);
                        }, 300);
                    }
                })
                .catch(error => {
                    console.error('Error loading current step:', error);
                });
            });
            
            // 添加表单字段的change事件监听器，自动保存数据
            document.querySelectorAll('#addressForm input, #addressForm select, #addressForm textarea').forEach(element => {
                element.addEventListener('change', saveFormData);
                element.addEventListener('input', saveFormData);
            });
            
            // 为返回用户中心按钮添加确认对话框
            const backButton = document.querySelector('.back-button');
            if (backButton) {
                backButton.onclick = function(e) {
                    e.preventDefault();
                    confirmSaveFormData('dashboard.html');
                    return false;
                };
            }
            
            // 为取消按钮添加确认对话框
            const cancelButton = document.querySelector('.buttons-container .btn-outline');
            if (cancelButton && cancelButton.textContent === '取消') {
                cancelButton.onclick = function() {
                    confirmSaveFormData('dashboard.html');
                };
            }
            
            // 为国家/地区选择添加更改事件监听器，用于更新运输方式选项
            const countrySelect = document.getElementById('country');
            if (countrySelect) {
                countrySelect.addEventListener('change', updateShippingOptions);
                // 页面加载时立即执行一次，根据当前选择的国家/地区更新选项
                setTimeout(updateShippingOptions, 500);
            }
            
            // 为收件人姓名添加输入验证
            const nameInput = document.getElementById('name');
            if (nameInput) {
                // 记录上一次输入长度
                let prevNameLength = 0;
                
                nameInput.addEventListener('input', function(e) {
                    // 检查是否尝试输入更多字符
                    if (this.value.length >= 57 && prevNameLength <= this.value.length) {
                        // 判断是否已经显示过警告，避免重复显示
                        if (!this.dataset.warningShown) {
                            alert('收件人姓名不能超过57个字符');
                            this.dataset.warningShown = 'true'; // 标记已显示警告
                            
                            // 5秒后重置标记，允许再次显示警告
                            setTimeout(() => {
                                this.dataset.warningShown = '';
                            }, 5000);
                        }
                        
                        this.value = this.value.substring(0, 57); // 截断为57个字符
                    }
                    
                    // 更新记录的长度
                    prevNameLength = this.value.length;
                });
            }
            
            // 为联系电话添加输入验证
            const phoneInput = document.getElementById('phone');
            if (phoneInput) {
                // 记录上一次输入长度
                let prevPhoneLength = 0;
                
                phoneInput.addEventListener('input', function() {
                    // 检查是否尝试输入更多字符
                    if (this.value.length >= 30 && prevPhoneLength <= this.value.length) {
                        // 判断是否已经显示过警告，避免重复显示
                        if (!this.dataset.warningShown) {
                            alert('联系电话不能超过30个字符');
                            this.dataset.warningShown = 'true'; // 标记已显示警告
                            
                            // 5秒后重置标记，允许再次显示警告
                            setTimeout(() => {
                                this.dataset.warningShown = '';
                            }, 5000);
                        }
                        
                        this.value = this.value.substring(0, 30); // 截断为30个字符
                    }
                    
                    // 更新记录的长度
                    prevPhoneLength = this.value.length;
                });
            }
            
            // 为城市添加输入验证
            const cityInput = document.getElementById('city');
            if (cityInput) {
                // 记录上一次输入长度
                let prevCityLength = 0;
                
                cityInput.addEventListener('input', function() {
                    // 检查是否尝试输入更多字符
                    if (this.value.length >= 30 && prevCityLength <= this.value.length) {
                        // 判断是否已经显示过警告，避免重复显示
                        if (!this.dataset.warningShown) {
                            alert('城市不能超过30个字符');
                            this.dataset.warningShown = 'true'; // 标记已显示警告
                            
                            // 5秒后重置标记，允许再次显示警告
                            setTimeout(() => {
                                this.dataset.warningShown = '';
                            }, 5000);
                        }
                        
                        this.value = this.value.substring(0, 30); // 截断为30个字符
                    }
                    
                    // 更新记录的长度
                    prevCityLength = this.value.length;
                });
            }
            
            // 为邮政编码添加输入验证
            const zipCodeInput = document.getElementById('zipCode');
            if (zipCodeInput) {
                // 记录上一次输入长度
                let prevZipCodeLength = 0;
                
                zipCodeInput.addEventListener('input', function() {
                    // 检查是否尝试输入更多字符
                    if (this.value.length >= 20 && prevZipCodeLength <= this.value.length) {
                        // 判断是否已经显示过警告，避免重复显示
                        if (!this.dataset.warningShown) {
                            alert('邮政编码不能超过20个字符');
                            this.dataset.warningShown = 'true'; // 标记已显示警告
                            
                            // 5秒后重置标记，允许再次显示警告
                            setTimeout(() => {
                                this.dataset.warningShown = '';
                            }, 5000);
                        }
                        
                        this.value = this.value.substring(0, 20); // 截断为20个字符
                    }
                    
                    // 更新记录的长度
                    prevZipCodeLength = this.value.length;
                });
            }
            
            // 为详细地址添加输入验证
            const addressInput = document.getElementById('address');
            if (addressInput) {
                // 记录上一次输入长度
                let prevAddressLength = 0;
                
                addressInput.addEventListener('input', function() {
                    // 检查是否尝试输入更多字符
                    if (this.value.length >= 189 && prevAddressLength <= this.value.length) {
                        // 判断是否已经显示过警告，避免重复显示
                        if (!this.dataset.warningShown) {
                            alert('详细地址不能超过189个字符');
                            this.dataset.warningShown = 'true'; // 标记已显示警告
                            
                            // 5秒后重置标记，允许再次显示警告
                            setTimeout(() => {
                                this.dataset.warningShown = '';
                            }, 5000);
                        }
                        
                        this.value = this.value.substring(0, 189); // 截断为189个字符
                    }
                    
                    // 更新记录的长度
                    prevAddressLength = this.value.length;
                });
            }
            
            // 初始化新地址表单的输入监听器
            setupNewAddressInputListeners();
        });

        // 从服务器加载保存的地址
        function loadSavedAddresses() {
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            // 如果找不到登录用户，则不加载地址
            if (!loggedInUser || !loggedInUser.username) {
                console.warn('No logged in user found, cannot load addresses');
                return;
            }
            
            // 使用缓存中的地址数据
            const savedAddresses = window.savedAddressesCache || [];
            
            const addressesContainer = document.querySelectorAll('.saved-addresses');
            
            // 更新地址数量显示 - 修改这部分，确保显示正确的数量
            const addressCountElement = document.getElementById('addressCount');
            if (addressCountElement) {
                addressCountElement.textContent = savedAddresses.length;
                // 如果没有地址，隐藏数字标签
                if (savedAddresses.length === 0) {
                    addressCountElement.style.display = 'none';
                } else {
                    addressCountElement.style.display = 'inline-block';
                }
            }
            
            // 更新弹窗中的地址数量显示
            const addressCountInPopup = document.getElementById('addressCountInPopup');
            if (addressCountInPopup) {
                addressCountInPopup.textContent = savedAddresses.length;
            }
            
            // 清空现有地址卡片
            addressesContainer.forEach(container => {
                container.innerHTML = '';
            });
            
            // 添加保存的地址
            savedAddresses.forEach(addressData => {
                const addressCard = createAddressCard(
                    addressData.name,
                    addressData.phone,
                    addressData.address,
                    addressData.zipCode,
                    addressData.city || "",
                    addressData.country || "中国"
                );
                
                // 添加到所有地址容器
                addressesContainer.forEach(container => {
                    const clonedCard = addressCard.cloneNode(true);
                    
                    // 确保为克隆的卡片添加事件监听器
                    clonedCard.addEventListener('click', function(e) {
                        // 如果点击的是按钮或按钮内的元素，不处理选择逻辑
                        if (e.target.closest('.address-actions')) {
                            return;
                        }
                        selectAddressFromPopup(this);
                    });
                    
                    // 为编辑和删除按钮添加事件监听器
                    const editBtn = clonedCard.querySelector('.edit-btn');
                    if (editBtn) {
                        editBtn.addEventListener('click', function(e) {
                            editAddress(this, e);
                        });
                    }
                    
                    const deleteBtn = clonedCard.querySelector('.delete-btn');
                    if (deleteBtn) {
                        deleteBtn.addEventListener('click', function(e) {
                            deleteAddress(this, e);
                        });
                    }
                    
                    container.appendChild(clonedCard);
                });
            });
        }
        
        // 创建地址卡片元素
        function createAddressCard(name, phone, address, zipCode, city = "", country = "中国") {
            const newAddressCard = document.createElement('div');
            newAddressCard.className = 'address-card';
            
            // 创建内容容器，用于两列布局
            const contentDiv = document.createElement('div');
            contentDiv.className = 'address-card-content';
            
            // 创建上半部分容器
            const topRow = document.createElement('div');
            topRow.className = 'address-top-row';
            
            // 创建左列
            const leftCol = document.createElement('div');
            leftCol.className = 'address-left-col';
            
            // 创建姓名字段
            const nameField = document.createElement('div');
            nameField.className = 'address-field';
            
            const nameLabel = document.createElement('div');
            nameLabel.className = 'address-field-label';
            nameLabel.textContent = '姓名：';
            
            const nameValue = document.createElement('div');
            nameValue.className = 'address-field-value';
            nameValue.textContent = name;
            
            nameField.appendChild(nameLabel);
            nameField.appendChild(nameValue);
            
            // 创建电话字段
            const phoneField = document.createElement('div');
            phoneField.className = 'address-field';
            
            const phoneLabel = document.createElement('div');
            phoneLabel.className = 'address-field-label';
            phoneLabel.textContent = '电话：';
            
            const phoneValue = document.createElement('div');
            phoneValue.className = 'address-field-value';
            phoneValue.textContent = phone;
            
            phoneField.appendChild(phoneLabel);
            phoneField.appendChild(phoneValue);
            
            leftCol.appendChild(nameField);
            leftCol.appendChild(phoneField);
            
            // 创建右列
            const rightCol = document.createElement('div');
            rightCol.className = 'address-right-col';
            
            // 创建国家字段 - 移至右侧
            const countryField = document.createElement('div');
            countryField.className = 'address-field';
            
            const countryLabel = document.createElement('div');
            countryLabel.className = 'address-field-label';
                            countryLabel.textContent = '国家：';
            
            const countryValue = document.createElement('div');
            countryValue.className = 'address-field-value';
            countryValue.textContent = country;
            
            countryField.appendChild(countryLabel);
            countryField.appendChild(countryValue);
            
            // 创建城市字段
            const cityField = document.createElement('div');
            cityField.className = 'address-field';
            
            const cityLabel = document.createElement('div');
            cityLabel.className = 'address-field-label';
            cityLabel.textContent = '城市：';
            
            const cityValue = document.createElement('div');
            cityValue.className = 'address-field-value';
            cityValue.textContent = city || "未指定城市";
            
            cityField.appendChild(cityLabel);
            cityField.appendChild(cityValue);
            
            // 创建邮编字段
            const zipField = document.createElement('div');
            zipField.className = 'address-field';
            
            const zipLabel = document.createElement('div');
            zipLabel.className = 'address-field-label';
            zipLabel.textContent = '邮政编码：';
            
            const zipValue = document.createElement('div');
            zipValue.className = 'address-field-value';
            zipValue.textContent = zipCode;
            
            zipField.appendChild(zipLabel);
            zipField.appendChild(zipValue);
            
            // 添加字段到右列
            rightCol.appendChild(countryField);
            rightCol.appendChild(cityField);
            rightCol.appendChild(zipField);
            
            // 添加左右列到上半部分
            topRow.appendChild(leftCol);
            topRow.appendChild(rightCol);
            
            // 创建下半部分容器
            const bottomRow = document.createElement('div');
            bottomRow.className = 'address-bottom-row';
            
            // 创建地址字段
            const addressField = document.createElement('div');
            addressField.className = 'address-field address-detail';
            
            const addressLabel = document.createElement('div');
            addressLabel.className = 'address-field-label';
            addressLabel.textContent = '详细地址：';
            
            const addressValue = document.createElement('div');
            addressValue.className = 'address-field-value';
            addressValue.textContent = address;
            
            addressField.appendChild(addressLabel);
            addressField.appendChild(addressValue);
            
            bottomRow.appendChild(addressField);
            
            // 添加上下部分到内容容器
            contentDiv.appendChild(topRow);
            contentDiv.appendChild(bottomRow);
            
            // 添加内容容器到卡片
            newAddressCard.appendChild(contentDiv);
            
            // 添加编辑和删除按钮
            const actionsDiv = document.createElement('div');
            actionsDiv.className = 'address-actions';
            
            const editBtn = document.createElement('button');
            editBtn.className = 'edit-btn';
            editBtn.innerHTML = '<i class="fas fa-edit"></i>';
            editBtn.addEventListener('click', function(e) {
                editAddress(this, e);
            });
            
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-btn';
            deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
            deleteBtn.addEventListener('click', function(e) {
                deleteAddress(this, e);
            });
            
            actionsDiv.appendChild(editBtn);
            actionsDiv.appendChild(deleteBtn);
            
            newAddressCard.appendChild(actionsDiv);
            
            // 添加点击事件
            newAddressCard.addEventListener('click', function(e) {
                // 如果点击的是按钮或按钮内的元素，不处理选择逻辑
                if (e.target.closest('.address-actions')) {
                    return;
                }
                selectAddressFromPopup(this);
            });
            
            return newAddressCard;
        }
        
        // 选择已保存的地址
        function selectAddress(element) {
            document.querySelectorAll('.address-card').forEach(card => {
                card.classList.remove('selected');
            });
            element.classList.add('selected');
            
                    // 自动填充表单
        const topRow = element.querySelector('.address-top-row');
        const leftCol = topRow.querySelector('.address-left-col');
        const rightCol = topRow.querySelector('.address-right-col');
        const bottomRow = element.querySelector('.address-bottom-row');
        
        const name = leftCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent;
        const phone = leftCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent;
        const country = rightCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent;
        const city = rightCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent;
        const zipCode = rightCol.querySelector('.address-field:nth-child(3) .address-field-value').textContent;
        const address = bottomRow.querySelector('.address-field .address-field-value').textContent;
        
        document.getElementById('name').value = name;
        document.getElementById('phone').value = phone;
        document.getElementById('country').value = country;
        document.getElementById('city').value = city !== "未指定城市" ? city : "";
        document.getElementById('zipCode').value = zipCode;
        document.getElementById('address').value = address;
        
        // 更新自定义下拉菜单显示
        const customCountrySelect = document.getElementById('customCountrySelect');
        if (customCountrySelect) {
            const trigger = customCountrySelect.querySelector('.custom-select-trigger');
            if (trigger) {
                trigger.textContent = country;
            }
        }
        
        // 保存表单数据到localStorage
        saveFormData();
        
        // 触发国家选择变更事件，更新运输方式选项
        const countrySelect = document.getElementById('country');
        if (countrySelect) {
            const event = new Event('change');
            countrySelect.dispatchEvent(event);
        }
            
            // 隐藏地址选择面板
            document.getElementById('savedAddressesContainer').style.display = 'none';
        }
        
        // 从弹窗中选择地址
        function selectAddressFromPopup(element) {
            // 移除其他选中状态
            document.querySelectorAll('.address-card').forEach(card => {
                card.classList.remove('selected');
            });
            // 添加选中状态
            element.classList.add('selected');
        }
        
        // 编辑地址
        function editAddress(button, event) {
            event.stopPropagation(); // 阻止事件冒泡
            const addressCard = button.closest('.address-card');
            
            // 获取地址信息
            const topRow = addressCard.querySelector('.address-top-row');
            const leftCol = topRow.querySelector('.address-left-col');
            const rightCol = topRow.querySelector('.address-right-col');
            const bottomRow = addressCard.querySelector('.address-bottom-row');
            
            const name = leftCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent;
            const phone = leftCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent;
            const country = rightCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent;
            const city = rightCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent;
            const zipCode = rightCol.querySelector('.address-field:nth-child(3) .address-field-value').textContent;
            const address = bottomRow.querySelector('.address-field .address-field-value').textContent;
            
            // 保存原始数据，用于后续更新其他相同的地址卡片
            window.currentEditingCardOriginalData = {
                name: name,
                phone: phone,
                country: country,
                city: city,
                zipCode: zipCode,
                address: address
            };
            
            // 填充编辑表单
            document.getElementById('newName').value = name;
            document.getElementById('newPhone').value = phone;
            document.getElementById('newCountry').value = country;
            
            // 更新自定义下拉菜单显示
            const customNewCountrySelect = document.getElementById('customNewCountrySelect');
            if (customNewCountrySelect) {
                const trigger = customNewCountrySelect.querySelector('.custom-select-trigger');
                if (trigger) {
                    // 显示选中的国家/地区
                    trigger.textContent = country;
                }
            }
            
            document.getElementById('newCity').value = city !== "未指定城市" ? city : "";
            document.getElementById('newZipCode').value = zipCode;
            document.getElementById('newAddress').value = address;
            
            // 显示编辑表单
            document.getElementById('savedAddressesList').style.display = 'none';
            document.getElementById('newAddressForm').style.display = 'block';
            document.querySelector('.popup-title').textContent = '编辑地址';
            
            // 标记为编辑模式，保存当前编辑的卡片引用
            window.currentEditingCard = addressCard;
            
            // 添加输入事件监听器，提供实时字符限制反馈
            setupNewAddressInputListeners();
        }
        
        // 删除地址
        function deleteAddress(button, event) {
            event.stopPropagation(); // 阻止事件冒泡
            if(confirm('确定要删除这个地址吗？')) {
                const addressCard = button.closest('.address-card');
                
                // 获取要删除的地址信息用于比对
                const topRow = addressCard.querySelector('.address-top-row');
                const leftCol = topRow.querySelector('.address-left-col');
                const rightCol = topRow.querySelector('.address-right-col');
                const bottomRow = addressCard.querySelector('.address-bottom-row');
                
                const name = leftCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent;
                const phone = leftCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent;
                const country = rightCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent;
                const address = bottomRow.querySelector('.address-field .address-field-value').textContent;
                
                // 从DOM中删除
                addressCard.remove();
                
                // 从所有地址容器中删除相同的地址卡片
                document.querySelectorAll('.saved-addresses').forEach(container => {
                    const cards = container.querySelectorAll('.address-card');
                    cards.forEach(card => {
                        const cardTopRow = card.querySelector('.address-top-row');
                        const cardLeftCol = cardTopRow.querySelector('.address-left-col');
                        const cardRightCol = cardTopRow.querySelector('.address-right-col');
                        const cardBottomRow = card.querySelector('.address-bottom-row');
                        
                        const cardName = cardLeftCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent;
                        const cardPhone = cardLeftCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent;
                        const cardCountry = cardRightCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent;
                        const cardAddress = cardBottomRow.querySelector('.address-field .address-field-value').textContent;
                        
                        // 如果找到相同的地址卡片，也删除它
                        if (cardName === name && cardPhone === phone && cardCountry === country && cardAddress === address) {
                            card.remove();
                        }
                    });
                });
                
                // 计算剩余的地址数量
                const remainingAddressCount = document.querySelectorAll('.saved-addresses')[0].querySelectorAll('.address-card').length;
                
                // 从localStorage中删除并更新地址数量显示
                saveAddressesToLocalStorage();
                
                // 直接更新弹窗标题中的地址数量
                document.querySelector('.popup-title').textContent = `已保存的地址 (${remainingAddressCount})`;
                
                // 更新弹窗中的地址数量显示
                const addressCountInPopup = document.getElementById('addressCountInPopup');
                if (addressCountInPopup) {
                    addressCountInPopup.textContent = remainingAddressCount;
                }
                
                // 更新主页面上的地址数量显示
                const addressCountElement = document.getElementById('addressCount');
                if (addressCountElement) {
                    addressCountElement.textContent = remainingAddressCount;
                    // 如果没有地址，隐藏数字标签
                    if (remainingAddressCount === 0) {
                        addressCountElement.style.display = 'none';
                    } else {
                        addressCountElement.style.display = 'inline-block';
                    }
                }
                
                // 更新全局缓存中的地址数据
                if (window.savedAddressesCache) {
                    window.savedAddressesCache = Array.from(document.querySelectorAll('.saved-addresses')[0].querySelectorAll('.address-card')).map(card => {
                        const cardTopRow = card.querySelector('.address-top-row');
                        const cardLeftCol = cardTopRow.querySelector('.address-left-col');
                        const cardRightCol = cardTopRow.querySelector('.address-right-col');
                        const cardBottomRow = card.querySelector('.address-bottom-row');
                        
                        return {
                            name: cardLeftCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent,
                            phone: cardLeftCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent,
                            country: cardRightCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent,
                            city: cardRightCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent,
                            zipCode: cardRightCol.querySelector('.address-field:nth-child(3) .address-field-value').textContent,
                            address: cardBottomRow.querySelector('.address-field .address-field-value').textContent
                        };
                    });
                }
            }
        }
        
        // 显示添加地址表单
        function showAddAddressForm() {
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            // 如果找不到登录用户，则不允许添加地址
            if (!loggedInUser || !loggedInUser.username) {
                alert('请先登录再添加地址');
                return;
            }
            
            // 检查已保存的地址数量是否达到上限
            const savedAddresses = window.savedAddressesCache || [];
            if (savedAddresses.length >= 20) {
                alert('已保存的地址已达到上限(20个)，请删除不需要的地址后再添加新地址。');
                return;
            }
            
            document.getElementById('savedAddressesList').style.display = 'none';
            document.getElementById('newAddressForm').style.display = 'block';
            document.querySelector('.popup-title').textContent = '添加新地址';
            
            // 清空表单，确保不是编辑模式
            document.getElementById('newName').value = '';
            document.getElementById('newPhone').value = '';
            
            // 重置国家/地区选择为默认的空值
            document.getElementById('newCountry').value = '';
            
            // 更新自定义下拉菜单的显示文本为默认提示
            const newCountryCustomSelect = document.getElementById('customNewCountrySelect');
            if (newCountryCustomSelect) {
                const trigger = newCountryCustomSelect.querySelector('.custom-select-trigger');
                if (trigger) trigger.textContent = '请选择国家';
            }
            document.getElementById('newCity').value = '';
            document.getElementById('newZipCode').value = '';
            document.getElementById('newAddress').value = '';
            
            // 清除编辑模式标记
            window.currentEditingCard = null;
            
            // 添加输入事件监听器，提供实时字符限制反馈
            setupNewAddressInputListeners();
        }
        
        // 为新增地址表单添加输入事件监听器
        function setupNewAddressInputListeners() {
            // 为收件人姓名添加输入验证
            const nameInput = document.getElementById('newName');
            if (nameInput) {
                // 记录上一次输入长度
                let prevNameLength = 0;
                
                nameInput.addEventListener('input', function(e) {
                    // 检查是否尝试输入更多字符
                    if (this.value.length >= 57 && prevNameLength <= this.value.length) {
                        // 判断是否已经显示过警告，避免重复显示
                        if (!this.dataset.warningShown) {
                            alert('收件人姓名不能超过57个字符');
                            this.dataset.warningShown = 'true'; // 标记已显示警告
                            
                            // 5秒后重置标记，允许再次显示警告
                            setTimeout(() => {
                                this.dataset.warningShown = '';
                            }, 5000);
                        }
                        
                        this.value = this.value.substring(0, 57); // 截断为57个字符
                    }
                    
                    // 更新记录的长度
                    prevNameLength = this.value.length;
                });
            }
            
            // 为联系电话添加输入验证
            const phoneInput = document.getElementById('newPhone');
            if (phoneInput) {
                // 记录上一次输入长度
                let prevPhoneLength = 0;
                
                phoneInput.addEventListener('input', function() {
                    // 检查是否尝试输入更多字符
                    if (this.value.length >= 30 && prevPhoneLength <= this.value.length) {
                        // 判断是否已经显示过警告，避免重复显示
                        if (!this.dataset.warningShown) {
                            alert('联系电话不能超过30个字符');
                            this.dataset.warningShown = 'true'; // 标记已显示警告
                            
                            // 5秒后重置标记，允许再次显示警告
                            setTimeout(() => {
                                this.dataset.warningShown = '';
                            }, 5000);
                        }
                        
                        this.value = this.value.substring(0, 30); // 截断为30个字符
                    }
                    
                    // 更新记录的长度
                    prevPhoneLength = this.value.length;
                });
            }
            
            // 为城市添加输入验证
            const cityInput = document.getElementById('newCity');
            if (cityInput) {
                // 记录上一次输入长度
                let prevCityLength = 0;
                
                cityInput.addEventListener('input', function() {
                    // 检查是否尝试输入更多字符
                    if (this.value.length >= 30 && prevCityLength <= this.value.length) {
                        // 判断是否已经显示过警告，避免重复显示
                        if (!this.dataset.warningShown) {
                            alert('城市不能超过30个字符');
                            this.dataset.warningShown = 'true'; // 标记已显示警告
                            
                            // 5秒后重置标记，允许再次显示警告
                            setTimeout(() => {
                                this.dataset.warningShown = '';
                            }, 5000);
                        }
                        
                        this.value = this.value.substring(0, 30); // 截断为30个字符
                    }
                    
                    // 更新记录的长度
                    prevCityLength = this.value.length;
                });
            }
            
            // 为邮政编码添加输入验证
            const zipCodeInput = document.getElementById('newZipCode');
            if (zipCodeInput) {
                // 记录上一次输入长度
                let prevZipCodeLength = 0;
                
                zipCodeInput.addEventListener('input', function() {
                    // 检查是否尝试输入更多字符
                    if (this.value.length >= 20 && prevZipCodeLength <= this.value.length) {
                        // 判断是否已经显示过警告，避免重复显示
                        if (!this.dataset.warningShown) {
                            alert('邮政编码不能超过20个字符');
                            this.dataset.warningShown = 'true'; // 标记已显示警告
                            
                            // 5秒后重置标记，允许再次显示警告
                            setTimeout(() => {
                                this.dataset.warningShown = '';
                            }, 5000);
                        }
                        
                        this.value = this.value.substring(0, 20); // 截断为20个字符
                    }
                    
                    // 更新记录的长度
                    prevZipCodeLength = this.value.length;
                });
            }
            
            // 为详细地址添加输入验证
            const addressInput = document.getElementById('newAddress');
            if (addressInput) {
                // 记录上一次输入长度
                let prevAddressLength = 0;
                
                addressInput.addEventListener('input', function() {
                    // 检查是否尝试输入更多字符
                    if (this.value.length >= 189 && prevAddressLength <= this.value.length) {
                        // 判断是否已经显示过警告，避免重复显示
                        if (!this.dataset.warningShown) {
                            alert('详细地址不能超过189个字符');
                            this.dataset.warningShown = 'true'; // 标记已显示警告
                            
                            // 5秒后重置标记，允许再次显示警告
                            setTimeout(() => {
                                this.dataset.warningShown = '';
                            }, 5000);
                        }
                        
                        this.value = this.value.substring(0, 189); // 截断为189个字符
                    }
                    
                    // 更新记录的长度
                    prevAddressLength = this.value.length;
                });
            }
        }
        
        // 取消添加地址
        function cancelAddAddress() {
            document.getElementById('savedAddressesList').style.display = 'block';
            document.getElementById('newAddressForm').style.display = 'none';
            
            // 更新弹窗标题，显示地址数量
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            // 如果找不到登录用户，则不显示地址数量
            let savedAddresses = [];
            if (loggedInUser && loggedInUser.username) {
                const storageKey = `savedAddresses_${loggedInUser.username}`;
                savedAddresses = JSON.parse(localStorage.getItem(storageKey)) || [];
            }
            
            document.querySelector('.popup-title').textContent = `已保存的地址 (${savedAddresses.length})`;
        }
        
        // 保存新地址
            function saveNewAddress() {
        // 获取当前登录用户信息
        let loggedInUser = null;
        try {
            const userDataStr = sessionStorage.getItem('loggedInUser');
            if (userDataStr) {
                loggedInUser = JSON.parse(userDataStr);
            }
        } catch (error) {
            console.warn('Failed to get user data:', error);
        }
        
        // 如果找不到登录用户，则不保存地址
        if (!loggedInUser || !loggedInUser.username) {
            alert('请先登录再保存地址');
            return;
        }
        
        const name = document.getElementById('newName').value;
        const phone = document.getElementById('newPhone').value;
        const country = document.getElementById('newCountry').value;
        const city = document.getElementById('newCity').value;
        const zipCode = document.getElementById('newZipCode').value;
        const address = document.getElementById('newAddress').value;
        
        // 验证必填字段
        if (!name || !phone || !country || !city || !zipCode || !address) {
            alert('请填写完整的地址信息');
            return;
        }
        
        // 验证字符长度
        if (name.length > 57) {
            alert('收件人姓名不能超过57个字符');
            return;
        }
        
        if (phone.length > 30) {
            alert('联系电话不能超过30个字符');
            return;
        }
        
        if (city.length > 30) {
            alert('城市不能超过30个字符');
            return;
        }
        
        if (zipCode.length > 20) {
            alert('邮政编码不能超过20个字符');
            return;
        }
        
        if (address.length > 189) {
            alert('详细地址不能超过189个字符');
            return;
        }
        
        // 检查是否是添加新地址模式（非编辑模式）
        if (!window.currentEditingCard) {
            // 检查已保存的地址数量是否达到上限
            const storageKey = `savedAddresses_${loggedInUser.username}`;
            const savedAddresses = JSON.parse(localStorage.getItem(storageKey)) || [];
            if (savedAddresses.length >= 20) {
                alert('已保存的地址已达到上限(20个)，请删除不需要的地址后再添加新地址。');
                return;
            }
        }
        
        if (window.currentEditingCard) {
            // 编辑模式 - 更新现有地址卡片
            const topRow = window.currentEditingCard.querySelector('.address-top-row');
            const leftCol = topRow.querySelector('.address-left-col');
            const rightCol = topRow.querySelector('.address-right-col');
            const bottomRow = window.currentEditingCard.querySelector('.address-bottom-row');
            
            leftCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent = name;
            leftCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent = phone;
            rightCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent = country;
            
            rightCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent = city;
            rightCol.querySelector('.address-field:nth-child(3) .address-field-value').textContent = zipCode;
            
            bottomRow.querySelector('.address-field .address-field-value').textContent = address;
            
            // 更新所有地址容器中的相同地址卡片
            const originalName = window.currentEditingCardOriginalData?.name;
            const originalPhone = window.currentEditingCardOriginalData?.phone;
            const originalCountry = window.currentEditingCardOriginalData?.country;
            const originalAddress = window.currentEditingCardOriginalData?.address;
            
            if (originalName && originalPhone && originalCountry && originalAddress) {
                document.querySelectorAll('.saved-addresses').forEach(container => {
                    if (container !== window.currentEditingCard.closest('.saved-addresses')) {
                        const cards = container.querySelectorAll('.address-card');
                        cards.forEach(card => {
                            const cardTopRow = card.querySelector('.address-top-row');
                            const cardLeftCol = cardTopRow.querySelector('.address-left-col');
                            const cardRightCol = cardTopRow.querySelector('.address-right-col');
                            const cardBottomRow = card.querySelector('.address-bottom-row');
                            
                            const cardName = cardLeftCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent;
                            const cardPhone = cardLeftCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent;
                            const cardCountry = cardRightCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent;
                            const cardAddress = cardBottomRow.querySelector('.address-field .address-field-value').textContent;
                            
                            // 如果找到相同的地址卡片，也更新它
                            if (cardName === originalName && cardPhone === originalPhone && 
                                cardCountry === originalCountry && cardAddress === originalAddress) {
                                cardLeftCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent = name;
                                cardLeftCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent = phone;
                                cardRightCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent = country;
                                cardRightCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent = city;
                                cardRightCol.querySelector('.address-field:nth-child(3) .address-field-value').textContent = zipCode;
                                cardBottomRow.querySelector('.address-field .address-field-value').textContent = address;
                            }
                        });
                    }
                });
            }
            
            // 重置编辑模式
            window.currentEditingCard = null;
            window.currentEditingCardOriginalData = null;
            
            // 提示成功
            alert('地址已更新');
        } else {
            // 添加模式 - 创建新的地址卡片
            const newAddressCard = createAddressCard(name, phone, address, zipCode, city, country);
            
            // 添加到所有已保存的地址列表
            document.querySelectorAll('.saved-addresses').forEach(container => {
                const clonedCard = newAddressCard.cloneNode(true);
                
                // 确保为克隆的卡片添加事件监听器
                clonedCard.addEventListener('click', function(e) {
                    // 如果点击的是按钮或按钮内的元素，不处理选择逻辑
                    if (e.target.closest('.address-actions')) {
                        return;
                    }
                    selectAddressFromPopup(this);
                });
                
                // 为编辑和删除按钮添加事件监听器
                const editBtn = clonedCard.querySelector('.edit-btn');
                if (editBtn) {
                    editBtn.addEventListener('click', function(e) {
                        editAddress(this, e);
                    });
                }
                
                const deleteBtn = clonedCard.querySelector('.delete-btn');
                if (deleteBtn) {
                    deleteBtn.addEventListener('click', function(e) {
                        deleteAddress(this, e);
                    });
                }
                
                container.appendChild(clonedCard);
            });
            
            // 提示成功
            alert('新地址已添加');
        }
        
        // 保存到localStorage并更新地址数量显示
        saveAddressesToLocalStorage();
        
        // 更新弹窗标题中的地址数量
        const savedAddresses = JSON.parse(localStorage.getItem('savedAddresses')) || [];
        document.querySelector('.popup-title').textContent = `已保存的地址 (${savedAddresses.length})`;
        
        // 更新弹窗中的地址数量显示
        const addressCountInPopup = document.getElementById('addressCountInPopup');
        if (addressCountInPopup) {
            addressCountInPopup.textContent = savedAddresses.length;
        }
        
        // 填充主表单
        document.getElementById('name').value = name;
        document.getElementById('phone').value = phone;
        document.getElementById('country').value = country;
        document.getElementById('city').value = city;
        document.getElementById('zipCode').value = zipCode;
        document.getElementById('address').value = address;
        
        // 更新自定义下拉菜单显示
        const customCountrySelect = document.getElementById('customCountrySelect');
        if (customCountrySelect) {
            const trigger = customCountrySelect.querySelector('.custom-select-trigger');
            if (trigger) {
                trigger.textContent = country;
            }
        }
        
        // 触发国家选择变更事件，更新运输方式选项
        const countrySelect = document.getElementById('country');
        if (countrySelect) {
            const event = new Event('change');
            countrySelect.dispatchEvent(event);
        }
        
        // 关闭弹窗
        closeAddressPopup();
    }
        
        // 保存地址到服务器
        function saveAddressesToLocalStorage() {
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            // 如果找不到登录用户，则不保存地址
            if (!loggedInUser || !loggedInUser.username) {
                console.warn('No logged in user found, cannot save addresses');
                alert('请先登录再保存地址');
                return;
            }
            
            // 获取第一个地址容器中的所有地址卡片
            const addressCards = document.querySelectorAll('.saved-addresses')[0].querySelectorAll('.address-card');
            const addresses = [];
            
            // 遍历所有地址卡片，提取信息
            addressCards.forEach(card => {
                const topRow = card.querySelector('.address-top-row');
                const leftCol = topRow.querySelector('.address-left-col');
                const rightCol = topRow.querySelector('.address-right-col');
                const bottomRow = card.querySelector('.address-bottom-row');
                
                addresses.push({
                    name: leftCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent,
                    phone: leftCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent,
                    country: rightCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent,
                    city: rightCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent,
                    zipCode: rightCol.querySelector('.address-field:nth-child(3) .address-field-value').textContent,
                    address: bottomRow.querySelector('.address-field .address-field-value').textContent
                });
            });
            
            // 更新缓存中的地址数据
            window.savedAddressesCache = addresses;
            
            // 使用API保存到服务器
            fetch('/api/save-addresses', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: loggedInUser.username,
                    addresses: addresses
                })
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    console.warn('Failed to save addresses:', data.message);
                }
                
                // 更新地址数量显示
                const addressCountElement = document.getElementById('addressCount');
                if (addressCountElement) {
                    addressCountElement.textContent = addresses.length;
                    // 如果没有地址，隐藏数字标签
                    if (addresses.length === 0) {
                        addressCountElement.style.display = 'none';
                    } else {
                        addressCountElement.style.display = 'inline-block';
                    }
                }
            })
            .catch(error => {
                console.error('Error saving addresses:', error);
            });
        }
        
            // 使用选中的地址 - 将选中的地址添加到主表单
    function useSelectedAddress() {
        const selectedAddress = document.querySelector('.address-card.selected');
        
        if (!selectedAddress) {
            alert('请先选择一个地址');
            return;
        }
        
        // 获取选中地址的信息
        const topRow = selectedAddress.querySelector('.address-top-row');
        const leftCol = topRow.querySelector('.address-left-col');
        const rightCol = topRow.querySelector('.address-right-col');
        const bottomRow = selectedAddress.querySelector('.address-bottom-row');
        
        const name = leftCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent;
        const phone = leftCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent;
        const country = rightCol.querySelector('.address-field:nth-child(1) .address-field-value').textContent;
        const city = rightCol.querySelector('.address-field:nth-child(2) .address-field-value').textContent;
        const zipCode = rightCol.querySelector('.address-field:nth-child(3) .address-field-value').textContent;
        const address = bottomRow.querySelector('.address-field .address-field-value').textContent;
        
        // 填充主表单
        document.getElementById('name').value = name;
        document.getElementById('phone').value = phone;
        document.getElementById('country').value = country;
        document.getElementById('city').value = city !== "未指定城市" ? city : "";
        document.getElementById('zipCode').value = zipCode;
        document.getElementById('address').value = address;
        
        // 更新自定义下拉菜单显示
        const customCountrySelect = document.getElementById('customCountrySelect');
        if (customCountrySelect) {
            const trigger = customCountrySelect.querySelector('.custom-select-trigger');
            if (trigger) {
                trigger.textContent = country;
            }
        }
        
        // 保存表单数据到localStorage
        saveFormData();
        
        // 触发国家选择变更事件，更新运输方式选项
        const countrySelect = document.getElementById('country');
        if (countrySelect) {
            const event = new Event('change');
            countrySelect.dispatchEvent(event);
        }
        
        // 关闭弹窗
        closeAddressPopup();
        
        // 提示成功
        alert('地址已添加到表单');
    }
        
        // 显示/隐藏已保存的地址弹窗
        function toggleSavedAddresses() {
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
            }
            
            // 如果找不到登录用户，则提示用户登录
            if (!loggedInUser || !loggedInUser.username) {
                alert('请先登录再查看保存的地址');
                return;
            }
            
            // 显示弹窗和加载指示器
            document.getElementById('addressPopup').style.display = 'flex';
            // 确保显示地址列表，隐藏表单
            document.getElementById('savedAddressesList').style.display = 'block';
            document.getElementById('newAddressForm').style.display = 'none';
            document.querySelector('.popup-title').textContent = '加载地址中...';
            
            // 从服务器获取地址数据
            fetch(`/api/get-addresses?username=${encodeURIComponent(loggedInUser.username)}`)
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    const savedAddresses = result.data || [];
                    
                    // 更新弹窗标题
                    document.querySelector('.popup-title').textContent = `已保存的地址 (${savedAddresses.length})`;
                    
                    // 更新弹窗中的地址数量显示
                    const addressCountInPopup = document.getElementById('addressCountInPopup');
                    if (addressCountInPopup) {
                        addressCountInPopup.textContent = savedAddresses.length;
                    }
                    
                    // 更新主页面中的地址数量显示
                    const addressCountElement = document.getElementById('addressCount');
                    if (addressCountElement) {
                        addressCountElement.textContent = savedAddresses.length;
                        // 如果没有地址，隐藏数字标签
                        if (savedAddresses.length === 0) {
                            addressCountElement.style.display = 'none';
                        } else {
                            addressCountElement.style.display = 'inline-block';
                        }
                    }
                    
                    // 存储地址到全局缓存
                    window.savedAddressesCache = savedAddresses;
                    
                    // 加载地址列表
                    loadSavedAddresses();
                } else {
                    alert('加载地址失败：' + result.message);
                }
            })
            .catch(error => {
                console.error('Error loading addresses:', error);
                alert('加载地址时发生错误，请稍后重试');
            });
        }
        
        // 关闭地址弹窗
        function closeAddressPopup() {
            document.getElementById('addressPopup').style.display = 'none';
            
            // 更新主页面地址数量显示
            // 使用缓存中的地址数据
            if (window.savedAddressesCache) {
                const savedAddresses = window.savedAddressesCache;
                const addressCountElement = document.getElementById('addressCount');
                if (addressCountElement) {
                    addressCountElement.textContent = savedAddresses.length;
                    // 如果没有地址，隐藏数字标签
                    if (savedAddresses.length === 0) {
                        addressCountElement.style.display = 'none';
                    } else {
                        addressCountElement.style.display = 'inline-block';
                    }
                }
            }
        }
        
        // 下一步操作
        function nextStep() {
            // 检查是否选择了保存的地址或填写了新地址
            const selectedAddress = document.querySelector('.address-card.selected');
            const form = document.getElementById('addressForm');
            const nameInput = document.getElementById('name');
            const phoneInput = document.getElementById('phone');
            const countryInput = document.getElementById('country');
            const cityInput = document.getElementById('city');
            const zipCodeInput = document.getElementById('zipCode');
            const addressInput = document.getElementById('address');
            
            // 检查字段是否只有空白字符
            if (nameInput && nameInput.value.trim() === '') {
                alert('收件人姓名不能为空');
                nameInput.value = '';
                nameInput.focus();
                return;
            }
            
            if (phoneInput && phoneInput.value.trim() === '') {
                alert('联系电话不能为空');
                phoneInput.value = '';
                phoneInput.focus();
                return;
            }
            
            if (!countryInput.value) {
                alert('请选择国家');
                countryInput.focus();
                return;
            }
            
            if (cityInput && cityInput.value.trim() === '') {
                alert('城市不能为空');
                cityInput.value = '';
                cityInput.focus();
                return;
            }
            
            if (zipCodeInput && zipCodeInput.value.trim() === '') {
                alert('邮政编码不能为空');
                zipCodeInput.value = '';
                zipCodeInput.focus();
                return;
            }
            
            if (addressInput && addressInput.value.trim() === '') {
                alert('详细地址不能为空');
                addressInput.value = '';
                addressInput.focus();
                return;
            }
            
            // 验证收件人姓名长度
            if (nameInput && nameInput.value.length > 57) {
                nameInput.value = nameInput.value.substring(0, 57); // 截断为57个字符
                if (!nameInput.dataset.warningShown) {
                    alert('收件人姓名不能超过57个字符');
                }
                return; // 阻止继续
            }
            
            // 验证联系电话长度
            if (phoneInput && phoneInput.value.length > 30) {
                phoneInput.value = phoneInput.value.substring(0, 30); // 截断为30个字符
                if (!phoneInput.dataset.warningShown) {
                    alert('联系电话不能超过30个字符');
                }
                return; // 阻止继续
            }
            
            // 验证城市长度
            if (cityInput && cityInput.value.length > 30) {
                cityInput.value = cityInput.value.substring(0, 30); // 截断为30个字符
                if (!cityInput.dataset.warningShown) {
                    alert('城市不能超过30个字符');
                }
                return; // 阻止继续
            }
            
            // 验证邮政编码长度
            if (zipCodeInput && zipCodeInput.value.length > 20) {
                zipCodeInput.value = zipCodeInput.value.substring(0, 20); // 截断为20个字符
                if (!zipCodeInput.dataset.warningShown) {
                    alert('邮政编码不能超过20个字符');
                }
                return; // 阻止继续
            }
            
            // 验证详细地址长度
            if (addressInput && addressInput.value.length > 189) {
                addressInput.value = addressInput.value.substring(0, 189); // 截断为189个字符
                if (!addressInput.dataset.warningShown) {
                    alert('详细地址不能超过189个字符');
                }
                return; // 阻止继续
            }
            
            if (selectedAddress) {
                // 使用选中的地址进行下一步
                navigateToStep2();
            } else {
                // 检查表单是否填写完整
                if (form.checkValidity()) {
                    navigateToStep2();
                } else {
                    alert('请完整填写收货地址信息！');
                    // 触发浏览器的表单验证
                    const submitButton = document.createElement('button');
                    submitButton.type = 'submit';
                    form.appendChild(submitButton);
                    submitButton.click();
                    form.removeChild(submitButton);
                }
            }
        }

        // 显示图片压缩信息 - 空实现，不显示压缩信息但保留函数调用
        function showCompressionInfo(originalSize, compressedSize, pairIndex) {
            // 计算压缩率，保留日志记录但不显示UI
            const compressionRate = Math.round((1 - compressedSize/originalSize) * 100);
            
            // 仅在控制台记录压缩信息，不在UI上显示
            console.log(`物流码${pairIndex}: ${Math.round(originalSize/1024)}KB → ${Math.round(compressedSize/1024)}KB (压缩率: ${compressionRate}%)`);
        }

        // 删除所有已上传的图片
        function deleteAllUploadedImages() {
            // 获取当前登录用户信息
            let loggedInUser = null;
            try {
                const userDataStr = sessionStorage.getItem('loggedInUser');
                if (userDataStr) {
                    loggedInUser = JSON.parse(userDataStr);
                }
            } catch (error) {
                console.warn('Failed to get user data:', error);
                return;
            }
            
            // 如果找不到登录用户，则不执行删除操作
            if (!loggedInUser || !loggedInUser.username) {
                console.warn('No logged in user found, cannot delete uploaded images');
                return;
            }
            
            // 收集所有已上传图片的文件名
            const pairs = document.querySelectorAll('.code-image-pair');
            const imageFilenames = [];
            
            pairs.forEach(pair => {
                const codeInput = pair.querySelector('.logistics-code');
                if (codeInput && codeInput.dataset.imageFilename) {
                    imageFilenames.push(codeInput.dataset.imageFilename);
                }
            });
            
            // 如果没有图片文件名，则不需要进一步操作
            if (imageFilenames.length === 0) {
                console.log('No uploaded images to delete');
                return;
            }
            
            // 发送删除请求到服务器
            fetch('/api/delete-multiple-images', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: loggedInUser.username,
                    filenames: imageFilenames
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log(`成功删除 ${data.deletedCount} 张图片`);
                } else {
                    console.warn('删除图片失败:', data.message);
                }
            })
            .catch(error => {
                console.error('删除图片错误:', error);
            });
        }
    </script>
    
    <!-- 引入价格限制脚本 -->
    <script src="js/price-limit.js"></script>
    
    <!-- 引入商品名称textarea处理脚本 -->
    <script src="js/product-name-textarea.js"></script>
</body>
</html> 