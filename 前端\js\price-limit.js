/**
 * 商品价格输入限制
 * 此脚本限制商品价格输入框的最大字符数为9个字符
 */
document.addEventListener('DOMContentLoaded', function() {
    // 等待DOM完全加载
    setTimeout(addPriceLimits, 1000);
    
    // 监听DOM变化，以便在添加新的物流码对时添加价格限制
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                addPriceLimits();
            }
        });
    });
    
    // 开始观察DOM变化
    observer.observe(document.body, { childList: true, subtree: true });
});

/**
 * 为所有价格输入框添加字符限制
 */
function addPriceLimits() {
    // 查找所有价格输入框
    const priceInputs = document.querySelectorAll('input.product-price');
    
    priceInputs.forEach(function(input) {
        // 如果已经处理过，则跳过
        if (input.dataset.limitApplied) {
            return;
        }
        
        // 标记为已处理
        input.dataset.limitApplied = 'true';
        
        // 添加输入事件监听器
        input.addEventListener('input', function() {
            // 检查输入长度是否超过9个字符
            if (this.value.length > 9) {
                this.value = this.value.slice(0, 9); // 截断为9个字符
            }
        });
    });
}

// 覆盖原始的验证函数，添加长度验证
document.addEventListener('DOMContentLoaded', function() {
    // 等待原始函数加载完成
    setTimeout(function() {
        // 保存原始的验证函数
        if (typeof validateLogisticsCodes === 'function') {
            const originalValidate = validateLogisticsCodes;
            
            // 覆盖验证函数
            window.validateLogisticsCodes = function() {
                const priceInputs = document.querySelectorAll('.product-price');
                let isValid = true;
                
                // 检查价格长度
                priceInputs.forEach((input, index) => {
                    if (input.value.length > 9) {
                        input.value = input.value.slice(0, 9); // 截断为9个字符
                    }
                });
                
                // 继续原始验证
                return originalValidate();
            };
        }
    }, 1500);
}); 