<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器状态检查 - 金舟国际物流</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0c4da2;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-item {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        .status-online {
            background-color: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .status-offline {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .status-checking {
            background-color: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .btn {
            background-color: #0c4da2;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .btn:hover {
            background-color: #0a3d82;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .instructions {
            background-color: #e9ecef;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .instructions h3 {
            margin-top: 0;
            color: #495057;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            border: 1px solid #dee2e6;
            margin: 10px 0;
        }
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #0c4da2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>服务器状态检查</h1>
        
        <div id="carouselStatus" class="status-item status-checking">
            <strong>轮播图API (/api/carousels):</strong> 
            <span class="spinner"></span> 检查中...
        </div>
        
        <div id="productsStatus" class="status-item status-checking">
            <strong>商品API (/api/products):</strong> 
            <span class="spinner"></span> 检查中...
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <button class="btn" onclick="checkStatus()">重新检查</button>
            <button class="btn btn-secondary" onclick="window.location.href='recommend.html'">返回主页</button>
        </div>
        
        <div class="instructions">
            <h3>如果服务器离线，请按以下步骤操作：</h3>
            <ol>
                <li><strong>检查Node.js是否安装：</strong>
                    <div class="code">node --version</div>
                    如果显示错误，请从 <a href="https://nodejs.org/" target="_blank">https://nodejs.org/</a> 下载并安装Node.js
                </li>
                <li><strong>启动后端服务器：</strong>
                    <div class="code">双击运行项目根目录下的 "启动服务器.bat" 文件</div>
                    或者手动执行：
                    <div class="code">
                        cd 后端<br>
                        npm install<br>
                        npm start
                    </div>
                </li>
                <li><strong>确认服务器运行：</strong>
                    <div class="code">服务器应该显示: "服务器运行在 http://localhost:8080"</div>
                </li>
                <li><strong>刷新此页面</strong>重新检查状态</li>
            </ol>
        </div>
        
        <div class="instructions">
            <h3>常见问题解决：</h3>
            <ul>
                <li><strong>端口被占用：</strong> 关闭其他可能使用8080端口的程序</li>
                <li><strong>防火墙阻止：</strong> 允许Node.js通过防火墙</li>
                <li><strong>权限问题：</strong> 以管理员身份运行命令提示符</li>
                <li><strong>依赖包问题：</strong> 删除node_modules文件夹后重新运行 npm install</li>
            </ul>
        </div>
    </div>

    <script>
        async function checkApiStatus(url, elementId) {
            const element = document.getElementById(elementId);
            const apiName = url.split('/').pop();
            
            try {
                const response = await fetch(url, { 
                    method: 'GET',
                    cache: 'no-cache',
                    timeout: 5000
                });
                
                if (response.ok) {
                    element.className = 'status-item status-online';
                    element.innerHTML = `<strong>${apiName.toUpperCase()} API (${url}):</strong> ✅ 在线`;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                element.className = 'status-item status-offline';
                element.innerHTML = `<strong>${apiName.toUpperCase()} API (${url}):</strong> ❌ 离线 (${error.message})`;
            }
        }

        async function checkStatus() {
            // 重置状态为检查中
            document.getElementById('carouselStatus').className = 'status-item status-checking';
            document.getElementById('carouselStatus').innerHTML = '<strong>轮播图API (/api/carousels):</strong> <span class="spinner"></span> 检查中...';
            
            document.getElementById('productsStatus').className = 'status-item status-checking';
            document.getElementById('productsStatus').innerHTML = '<strong>商品API (/api/products):</strong> <span class="spinner"></span> 检查中...';
            
            // 检查API状态
            await Promise.all([
                checkApiStatus('/api/carousels', 'carouselStatus'),
                checkApiStatus('/api/products', 'productsStatus')
            ]);
        }

        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', checkStatus);
    </script>
</body>
</html>
