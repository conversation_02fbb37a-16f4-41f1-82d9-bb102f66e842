<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱注册 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/blacklist-handler.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .page-title {
            color: #0c4da2;
            font-size: 32px;
            margin-bottom: 0;
        }
        
        .gold {
            color: #D4AF37;
        }
        
        .back-button {
            display: inline-block;
            color: #0c4da2;
            text-decoration: none;
            font-size: 16px;
            background-color: #fff;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background-color: #f5f5f5;
        }
        
        .register-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .option-title {
            color: #0c4da2;
            font-size: 24px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #0c4da2;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            border-color: #0c4da2;
            outline: none;
        }
        
        /* 验证码输入框和按钮的样式 */
        .verification-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .verification-group input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .verification-group input:focus {
            border-color: #0c4da2;
            outline: none;
        }
        
        .send-code-btn {
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 0 15px;
            border-radius: 5px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 120px;
        }
        
        .send-code-btn:hover {
            background-color: #083778;
        }
        
        .send-code-btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        
        .submit-button {
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .submit-button:hover {
            background-color: #083778;
        }
        
        .login-link {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
        }
        
        .login-link a {
            color: #0c4da2;
            text-decoration: none;
            font-weight: bold;
        }
        
        .login-link a:hover {
            text-decoration: underline;
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            color: #666;
            font-size: 14px;
        }
        
        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background-color: #fff;
            margin: 15% auto;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
            position: relative;
            max-width: 420px;
            transform: scale(0.8);
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .modal.show .modal-content {
            transform: scale(1);
            opacity: 1;
        }
        
        @keyframes modal-show {
            from {opacity: 0; transform: translateY(-20px) scale(0.8);}
            to {opacity: 1; transform: translateY(0) scale(1);}
        }
        
        .modal-title {
            color: #0c4da2;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
            font-size: 20px;
        }
        
        .modal-text {
            margin-bottom: 25px;
            text-align: center;
            color: #555;
            font-size: 16px;
        }
        
        .modal-btn {
            display: block;
            width: 100%;
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .modal-btn:hover {
            background-color: #083778;
        }
        
        .modal-icon {
            display: block;
            text-align: center;
            font-size: 48px;
            margin-bottom: 20px;
            color: #0c4da2;
        }
        
        /* 状态提示样式 */
        .status-message {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            display: none;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        /* 新增优雅的通知系统 */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            max-width: 350px;
        }
        
        .toast {
            display: flex;
            align-items: center;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            margin-bottom: 15px;
            padding: 12px 20px;
            overflow: hidden;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        .toast.toast-success {
            border-left: 5px solid #28a745;
        }
        
        .toast.toast-error {
            border-left: 5px solid #dc3545;
        }
        
        .toast.toast-info {
            border-left: 5px solid #17a2b8;
        }
        
        .toast.toast-warning {
            border-left: 5px solid #ffc107;
        }
        
        .toast-icon {
            margin-right: 15px;
            font-size: 20px;
        }
        
        .toast-success .toast-icon {
            color: #28a745;
        }
        
        .toast-error .toast-icon {
            color: #dc3545;
        }
        
        .toast-info .toast-icon {
            color: #17a2b8;
        }
        
        .toast-warning .toast-icon {
            color: #ffc107;
        }
        
        .toast-content {
            flex: 1;
        }
        
        .toast-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .toast-message {
            font-size: 14px;
            color: #666;
        }
        
        .toast-close {
            color: #999;
            font-size: 18px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .toast-close:hover {
            color: #333;
        }
        
        .toast-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background-color: rgba(0, 0, 0, 0.1);
            width: 100%;
        }
        
        .toast-progress-bar {
            height: 3px;
            width: 100%;
        }
        
        .toast-success .toast-progress-bar {
            background-color: #28a745;
        }
        
        .toast-error .toast-progress-bar {
            background-color: #dc3545;
        }
        
        .toast-info .toast-progress-bar {
            background-color: #17a2b8;
        }
        
        .toast-warning .toast-progress-bar {
            background-color: #ffc107;
        }
        
        /* 图片验证码弹窗样式 */
        .captcha-modal {
            display: none;
            position: fixed;
            z-index: 1100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .captcha-content {
            background-color: #fff;
            margin: 15% auto;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
            position: relative;
            max-width: 350px;
            transform: scale(0.8);
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .captcha-modal.show .captcha-content {
            transform: scale(1);
            opacity: 1;
        }
        
        .captcha-title {
            color: #0c4da2;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
            font-size: 18px;
        }
        
        .captcha-container {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .captcha-image {
            height: 80px;
            background-color: #f5f5f5;
            border-radius: 5px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
            user-select: none;
        }
        
        .captcha-image canvas {
            cursor: pointer;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .captcha-refresh {
            position: absolute;
            right: 10px;
            bottom: 10px;
            color: #0c4da2;
            background-color: rgba(255, 255, 255, 0.8);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .captcha-input-container {
            display: flex;
            margin-bottom: 20px;
        }
        
        .captcha-input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .captcha-verify-btn {
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 0 20px;
            border-radius: 5px;
            margin-left: 10px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .captcha-verify-btn:hover {
            background-color: #083778;
        }
        
        .captcha-footer {
            text-align: center;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-container">
                <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="page-title">邮箱注册</h1>
            </div>
            <a href="register.html" class="back-button">返回注册选择</a>
        </div>
        
        <div class="register-container">
            <div class="option-title">邮箱注册</div>
            
            <!-- 状态提示 -->
            <div id="status-message" class="status-message"></div>
            
            <form id="emailRegisterForm">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" placeholder="请设置用户名" maxlength="10" required>
                </div>
                
                <div class="form-group">
                    <label for="email">电子邮箱</label>
                    <input type="email" id="email" name="email" placeholder="请输入您的邮箱" maxlength="320" required>
                </div>
                
                <div class="form-group">
                    <label for="verificationCode">邮箱验证码</label>
                    <div class="verification-group">
                        <input type="text" id="verificationCode" name="verificationCode" placeholder="请输入验证码" maxlength="6" required>
                        <button type="button" id="sendCodeBtn" class="send-code-btn">发送验证码</button>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" placeholder="请设置密码" maxlength="64" required>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">确认密码</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" placeholder="请再次输入密码" maxlength="64" required>
                </div>
                
                <button type="submit" class="submit-button">注册账号</button>
            </form>
            
            <div class="login-link">
                已有账号？<a href="login.html">立即登录</a>
            </div>
        </div>
        
        <footer>
            <p>&copy; 2023 金舟国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </footer>
    </div>
    
    <!-- 模态弹窗 -->
    <div id="message-modal" class="modal">
        <div class="modal-content">
            <div class="modal-icon" id="modal-icon"><i class="fas fa-check-circle"></i></div>
            <h3 class="modal-title" id="modal-title"></h3>
            <p class="modal-text" id="modal-text"></p>
            <button class="modal-btn" id="modal-btn">确定</button>
        </div>
    </div>
    
    <!-- 优雅提示的容器 -->
    <div class="toast-container" id="toast-container"></div>
    
    <!-- 图片验证码弹窗 -->
    <div id="captcha-modal" class="captcha-modal">
        <div class="captcha-content">
            <div class="captcha-title">请完成安全验证</div>
            <div class="captcha-container">
                <div class="captcha-image">
                    <canvas id="captcha-canvas" width="300" height="80"></canvas>
                    <div class="captcha-refresh" id="refresh-captcha"><i class="fas fa-sync-alt"></i></div>
                </div>
                <div class="captcha-input-container">
                    <input type="text" id="captcha-input" class="captcha-input" placeholder="请输入验证码" maxlength="6">
                    <button class="captcha-verify-btn" id="verify-captcha">验证</button>
                </div>
            </div>
            <div class="captcha-footer">
                点击图片可刷新验证码
            </div>
        </div>
    </div>
    
    <script>
        // API地址 - 使用后端服务的地址
        const API_BASE_URL = 'http://localhost:8080';
        
        // 显示模态弹窗的函数
        function showModal(title, text, callback, type = 'success') {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-text').textContent = text;
            
            // 设置图标
            const modalIcon = document.getElementById('modal-icon');
            if (type === 'success') {
                modalIcon.innerHTML = '<i class="fas fa-check-circle" style="color: #28a745;"></i>';
            } else if (type === 'error') {
                modalIcon.innerHTML = '<i class="fas fa-times-circle" style="color: #dc3545;"></i>';
            } else if (type === 'info') {
                modalIcon.innerHTML = '<i class="fas fa-info-circle" style="color: #17a2b8;"></i>';
            } else if (type === 'warning') {
                modalIcon.innerHTML = '<i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>';
            }
            
            const modal = document.getElementById('message-modal');
            modal.style.display = 'block';
            
            // 添加显示类以触发动画
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
            
            document.getElementById('modal-btn').onclick = function() {
                modal.classList.remove('show');
                
                // 等待动画完成后隐藏
                setTimeout(() => {
                    modal.style.display = 'none';
                    if (callback) callback();
                }, 300);
            };
        }

        // 显示优雅的toast通知
        function showToast(message, type = 'info', title = '', duration = 5000) {
            const container = document.getElementById('toast-container');
            
            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            
            // 设置图标
            let iconClass = 'info-circle';
            if (type === 'success') iconClass = 'check-circle';
            if (type === 'error') iconClass = 'times-circle';
            if (type === 'warning') iconClass = 'exclamation-triangle';
            
            // 构建toast内容
            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="fas fa-${iconClass}"></i>
                </div>
                <div class="toast-content">
                    ${title ? `<div class="toast-title">${title}</div>` : ''}
                    <div class="toast-message">${message}</div>
                </div>
                <div class="toast-close">
                    <i class="fas fa-times"></i>
                </div>
                <div class="toast-progress">
                    <div class="toast-progress-bar"></div>
                </div>
            `;
            
            // 添加到容器
            container.appendChild(toast);
            
            // 触发动画
            setTimeout(() => {
                toast.classList.add('show');
                
                // 设置进度条动画
                const progressBar = toast.querySelector('.toast-progress-bar');
                progressBar.style.transition = `width ${duration}ms linear`;
                progressBar.style.width = '0';
            }, 10);
            
            // 设置关闭事件
            const closeBtn = toast.querySelector('.toast-close');
            closeBtn.addEventListener('click', () => {
                removeToast(toast);
            });
            
            // 自动关闭
            const closeTimeout = setTimeout(() => {
                removeToast(toast);
            }, duration);
            
            // 移除Toast的函数
            function removeToast(toast) {
                clearTimeout(closeTimeout);
                toast.classList.remove('show');
                
                // 动画结束后移除DOM
                setTimeout(() => {
                    if (container.contains(toast)) {
                        container.removeChild(toast);
                    }
                }, 300);
            }
        }
        
        // 发送API请求
        async function callAPI(endpoint, data, method = 'POST') {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };
                
                if (method === 'POST') {
                    options.body = JSON.stringify(data);
                } else if (method === 'GET' && data) {
                    endpoint = `${endpoint}?${new URLSearchParams(data)}`;
                }
                
                const url = `${API_BASE_URL}/api/${endpoint}`;
                const response = await fetch(url, options);
                const result = await response.json();
                return result;
            } catch (error) {
                console.error('API请求失败:', error);
                return { success: false, message: '服务器连接失败，请检查后端服务是否运行' };
            }
        }
        
        // 图片验证码功能
        class ImageCaptcha {
            constructor() {
                this.canvas = document.getElementById('captcha-canvas');
                this.ctx = this.canvas.getContext('2d');
                this.captchaText = '';
                this.captchaLength = 4; // 验证码长度，保持为4，符合最大6个字符的限制
                this.verified = false;
                
                // 绑定事件处理器
                this.bindEvents();
                
                // 初始化验证码
                this.refresh();
            }
            
            // 绑定事件
            bindEvents() {
                // 点击刷新图片验证码
                document.getElementById('refresh-captcha').addEventListener('click', () => this.refresh());
                this.canvas.addEventListener('click', () => this.refresh());
                
                // 验证按钮点击
                document.getElementById('verify-captcha').addEventListener('click', () => {
                    this.verify();
                });
                
                // 输入框按下回车键也可验证
                document.getElementById('captcha-input').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.verify();
                    }
                });
            }
            
            // 生成随机验证码
            generateCaptchaText() {
                const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
                let result = '';
                for (let i = 0; i < this.captchaLength; i++) {
                    result += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                return result;
            }
            
            // 刷新验证码
            refresh() {
                // 生成新验证码文本
                this.captchaText = this.generateCaptchaText();
                
                // 清空画布
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                
                // 设置背景
                this.ctx.fillStyle = '#f0f2f5';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                // 添加干扰线
                this.drawInterferenceLines(5);
                
                // 添加干扰点
                this.drawInterferenceDots(50);
                
                // 绘制验证码文本
                this.drawCaptchaText();
                
                // 重置输入框和验证状态
                document.getElementById('captcha-input').value = '';
                this.verified = false;
            }
            
            // 绘制干扰线
            drawInterferenceLines(count) {
                for (let i = 0; i < count; i++) {
                    this.ctx.strokeStyle = this.getRandomColor(40, 180);
                    this.ctx.beginPath();
                    this.ctx.moveTo(Math.random() * this.canvas.width, Math.random() * this.canvas.height);
                    this.ctx.lineTo(Math.random() * this.canvas.width, Math.random() * this.canvas.height);
                    this.ctx.lineWidth = Math.random() * 2 + 1;
                    this.ctx.stroke();
                }
            }
            
            // 绘制干扰点
            drawInterferenceDots(count) {
                for (let i = 0; i < count; i++) {
                    this.ctx.fillStyle = this.getRandomColor(0, 255);
                    this.ctx.beginPath();
                    this.ctx.arc(Math.random() * this.canvas.width, Math.random() * this.canvas.height, 
                                 Math.random() * 2 + 1, 0, Math.PI * 2);
                    this.ctx.fill();
                }
            }
            
            // 绘制验证码文本
            drawCaptchaText() {
                const textWidth = this.canvas.width / (this.captchaLength + 1);
                
                for (let i = 0; i < this.captchaText.length; i++) {
                    const char = this.captchaText[i];
                    
                    // 随机颜色
                    this.ctx.fillStyle = this.getRandomColor(10, 100);
                    
                    // 随机字体大小
                    const fontSize = Math.random() * 10 + 28;
                    
                    // 随机旋转角度
                    const rotation = (Math.random() - 0.5) * 0.4;
                    
                    // 保存当前状态
                    this.ctx.save();
                    
                    // 位置计算，让字符均匀分布
                    const x = textWidth * (i + 1);
                    const y = this.canvas.height / 2 + 10;
                    
                    // 设置旋转中心点
                    this.ctx.translate(x, y);
                    this.ctx.rotate(rotation);
                    
                    // 设置字体
                    this.ctx.font = `bold ${fontSize}px Arial`;
                    this.ctx.textAlign = 'center';
                    this.ctx.textBaseline = 'middle';
                    
                    // 绘制文本
                    this.ctx.fillText(char, 0, 0);
                    
                    // 恢复状态
                    this.ctx.restore();
                }
            }
            
            // 获取随机颜色
            getRandomColor(min, max) {
                const r = Math.floor(Math.random() * (max - min) + min);
                const g = Math.floor(Math.random() * (max - min) + min);
                const b = Math.floor(Math.random() * (max - min) + min);
                return `rgb(${r}, ${g}, ${b})`;
            }
            
            // 验证用户输入
            verify() {
                const userInput = document.getElementById('captcha-input').value.trim();
                
                if (!userInput) {
                    showToast('请输入验证码', 'warning', '验证失败');
                    return;
                }
                
                // 验证码不区分大小写
                if (userInput.toLowerCase() === this.captchaText.toLowerCase()) {
                    this.verified = true;
                    showToast('验证成功', 'success', '安全验证');
                    
                    // 关闭验证码模态窗口
                    hideCaptchaModal();
                    
                    // 继续发送邮箱验证码的流程
                    continueSendEmailCode();
                } else {
                    showToast('验证码错误，请重新输入', 'error', '验证失败');
                    this.refresh();
                }
            }
            
            // 获取验证状态
            isVerified() {
                return this.verified;
            }
        }
        
        // 显示验证码模态窗口
        function showCaptchaModal(callback) {
            const modal = document.getElementById('captcha-modal');
            modal.style.display = 'block';
            
            // 保存回调函数
            window.captchaCallback = callback;
            
            // 添加显示类以触发动画
            setTimeout(() => {
                modal.classList.add('show');
                
                // 初始化图片验证码
                if (!window.imageCaptcha) {
                    window.imageCaptcha = new ImageCaptcha();
                } else {
                    window.imageCaptcha.refresh();
                }
                
                // 聚焦到输入框
                document.getElementById('captcha-input').focus();
            }, 10);
        }
        
        // 隐藏验证码模态窗口
        function hideCaptchaModal() {
            const modal = document.getElementById('captcha-modal');
            modal.classList.remove('show');
            
            // 等待动画完成后隐藏
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }
        
        // 验证码成功后继续发送邮箱验证码
        function continueSendEmailCode() {
            // 获取邮箱输入
            const email = document.getElementById('email').value.trim();
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            
            // 如果已有计时器在运行，先清除
            if (window.currentTimer) {
                clearInterval(window.currentTimer);
            }
            
            // 禁用按钮并设置加载状态
            sendCodeBtn.disabled = true;
            sendCodeBtn.textContent = '发送中...';
            
            // 先检查邮箱是否被拉黑
            callAPI('check-email-blacklist', { email: email }).then(blacklistResult => {
                if (blacklistResult.blacklisted) {
                    // 邮箱被拉黑，显示拉黑提示
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.textContent = '发送验证码';
                    BlacklistHandler.showBlacklistedMessage(blacklistResult.message || BlacklistHandler.DEFAULT_BLACKLIST_MESSAGE);
                } else {
                    // 邮箱未被拉黑，继续发送验证码
                    // 调用后端API发送验证码
                    callAPI('send-verification-code', { email: email }).then(result => {
                        if (result.success) {
                            // 发送成功，开始倒计时
                            let countdown = 60;
                            sendCodeBtn.textContent = `${countdown}秒后重新发送`;
                            
                            window.currentTimer = setInterval(function() {
                                countdown--;
                                if (sendCodeBtn) { // 确保元素仍然存在
                                    sendCodeBtn.textContent = `${countdown}秒后重新发送`;
                                    
                                    if (countdown <= 0) {
                                        clearInterval(window.currentTimer);
                                        window.currentTimer = null;
                                        sendCodeBtn.disabled = false;
                                        sendCodeBtn.textContent = '发送验证码';
                                    }
                                } else {
                                    clearInterval(window.currentTimer);
                                    window.currentTimer = null;
                                }
                            }, 1000);
                            
                            // 显示成功信息
                            showToast(`验证码已发送至 ${email}，请查收邮件`, 'success', '发送成功');
                            
                            // 将验证码和邮箱保存在sessionStorage中
                            sessionStorage.setItem('pendingEmail', email);
                        } else {
                            // 发送失败
                            sendCodeBtn.disabled = false;
                            sendCodeBtn.textContent = '发送验证码';
                            showToast(result.message || '验证码发送失败，请稍后重试', 'error', '发送失败');
                        }
                    });
                }
            }).catch(error => {
                console.error('检查邮箱黑名单出错:', error);
                sendCodeBtn.disabled = false;
                sendCodeBtn.textContent = '发送验证码';
                showToast('检查邮箱状态出错，请稍后重试', 'error', '系统错误');
            });
        }
        
        // 验证码发送功能
        document.addEventListener('DOMContentLoaded', function() {
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            const emailInput = document.getElementById('email');
            
            // 监听所有设置了maxlength属性的输入框
            document.querySelectorAll('input[maxlength]').forEach(input => {
                // 记录上一次显示提示的时间
                let lastToastTime = 0;

                // 使用keydown事件，可以捕获到用户尝试输入但被maxlength阻止的情况
                input.addEventListener('keydown', function(e) {
                    const maxLength = parseInt(this.getAttribute('maxlength'));
                    const currentTime = new Date().getTime();
                    
                    // 如果已经达到最大长度且不是功能键（如退格键、方向键等）
                    if (this.value.length >= maxLength && 
                        !e.ctrlKey && !e.altKey && 
                        e.key.length === 1 && 
                        currentTime - lastToastTime > 1000) { // 限制提示频率，至少间隔1秒
                        
                        showToast(`输入已达到最大限制${maxLength}个字符`, 'warning', '字符限制');
                        lastToastTime = currentTime;
                    }
                });
                
                // 保留input事件监听，用于处理粘贴等操作
                input.addEventListener('input', function(e) {
                    const maxLength = parseInt(this.getAttribute('maxlength'));
                    const currentTime = new Date().getTime();
                    
                    if (this.value.length >= maxLength && currentTime - lastToastTime > 1000) {
                        showToast(`输入已达到最大限制${maxLength}个字符`, 'warning', '字符限制');
                        lastToastTime = currentTime;
                    }
                });
            });
            
            if (sendCodeBtn && emailInput) {
                sendCodeBtn.addEventListener('click', async function() {
                    const email = emailInput.value.trim();

                    // 验证邮箱格式
                    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailPattern.test(email)) {
                        showToast('请输入有效的邮箱地址', 'error', '格式错误');
                        emailInput.focus();
                        return;
                    }

                    // 显示图片验证码
                    showCaptchaModal();
                });
            }
            
            // 表单提交验证
            const registerForm = document.getElementById('emailRegisterForm');
            if (registerForm) {
                registerForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    
                    const username = document.getElementById('username').value.trim();
                    const email = document.getElementById('email').value.trim();
                    const inputCode = document.getElementById('verificationCode').value.trim();
                    const password = document.getElementById('password').value;
                    const confirmPassword = document.getElementById('confirmPassword').value;
                    
                    // 验证用户名
                    if (username.length < 3) {
                        showToast('用户名至少需要3个字符', 'warning', '验证失败');
                        return;
                    }
                    
                    if (username.length > 10) {
                        showToast('用户名最多10个字符', 'warning', '验证失败');
                        return;
                    }
                    
                    // 验证邮箱
                    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailPattern.test(email)) {
                        showToast('请输入有效的邮箱地址', 'warning', '验证失败');
                        return;
                    }
                    
                    // 验证验证码
                    if (inputCode.length > 6) {
                        showToast('验证码最多6个字符', 'warning', '验证失败');
                        return;
                    }
                    
                    // 验证密码
                    const passwordPattern = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,64}$/;
                    if (!passwordPattern.test(password)) {
                        showToast('密码必须为8-64位，且同时包含英文字母和数字', 'warning', '验证失败');
                        return;
                    }
                    
                    // 验证两次密码是否一致
                    if (password !== confirmPassword) {
                        showToast('两次输入的密码不一致', 'warning', '验证失败');
                        return;
                    }
                    
                    // 直接调用后端API进行注册
                    const registerResult = await callAPI('register', { 
                        username: username,
                        email: email,
                        verificationCode: inputCode,
                        password: password
                    });
                    
                    if (registerResult.success) {
                        // 注册成功
                        showModal('注册成功', '恭喜您注册成功！即将跳转到登录页面...', function() {
                            window.location.href = 'login.html';
                        }, 'success');
                    } else {
                        // 注册失败
                        showToast(registerResult.message || '注册失败，请稍后重试', 'error', '注册失败');
                    }
                });
            }
        });
    </script>
</body>
</html> 