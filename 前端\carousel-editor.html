<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图编辑器 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .page-title {
            color: #0c4da2;
            font-size: 32px;
            margin-bottom: 0;
        }
        
        .back-button {
            display: inline-block;
            color: #0c4da2;
            text-decoration: none;
            font-size: 16px;
            background-color: #fff;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background-color: #f5f5f5;
        }
        
        .form-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .form-title {
            color: #0c4da2;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            border-color: #0c4da2;
            outline: none;
        }
        
        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }
        
        .image-upload-container {
            border: 2px dashed #ddd;
            padding: 40px;
            text-align: center;
            border-radius: 5px;
            cursor: pointer;
            transition: border-color 0.3s;
            background-color: #fafafa;
        }
        
        .image-upload-container:hover {
            border-color: #0c4da2;
        }
        
        .image-upload-icon {
            font-size: 48px;
            color: #ddd;
            margin-bottom: 15px;
        }
        
        .image-upload-text {
            color: #777;
            font-size: 16px;
        }
        
        .image-preview {
            margin-top: 20px;
            position: relative;
            display: none;
        }
        
        .preview-image {
            width: 100%;
            max-height: 300px;
            object-fit: cover;
            border-radius: 5px;
        }
        
        .remove-image {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(255, 0, 0, 0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .remove-image:hover {
            background-color: rgba(255, 0, 0, 0.9);
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background-color: #0c4da2;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0a3d82;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        
        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
        }
        
        .products-section {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #eee;
        }
        
        .product-search {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .product-search input {
            flex: 1;
        }
        
        .selected-products {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .product-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            position: relative;
        }

        .product-image {
            width: 100%;
            height: 120px;
            margin-bottom: 10px;
            border-radius: 4px;
            overflow: hidden;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .no-image {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            font-size: 12px;
        }

        .no-image i {
            font-size: 24px;
            margin-bottom: 5px;
            opacity: 0.5;
        }

        .product-item h4 {
            margin-bottom: 5px;
            color: #0c4da2;
            font-size: 14px;
        }

        .product-item p {
            color: #666;
            font-size: 12px;
            margin-bottom: 10px;
        }

        .product-price {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .remove-product {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .no-products {
            text-align: center;
            color: #666;
            padding: 40px;
            border: 1px dashed #ddd;
            border-radius: 5px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }
            
            .header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .form-actions {
                flex-direction: column;
            }
            
            .product-search {
                flex-direction: column;
            }
            
            .selected-products {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo-container">
                <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="page-title" id="pageTitle">添加轮播图</h1>
            </div>
            <a href="carousel-management.html" class="back-button">
                <i class="fas fa-arrow-left"></i> 返回管理页面
            </a>
        </header>
        
        <div class="form-card">
            <h2 class="form-title" id="formTitle">轮播图信息</h2>
            <form id="carouselForm">
                <div class="form-group">
                    <label for="carouselTitle">轮播图标题</label>
                    <input type="text" id="carouselTitle" name="title" class="form-control" placeholder="请输入轮播图标题" required>
                </div>
                
                <div class="form-group">
                    <label>轮播图图片</label>
                    <div class="image-upload-container" id="imageUploadContainer">
                        <div class="image-upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="image-upload-text">点击上传图片或拖拽图片到此处</div>
                        <input type="file" id="imageInput" accept="image/*" style="display: none;">
                    </div>
                    <div class="image-preview" id="imagePreview">
                        <img id="previewImage" class="preview-image" alt="预览图片">
                        <button type="button" class="remove-image" id="removeImage">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <div class="products-section">
                    <h3>关联商品</h3>
                    <p style="color: #666; margin-bottom: 15px;">选择要在此轮播图中展示的商品</p>
                    
                    <div class="product-search">
                        <input type="text" id="productSearch" class="form-control" placeholder="搜索商品名称...">
                        <button type="button" class="btn btn-primary" id="searchProductBtn">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                    
                    <div id="searchResults" style="display: none;">
                        <!-- 搜索结果将在这里显示 -->
                    </div>
                    
                    <div class="selected-products" id="selectedProducts">
                        <div class="no-products">
                            <i class="fas fa-box-open" style="font-size: 24px; margin-bottom: 10px;"></i>
                            <p>暂未选择商品</p>
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" id="cancelBtn">取消</button>
                    <button type="submit" class="btn btn-primary" id="saveBtn">保存轮播图</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 轮播图编辑器类
        class CarouselEditor {
            constructor() {
                this.carouselId = null;
                this.selectedProducts = [];
                this.uploadedImage = null;
                this.init();
            }

            init() {
                this.checkAdminAuth();
                this.bindEvents();
                this.loadCarouselData();
            }

            checkAdminAuth() {
                const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');
                
                if (!adminData.isAdmin) {
                    window.location.href = 'admin-login.html';
                    return null;
                }
                
                return adminData;
            }

            bindEvents() {
                // 图片上传
                const imageUploadContainer = document.getElementById('imageUploadContainer');
                const imageInput = document.getElementById('imageInput');
                
                imageUploadContainer.addEventListener('click', () => {
                    imageInput.click();
                });
                
                imageInput.addEventListener('change', (e) => {
                    this.handleImageUpload(e.target.files[0]);
                });
                
                // 拖拽上传
                imageUploadContainer.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    imageUploadContainer.style.borderColor = '#0c4da2';
                });
                
                imageUploadContainer.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    imageUploadContainer.style.borderColor = '#ddd';
                });
                
                imageUploadContainer.addEventListener('drop', (e) => {
                    e.preventDefault();
                    imageUploadContainer.style.borderColor = '#ddd';
                    const file = e.dataTransfer.files[0];
                    if (file && file.type.startsWith('image/')) {
                        this.handleImageUpload(file);
                    }
                });
                
                // 移除图片
                document.getElementById('removeImage').addEventListener('click', () => {
                    this.removeImage();
                });
                
                // 商品搜索
                document.getElementById('searchProductBtn').addEventListener('click', () => {
                    this.searchProducts();
                });

                document.getElementById('productSearch').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.searchProducts();
                    }
                });
                
                // 表单提交
                document.getElementById('carouselForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.saveCarousel();
                });
                
                // 取消按钮
                document.getElementById('cancelBtn').addEventListener('click', () => {
                    if (confirm('确定要取消吗？未保存的更改将丢失。')) {
                        window.location.href = 'carousel-management.html';
                    }
                });
            }

            loadCarouselData() {
                // 从URL获取轮播图ID
                const urlParams = new URLSearchParams(window.location.search);
                this.carouselId = urlParams.get('id');
                
                if (this.carouselId) {
                    // 编辑模式
                    document.getElementById('pageTitle').textContent = '编辑轮播图';
                    document.getElementById('formTitle').textContent = '编辑轮播图信息';
                    document.getElementById('saveBtn').textContent = '更新轮播图';
                    
                    // 加载轮播图数据
                    this.loadCarouselFromServer();
                } else {
                    // 添加模式
                    document.getElementById('pageTitle').textContent = '添加轮播图';
                    document.getElementById('formTitle').textContent = '新建轮播图';
                    document.getElementById('saveBtn').textContent = '保存轮播图';
                }
            }

            loadCarouselFromServer() {
                fetch(`/api/carousels/${this.carouselId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.populateForm(data.carousel);
                    } else {
                        alert('加载轮播图数据失败: ' + data.message);
                        window.location.href = 'carousel-management.html';
                    }
                })
                .catch(error => {
                    console.error('加载轮播图数据失败:', error);
                    alert('加载轮播图数据失败，请稍后重试');
                });
            }

            populateForm(carousel) {
                document.getElementById('carouselTitle').value = carousel.title || '';

                if (carousel.image) {
                    this.showImagePreview(carousel.image);
                }
                
                if (carousel.products && carousel.products.length > 0) {
                    this.selectedProducts = carousel.products;
                    this.renderSelectedProducts();
                }
            }

            handleImageUpload(file) {
                if (!file) return;

                // 验证文件类型
                if (!file.type.startsWith('image/')) {
                    alert('请选择图片文件');
                    return;
                }

                // 验证文件大小（10MB）
                if (file.size > 10 * 1024 * 1024) {
                    alert('图片文件不能超过10MB');
                    return;
                }

                // 显示压缩进度提示
                this.showCompressionProgress();

                // 使用图片压缩函数处理图片
                this.compressImage(file, (compressedFile) => {
                    // 隐藏压缩进度提示
                    this.hideCompressionProgress();

                    // 显示预览
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        this.showImagePreview(e.target.result);
                    };
                    reader.readAsDataURL(compressedFile);

                    // 保存压缩后的文件引用
                    this.uploadedImage = compressedFile;
                });
            }

            // 图片压缩函数
            compressImage(file, callback) {
                // 创建图像对象
                const img = new Image();
                const reader = new FileReader();

                reader.onload = function(e) {
                    img.src = e.target.result;

                    img.onload = function() {
                        // 创建Canvas
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // 计算压缩后的尺寸
                        let width = img.width;
                        let height = img.height;
                        const maxDimension = 1200; // 最大尺寸

                        // 如果图片尺寸超出了最大限制
                        if (width > maxDimension || height > maxDimension) {
                            if (width > height) {
                                height = Math.round(height * maxDimension / width);
                                width = maxDimension;
                            } else {
                                width = Math.round(width * maxDimension / height);
                                height = maxDimension;
                            }
                        }

                        // 设置Canvas尺寸
                        canvas.width = width;
                        canvas.height = height;

                        // 绘制到Canvas
                        ctx.drawImage(img, 0, 0, width, height);

                        // 根据文件大小确定压缩质量
                        let quality = 0.7; // 默认质量70%

                        if (file.size > 5 * 1024 * 1024) { // 大于5MB
                            quality = 0.5;
                        } else if (file.size > 2 * 1024 * 1024) { // 大于2MB
                            quality = 0.6;
                        } else if (file.size > 1 * 1024 * 1024) { // 大于1MB
                            quality = 0.7;
                        } else {
                            // 对于1MB以下的图片也进行适度压缩
                            quality = 0.8;
                        }

                        // 将Canvas导出为Blob
                        canvas.toBlob(
                            function(blob) {
                                // 创建新的文件对象
                                const compressedFile = new File(
                                    [blob],
                                    file.name,
                                    {
                                        type: 'image/jpeg',
                                        lastModified: Date.now()
                                    }
                                );

                                console.log(`轮播图图片压缩: ${Math.round(file.size/1024)}KB → ${Math.round(blob.size/1024)}KB (${Math.round((1 - blob.size/file.size) * 100)}% 压缩率)`);

                                // 返回压缩后的文件
                                callback(compressedFile);
                            },
                            'image/jpeg',
                            quality
                        );
                    };
                };

                reader.readAsDataURL(file);
            }

            showImagePreview(imageSrc) {
                const preview = document.getElementById('imagePreview');
                const previewImage = document.getElementById('previewImage');
                const uploadContainer = document.getElementById('imageUploadContainer');

                previewImage.src = imageSrc;
                preview.style.display = 'block';
                uploadContainer.style.display = 'none';
            }

            // 显示压缩进度提示
            showCompressionProgress() {
                const uploadContainer = document.getElementById('imageUploadContainer');
                const originalContent = uploadContainer.innerHTML;

                // 保存原始内容以便恢复
                uploadContainer.dataset.originalContent = originalContent;

                uploadContainer.innerHTML = `
                    <div class="compression-progress">
                        <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: #0c4da2; margin-bottom: 10px;"></i>
                        <div style="color: #0c4da2; font-weight: bold;">正在压缩图片...</div>
                        <div style="color: #666; font-size: 12px; margin-top: 5px;">请稍候</div>
                    </div>
                `;
                uploadContainer.style.justifyContent = 'center';
                uploadContainer.style.alignItems = 'center';
            }

            // 隐藏压缩进度提示
            hideCompressionProgress() {
                const uploadContainer = document.getElementById('imageUploadContainer');
                const originalContent = uploadContainer.dataset.originalContent;

                if (originalContent) {
                    uploadContainer.innerHTML = originalContent;
                    uploadContainer.style.justifyContent = '';
                    uploadContainer.style.alignItems = '';
                    delete uploadContainer.dataset.originalContent;
                }
            }

            removeImage() {
                const preview = document.getElementById('imagePreview');
                const uploadContainer = document.getElementById('imageUploadContainer');
                const imageInput = document.getElementById('imageInput');

                preview.style.display = 'none';
                uploadContainer.style.display = 'block';
                imageInput.value = '';
                this.uploadedImage = null;
            }

            searchProducts() {
                const searchTerm = document.getElementById('productSearch').value.trim();
                if (!searchTerm) {
                    alert('请输入搜索关键词');
                    return;
                }

                // 获取所有商品然后进行搜索过滤
                fetch('/api/products')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const allProducts = data.products || [];
                        // 在前端进行搜索过滤
                        const filteredProducts = allProducts.filter(product =>
                            product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            product.id.toLowerCase().includes(searchTerm.toLowerCase())
                        );
                        this.showSearchResults(filteredProducts);
                    } else {
                        alert('获取商品失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('获取商品失败:', error);
                    alert('网络错误，无法连接到服务器');
                });
            }

            showSearchResults(products) {
                const resultsContainer = document.getElementById('searchResults');

                if (products.length === 0) {
                    resultsContainer.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">未找到相关商品</p>';
                    resultsContainer.style.display = 'block';
                    return;
                }

                resultsContainer.innerHTML = `
                    <h4 style="margin-bottom: 15px;">搜索结果 (${products.length}个商品)</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px;">
                        ${products.map(product => {
                            const price = typeof product.price === 'number' ? product.price : parseFloat(product.price) || 0;
                            const productName = product.name || '未知商品';
                            const productId = product.id || '';
                            const productImage = product.mainImage || product.image || null;

                            return `
                                <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 10px; cursor: pointer; transition: all 0.2s; display: flex; align-items: center; gap: 10px;"
                                     onclick="carouselEditor.addProduct('${productId}', '${productName.replace(/'/g, "\\'")}', ${price}, '${productImage || ''}')"
                                     onmouseover="this.style.backgroundColor='#e9ecef'"
                                     onmouseout="this.style.backgroundColor='#f8f9fa'">
                                    <div style="width: 50px; height: 50px; flex-shrink: 0;">
                                        ${productImage ?
                                            `<img src="${productImage}" alt="${productName}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                             <div style="display: none; width: 100%; height: 100%; background: #e9ecef; border-radius: 4px; align-items: center; justify-content: center; font-size: 12px; color: #666;">
                                                 <i class="fas fa-image"></i>
                                             </div>` :
                                            `<div style="width: 100%; height: 100%; background: #e9ecef; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666;">
                                                 <i class="fas fa-image"></i>
                                             </div>`
                                        }
                                    </div>
                                    <div style="flex: 1;">
                                        <h5 style="margin-bottom: 5px; font-size: 14px; color: #333;">${productName}</h5>
                                        <p style="color: #e74c3c; font-weight: bold; margin: 0;">¥${price.toFixed(2)}</p>
                                        <small style="color: #666;">ID: ${productId}</small>
                                    </div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                `;
                resultsContainer.style.display = 'block';
            }

            addProduct(productId, productName, productPrice, productImage = null) {
                // 检查是否已添加
                if (this.selectedProducts.some(p => p.id === productId)) {
                    alert('该商品已添加');
                    return;
                }

                this.selectedProducts.push({
                    id: productId,
                    name: productName,
                    price: productPrice,
                    image: productImage
                });
                
                this.renderSelectedProducts();
                
                // 隐藏搜索结果
                document.getElementById('searchResults').style.display = 'none';
                document.getElementById('productSearch').value = '';
            }

            removeProduct(productId) {
                this.selectedProducts = this.selectedProducts.filter(p => p.id !== productId);
                this.renderSelectedProducts();
            }

            renderSelectedProducts() {
                const container = document.getElementById('selectedProducts');
                
                if (this.selectedProducts.length === 0) {
                    container.innerHTML = `
                        <div class="no-products">
                            <i class="fas fa-box-open" style="font-size: 24px; margin-bottom: 10px;"></i>
                            <p>暂未选择商品</p>
                        </div>
                    `;
                    return;
                }
                
                container.innerHTML = this.selectedProducts.map(product => `
                    <div class="product-item">
                        <button class="remove-product" onclick="carouselEditor.removeProduct('${product.id}')">
                            <i class="fas fa-times"></i>
                        </button>
                        <div class="product-image">
                            ${product.image ?
                                `<img src="${product.image}" alt="${product.name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                 <div class="no-image" style="display: none;">
                                     <i class="fas fa-image"></i>
                                     <span>暂无图片</span>
                                 </div>` :
                                `<div class="no-image">
                                     <i class="fas fa-image"></i>
                                     <span>暂无图片</span>
                                 </div>`
                            }
                        </div>
                        <h4>${product.name}</h4>
                        <p class="product-price">¥${product.price.toFixed(2)}</p>
                    </div>
                `).join('');
            }

            saveCarousel() {
                const formData = new FormData();
                const form = document.getElementById('carouselForm');
                
                // 获取表单数据
                const title = form.title.value.trim();

                if (!title) {
                    alert('请输入轮播图标题');
                    return;
                }

                // 构建数据
                const carouselData = {
                    title,
                    products: this.selectedProducts
                };
                
                if (this.carouselId) {
                    carouselData.id = this.carouselId;
                }
                
                formData.append('carouselData', JSON.stringify(carouselData));
                
                if (this.uploadedImage) {
                    formData.append('image', this.uploadedImage);
                }
                
                // 发送到服务器
                const url = this.carouselId ? `/api/carousels/${this.carouselId}` : '/api/carousels';
                const method = this.carouselId ? 'PUT' : 'POST';
                
                fetch(url, {
                    method: method,
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(this.carouselId ? '轮播图更新成功' : '轮播图创建成功');
                        window.location.href = 'carousel-management.html';
                    } else {
                        alert('保存失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('保存轮播图失败:', error);
                    alert('保存失败，请稍后重试');
                });
            }
        }

        // 初始化编辑器
        let carouselEditor;
        document.addEventListener('DOMContentLoaded', function() {
            carouselEditor = new CarouselEditor();
        });
    </script>
</body>
</html>
