<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>澳洲运输价格 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .page-title {
            color: #0c4da2;
            font-size: 32px;
            margin-bottom: 0;
        }
        
        .back-button {
            display: inline-block;
            color: #0c4da2;
            text-decoration: none;
            font-size: 16px;
            background-color: #fff;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background-color: #f5f5f5;
        }
        
        .content-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 100%;
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            color: #666;
            font-size: 14px;
        }

        /* 标签页样式 */
        .tab-container {
            width: 100%;
            margin-bottom: 30px;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }

        .tab-button {
            padding: 12px 24px;
            background-color: #f0f0f0;
            border: none;
            border-radius: 5px 5px 0 0;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin-right: 5px;
            transition: all 0.3s ease;
        }

        .tab-button:hover {
            background-color: #e0e0e0;
        }

        .tab-button.active {
            background-color: #0c4da2;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .page-heading {
            text-align: center;
            color: #0c4da2;
            margin-bottom: 30px;
            font-size: 28px;
        }
        
        /* 添加黑色边框样式 */
        table th, table td {
            border: 1px solid #000000 !important;
        }
        
        /* 表格标题行样式 */
        table thead tr {
            background-color: #dce6f2 !important;
        }
        
        /* 表格内容行样式 */
        table tbody tr {
            background-color: #c5d9f1 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-container">
                <img src="../img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="page-title">澳洲运输价格</h1>
            </div>
            <a href="../prices.html" class="back-button">返回价格页</a>
        </div>
        
        <div class="content-container">
            <!-- 运输方式标签页 -->
            <div class="shipping-tabs" style="display: flex; margin-bottom: 30px;">
                <div class="shipping-tab active" style="flex: 1; text-align: center; background-color: #0c4da2; color: white; padding: 15px; font-size: 20px; font-weight: bold; cursor: pointer;" onclick="openShippingTab('sea')">澳洲海运价格表</div>
                <div class="shipping-tab" style="flex: 1; text-align: center; background-color: #e9ecef; color: #333; padding: 15px; font-size: 20px; font-weight: bold; cursor: pointer;" onclick="openShippingTab('air')">澳洲空派价格表</div>
            </div>
            
            <!-- 海运价格内容 -->
            <div id="seaShipping" class="shipping-content">
                <!-- 澳洲海运小标题和价格表 -->
                <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">海运（普货/敏感货）包清关 包税费 派送到门</h3>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <thead>
                        <tr style="background-color: #d9e2f3;">
                            <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">货物类型</th>
                            <th style="border: 1px solid #000000; padding: 8px; text-align: center;">5kg-22kg</th>
                            <th style="border: 1px solid #000000; padding: 8px; text-align: center;">50kg以上</th>
                            <th style="border: 1px solid #000000; padding: 8px; text-align: center;">100kg以上</th>
                            <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">时效</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="background-color: #d6e6f7;">
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">普货</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">16/kg</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">14/kg</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">12/kg</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">40-45天提取</td>
                        </tr>
                        <tr style="background-color: #d6e6f7;">
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">敏货</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">19/kg</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">17/kg</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">15/kg</td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- 标签页导航 -->
                <div class="tab-container">
                    <div class="tabs">
                        <button class="tab-button active" onclick="openTab(event, 'sydney')">悉尼</button>
                        <button class="tab-button" onclick="openTab(event, 'melbourne')">墨尔本</button>
                        <button class="tab-button" onclick="openTab(event, 'brisbane')">布里斯班</button>
                        <button class="tab-button" onclick="openTab(event, 'adelaide')">阿德莱德</button>
                        <button class="tab-button" onclick="openTab(event, 'perth')">珀斯</button>
                    </div>

                    <!-- 悉尼价格表 -->
                    <div id="sydney" class="tab-content active">
                        <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">澳洲海运超大件普货</h3>
                        <p style="background-color: #e9ecef; padding: 8px; margin-bottom: 10px; font-size: 14px; text-align: center;">可接卡板货、木箱、航空箱、家私家具，建材等各种不规则包装的超大件.</p>
                        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                            <thead>
                                <tr style="background-color: #d9e2f3;">
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" colspan="2">海外仓/重量</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">167KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">500KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1000KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">2000KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">3000KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="3">悉尼</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">SYD-1区</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">11</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">10.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">8.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">7</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">6.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="3">开船后18天到港</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">SYD-2区</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">11</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">10.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">10</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">9</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">8</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">SYD-3区</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">12</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">11</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">9</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">9.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">8.5</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 墨尔本价格表 -->
                    <div id="melbourne" class="tab-content">
                        <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">澳洲海运超大件普货</h3>
                        <p style="background-color: #e9ecef; padding: 8px; margin-bottom: 10px; font-size: 14px; text-align: center;">可接卡板货、木箱、航空箱、家私家具，建材等各种不规则包装的超大件.</p>
                        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                            <thead>
                                <tr style="background-color: #d9e2f3;">
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" colspan="2">海外仓/重量</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">167KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">500KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1000KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">2000KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">3000KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="3">墨尔本</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">MEL-1区</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">12</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">11.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">10</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">9</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">8.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="3">开船后18天到港</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">MEL-2区</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">12</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">11</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">9</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">9.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">8.5</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">MEL-3区</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">12</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">11.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">10.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">10</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">9</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 布里斯班价格表 -->
                    <div id="brisbane" class="tab-content">
                        <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">澳洲海运超大件普货</h3>
                        <p style="background-color: #e9ecef; padding: 8px; margin-bottom: 10px; font-size: 14px; text-align: center;">可接卡板货、木箱、航空箱、家私家具，建材等各种不规则包装的超大件.</p>
                        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                            <thead>
                                <tr style="background-color: #d9e2f3;">
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" colspan="2">海外仓/重量</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">167KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">500KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1000KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">2000KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">3000KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="3">布里斯班</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">BNE-1区</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">12</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">11.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">10</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">9</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">8.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="3">开船后18天到港</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">BNE-2区</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">12</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">11</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">9</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">9.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">8.5</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">BNE-3区</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">12</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">11.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">10.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">10</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">9</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 阿德莱德价格表 -->
                    <div id="adelaide" class="tab-content">
                        <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">澳洲海运超大件普货</h3>
                        <p style="background-color: #e9ecef; padding: 8px; margin-bottom: 10px; font-size: 14px; text-align: center;">可接卡板货、木箱、航空箱、家私家具，建材等各种不规则包装的超大件.</p>
                        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                            <thead>
                                <tr style="background-color: #d9e2f3;">
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" colspan="2">海外仓/重量</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">167KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">500KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1000KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">2000KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">3000KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">阿德莱德</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">/</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">15</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">14</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">12</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">开船后18天到港</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 珀斯价格表 -->
                    <div id="perth" class="tab-content">
                        <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">澳洲海运超大件普货</h3>
                        <p style="background-color: #e9ecef; padding: 8px; margin-bottom: 10px; font-size: 14px; text-align: center;">可接卡板货、木箱、航空箱、家私家具，建材等各种不规则包装的超大件.</p>
                        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                            <thead>
                                <tr style="background-color: #d9e2f3;">
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" colspan="2">海外仓/重量</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">167KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">500KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1000KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">2000KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">3000KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">珀斯</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">/</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">14.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">12.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">开船后18天到港</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- 空派价格内容 -->
            <div id="airShipping" class="shipping-content" style="display: none;">
                <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">空运（普货/敏感货）包清关 包税费 派送到门</h3>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <thead>
                        <tr style="background-color: #d9e2f3;">
                            <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">货物类型</th>
                            <th style="border: 1px solid #000000; padding: 8px; text-align: center;" colspan="2">0.5-9.5kg</th>
                            <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">0-30kg以上</th>
                            <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">50-100kg以上</th>
                            <th style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">时效</th>
                        </tr>
                        <tr style="background-color: #d9e2f3;">
                            <th style="border: 1px solid #000000; padding: 8px; text-align: center;">首重</th>
                            <th style="border: 1px solid #000000; padding: 8px; text-align: center;">续重</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="background-color: #d6e6f7;">
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">普货</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">70/kg</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">32/kg</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">60/kg</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">55/kg</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;" rowspan="2">7-15天提取</td>
                        </tr>
                        <tr style="background-color: #d6e6f7;">
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">敏货</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">90/kg</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">34/kg</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">66/kg</td>
                            <td style="border: 1px solid #000000; padding: 8px; text-align: center;">62/kg</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <footer>
            <p>&copy; 2023 金舟国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </footer>
    </div>

    <script>
        function openTab(evt, tabName) {
            var i, tabcontent, tabbuttons;
            
            tabcontent = document.getElementsByClassName("tab-content");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            
            tabbuttons = document.getElementsByClassName("tab-button");
            for (i = 0; i < tabbuttons.length; i++) {
                tabbuttons[i].className = tabbuttons[i].className.replace(" active", "");
            }
            
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }
        
        function openShippingTab(tabName) {
            var i, shippingContent, shippingTabs;
            
            shippingContent = document.getElementsByClassName("shipping-content");
            for (i = 0; i < shippingContent.length; i++) {
                shippingContent[i].style.display = "none";
            }
            
            shippingTabs = document.getElementsByClassName("shipping-tab");
            for (i = 0; i < shippingTabs.length; i++) {
                shippingTabs[i].className = shippingTabs[i].className.replace(" active", "");
                shippingTabs[i].style.backgroundColor = "#e9ecef";
                shippingTabs[i].style.color = "#333";
            }
            
            document.getElementById(tabName + "Shipping").style.display = "block";
            event.currentTarget.className += " active";
            event.currentTarget.style.backgroundColor = "#0c4da2";
            event.currentTarget.style.color = "white";
        }
    </script>
</body>
</html> 