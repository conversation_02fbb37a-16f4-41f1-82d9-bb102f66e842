<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加拿大运输价格 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .page-title {
            color: #0c4da2;
            font-size: 32px;
            margin-bottom: 0;
        }
        
        .back-button {
            display: inline-block;
            color: #0c4da2;
            text-decoration: none;
            font-size: 16px;
            background-color: #fff;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background-color: #f5f5f5;
        }
        
        .content-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 100%;
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            color: #666;
            font-size: 14px;
        }

        /* 标签页样式 */
        .tab-container {
            width: 100%;
            margin-bottom: 30px;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }

        .tab-button {
            padding: 12px 24px;
            background-color: #f0f0f0;
            border: none;
            border-radius: 5px 5px 0 0;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin-right: 5px;
            transition: all 0.3s ease;
        }

        .tab-button:hover {
            background-color: #e0e0e0;
        }

        .tab-button.active {
            background-color: #0c4da2;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .page-heading {
            text-align: center;
            color: #0c4da2;
            margin-bottom: 30px;
            font-size: 28px;
        }
        
        /* 添加黑色边框样式 */
        table th, table td {
            border: 1px solid #000000 !important;
        }
        
        /* 表格标题行样式 */
        table thead tr {
            background-color: #dce6f2 !important;
        }
        
        /* 表格内容行样式 */
        table tbody tr {
            background-color: #c5d9f1 !important;
        }
        
        /* 主标签页样式 */
        .main-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: none;
        }
        
        .main-tab-button {
            flex: 1;
            text-align: center;
            padding: 15px;
            font-size: 20px;
            font-weight: bold;
            background-color: #f0f0f0;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .main-tab-button.active {
            background-color: #0c4da2;
            color: white;
        }
        
        .main-tab-content {
            display: none;
        }
        
        .main-tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-container">
                <img src="../img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="page-title">加拿大运输价格</h1>
            </div>
            <a href="../prices.html" class="back-button">返回价格页</a>
        </div>
        
        <div class="content-container">
            <!-- 主标签页导航 -->
            <div class="main-tabs">
                <button class="main-tab-button active" onclick="openMainTab(event, 'sea-shipping')">加拿大海运价格表</button>
                <button class="main-tab-button" onclick="openMainTab(event, 'small-package')">加拿大空派价格表</button>
            </div>

            <!-- 海运价格表内容 -->
            <div id="sea-shipping" class="main-tab-content active">
                <!-- 小件海运价格表 -->
                <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">海运（普货/敏感货）包清关 包税费 派送到门</h3>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <thead>
                        <tr style="background-color: #d9e2f3;">
                            <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">货物类型</th>
                            <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">10kg-30kg</th>
                            <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">0.1kg-50kg</th>
                            <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">50.1-100kg</th>
                            <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="background-color: #b8cce4;">
                            <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">普货</td>
                            <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">32/kg</td>
                            <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">30/kg</td>
                            <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">28/kg</td>
                            <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">40-45天提取</td>
                        </tr>
                        <tr style="background-color: #b8cce4;">
                            <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">敏货</td>
                            <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">36/kg</td>
                            <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">34/kg</td>
                            <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">32/kg</td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- 标签页导航 -->
                <div class="tab-container">
                    <div class="tabs">
                        <button class="tab-button active" onclick="openTab(event, 'toronto')">多伦多</button>
                        <button class="tab-button" onclick="openTab(event, 'vancouver')">温哥华</button>
                    </div>

                    <!-- 多伦多价格表 -->
                    <div id="toronto" class="tab-content active">
                        <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">加拿大海运（多伦多）超大件普货</h3>
                        <p style="background-color: #e9ecef; padding: 8px; margin-bottom: 10px; font-size: 14px; text-align: center;">仅运普通货物：家具，建材，日用品等超长超重件，每票不超20个品名，务必如实申报否则加关，一律不做赔偿；价格已包含关税、已含派送到门费用。</p>
                        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                            <thead>
                                <tr style="background-color: #d9e2f3;">
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">省份</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">城市</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">167-270KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">271-501kg</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">502-835kg</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">836-1670kg</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1671kg以上</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">3000kg以上</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="5">ON省</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">多伦多主区: Brampton（布兰顿）、North York（北约克）、Mississauga（密西沙加）、Markham（万锦）、Thornhill(荆山)、</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">15.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">15.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">14.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">14.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="11">开船后38-45天</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">多伦多二区: Oakville（奥克维尔）、Milton（米尔顿）、Bolton（博尔顿）、Halton Hill/西贡山, Burlington（伯灵顿）、Caledon（卡里顿）、</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">15.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">15.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">14.5</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">多伦多三区 汉密尔顿Hamilton Guelph圭尔夫, Kitchener基奇纳, Waterloo滑铁卢, Cambridge剑桥, Brantford布兰特福德</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">15.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">15.0</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">伦敦London（市区商业地址）,</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">15.5</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">渥太华Ottawa(市区商业地址) ,按照 K1A 0W9邮编为中心30KM内，超过30km的增加12RMB/公里/立方</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.0</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="3">QC省</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">三河市 (Trois-Rivières) 舍布鲁克市(Sherbrooke) 加蒂诺 (Gatineau)（市区商业地址）</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.5</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">蒙特利尔Montreal(市区商业地址) ,按照 H5A 1K6 邮编为中心30KM内，超过30km的增加12RMB/公里/立方</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">15.5</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">魁北克市Quebec(市区商业地址)</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">21.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.0</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">PEI省</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">爱德华王子岛Charlottetown 夏洛特敦按照C1A 4P3邮编为中心30KM内，超过30km的增加12RMB/公里/立方</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">26.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">24.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">22.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">21.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20.0</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">NS省</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">哈利法克斯Halifax(市区商业地址) 按照B3K 3B1邮编为中心30KM内，超过30km的增加12RMB/公里/立方</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">26.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">25.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">22.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">21.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20.0</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">NL</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">纽芬兰岛</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" colspan="3">300kg起运26/kg</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">24.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">24.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">23.5</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 温哥华价格表 -->
                    <div id="vancouver" class="tab-content">
                        <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">加拿大海运（温哥华）超大件普货</h3>
                        <p style="background-color: #e9ecef; padding: 8px; margin-bottom: 10px; font-size: 14px; text-align: center;">仅运普通货物：家具，建材，日用品等超长超重件，每票不超20个品名，务必如实申报否则加关，一律不做赔偿；价格已包含关税、已含派送到门费用。</p>
                        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                            <thead>
                                <tr style="background-color: #d9e2f3;">
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">省份</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">城市</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">167-270KG</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">271-501kg</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">502-835kg</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">836-1670kg</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1671kg以上</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">3000kg以上</th>
                                    <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="4">BC省</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">温哥华一区: Vancouver（温哥华）、West Vancouver（西温）、Delta（三角洲）、North</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="9">开船后38-45天</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">温哥华二区: Surrey（素里）、Whiterock（白石）、Langley（兰里）、Coquitlam（高贵林）、</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.0</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">维多利亚Victoria</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" colspan="3">300kg起步--835kg 19.5/kg</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.5</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">基洛纳Kelowna）Kamloops（坎卢普斯）</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.5</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">AB省</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">卡尔加里: Calgary市区30公里内</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">15.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">15.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">14.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">14.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">13.5</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">埃德蒙顿: Edmonton</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">17.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">16.0</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">SK省</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">萨斯卡通Saskatoon 里贾纳Regina</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">22.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">21.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.5</td>
                                </tr>
                                <tr style="background-color: #b8cce4;">
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">MB省</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: left;">温尼伯 Winnipeg</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">19.0</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.5</td>
                                    <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">18.0</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <tr>
                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; background-color: white;">其他超范围的私人地址及其他城市单询</td>
                        <td style="border: 1px solid #000000; padding: 8px; text-align: center; background-color: white;">可接整托、木箱、木架、超大件、超重货、超长件、不规则包装</td>
                    </tr>
                </table>
                
                <p>1、含加拿大海关检查费用</p>
                <p>2、一切货物任意一边≥300cm，重量一件1000KG，需额外收取1000RMB/票 (包含自提货物)</p>
                <p>3、单件实重≥40kg或者≥1立方，需要打木箱/木架/托（卡脚必须预留9cm以上）(木箱、木架需要带合页开关没有合页我司操作80元/件)</p>
                <p>4、入仓货物要求包装牢固坚固且不破损，如件箱变形、破损的，需加固包装的加收5人民币/箱，低消50人民币/票，整托、木架、木箱货根据实际情况另算</p>
                <p>5、货物入仓后，由于客户的其它要求，造成仓库需要重复工作的，我司最低收取10人民币/方额外操作费，低消50人民币/票，如：如重新测量，拍照核实等（我司没有测量错误，或者没有义务重复工作的情况下方可收取费用）</p>
                <p>6、<span style="color: red;">海外通知已截货物货物到港前200RMB/单，到港后改地址不保证修改成功200USD,如因运费未付导致的扣货留仓，自动产生此费用</span></p>
                <p>7、货物发出后更改地址的，卡派50RMB/单，快递派送100元/单，更改地址后如果产生异地派送费，需要额外补足运费（如加拿大东部地址改到加拿大西部地址）</p>
                <p>8、国外免仓储费期限：多伦多折柜起7个自然日，卡尔加里和温哥华是3个自然日，超过期限8RMB/方/天</p>
                
                <hr style="border: 1px solid #ddd; margin: 15px 0;">
                
                <p>多伦多加拿大标准托盘尺寸，122cm×100cm， 国内尺量按这个长、宽，进行打托。</p>
                <p>高度控制在200cm以内，这样，卡派，最经济</p>
                <p>温哥华: 长度在215m，宽度1.8m，高度2m（3边之和不超过4.5m），体重不超过500kg，超了这些尺寸都不好处理</p>
                <p>卡加本地的需要在1.8米左右长度，高度控制在1.8米，重量也是不超过1500lb，在用尾板的情况下，不用尾板的情况下 重量可以到2000lb 左右，外省的一般长2m 高2m 以下的， 重量在1500lb以下， 这些尺寸超了之后就不是很好搞，尤其是在需要尾板的情况下</p>
            </div>
            
            <!-- 小件海运价格表内容 -->
            <div id="small-package" class="main-tab-content">
                <div class="tab-container">
                    <h3 style="background-color: #ffbb00; color: #000; padding: 10px; text-align: center; font-weight: bold; margin-bottom: 5px;">空运（普货/敏感货）包清关 包税费 派送到门</h3>
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                        <thead>
                            <tr style="background-color: #d9e2f3;">
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">货物类型</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">1-10kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">10.5-20kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">20-30kg</th>
                                <th style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">时效</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="background-color: #b8cce4;">
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">普货</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">78/kg</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">76/kg</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">72/kg</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;" rowspan="2">6-15天提取</td>
                            </tr>
                            <tr style="background-color: #b8cce4;">
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">敏货</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">90/kg</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">85/kg</td>
                                <td style="border: 1px solid #b4c6e7; padding: 8px; text-align: center;">82/kg</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <footer>
            <p>&copy; 2023 金舟国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </footer>
    </div>

    <script>
        // 主标签页切换功能
        function openMainTab(evt, tabName) {
            // 隐藏所有主标签内容
            var mainTabContents = document.getElementsByClassName("main-tab-content");
            for (var i = 0; i < mainTabContents.length; i++) {
                mainTabContents[i].classList.remove("active");
            }

            // 移除所有主标签按钮的active类
            var mainTabButtons = document.getElementsByClassName("main-tab-button");
            for (var i = 0; i < mainTabButtons.length; i++) {
                mainTabButtons[i].classList.remove("active");
            }

            // 显示当前主标签内容并激活当前按钮
            document.getElementById(tabName).classList.add("active");
            evt.currentTarget.classList.add("active");
        }
        
        // 标签页切换功能
        function openTab(evt, tabName) {
            // 隐藏所有标签内容
            var tabContents = document.querySelectorAll("#sea-shipping .tab-content");
            for (var i = 0; i < tabContents.length; i++) {
                tabContents[i].classList.remove("active");
            }

            // 移除所有按钮的active类
            var tabButtons = document.querySelectorAll("#sea-shipping .tab-button");
            for (var i = 0; i < tabButtons.length; i++) {
                tabButtons[i].classList.remove("active");
            }

            // 显示当前标签内容并激活当前按钮
            document.getElementById(tabName).classList.add("active");
            evt.currentTarget.classList.add("active");
        }
        
        // 小件标签页切换功能
        function openSmallTab(evt, tabName) {
            // 隐藏所有标签内容
            var tabContents = document.querySelectorAll("#small-package .tab-content");
            for (var i = 0; i < tabContents.length; i++) {
                tabContents[i].classList.remove("active");
            }

            // 移除所有按钮的active类
            var tabButtons = document.querySelectorAll("#small-package .tab-button");
            for (var i = 0; i < tabButtons.length; i++) {
                tabButtons[i].classList.remove("active");
            }

            // 显示当前标签内容并激活当前按钮
            document.getElementById(tabName).classList.add("active");
            evt.currentTarget.classList.add("active");
        }
    </script>
</body>
</html> 