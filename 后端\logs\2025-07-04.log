[2025-07-04T03:01:06.923Z] 服务器已启动，监听端口 8080
[2025-07-04T03:01:06.940Z] 计划下次日志清理时间: 2025-07-29T17:00:00.000Z
[2025-07-04T03:01:59.876Z] 用户登录成功: 1111
[2025-07-04T03:15:49.044Z] 用户登录成功: 1111
[2025-07-04T03:32:59.272Z] 保存用户表单数据: 1111
[2025-07-04T03:32:59.276Z] 保存用户当前步骤: 1111, 步骤: 2
[2025-07-04T03:33:02.810Z] 保存用户表单数据: 1111
[2025-07-04T03:33:08.170Z] 保存用户表单数据: 1111
[2025-07-04T03:33:08.769Z] 保存用户表单数据: 1111
[2025-07-04T03:33:08.796Z] 保存用户表单数据: 1111
[2025-07-04T03:33:10.078Z] 保存用户表单数据: 1111
[2025-07-04T03:33:20.021Z] 保存用户表单数据: 1111
[2025-07-04T03:33:21.566Z] 保存用户物流码数据: 1111
[2025-07-04T03:33:21.818Z] 保存用户物流码数据: 1111
[2025-07-04T03:33:22.104Z] 保存用户物流码数据: 1111
[2025-07-04T03:33:22.425Z] 保存用户物流码数据: 1111
[2025-07-04T03:33:22.934Z] 保存用户物流码数据: 1111
[2025-07-04T03:33:23.299Z] 保存用户物流码数据: 1111
[2025-07-04T03:33:23.521Z] 保存用户物流码数据: 1111
[2025-07-04T03:33:23.909Z] 保存用户物流码数据: 1111
[2025-07-04T03:33:24.729Z] 保存用户物流码数据: 1111
[2025-07-04T03:33:29.135Z] 用户 1111 上传图片: image-1751600009132-96495699.png, 物流码: 物流码1
[2025-07-04T03:33:29.139Z] 保存用户物流码数据: 1111
[2025-07-04T03:33:30.382Z] 保存用户物流码数据: 1111
[2025-07-04T03:33:30.390Z] 添加新订单: JZ17516000103798186, 用户: 1111
[2025-07-04T03:33:32.000Z] 删除用户表单数据: 1111
[2025-07-04T03:33:32.002Z] 删除用户物流码数据: 1111
[2025-07-04T03:33:32.005Z] 删除用户当前步骤: 1111
