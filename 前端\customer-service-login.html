<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服登录 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .page-title {
            color: #28a745;
            font-size: 32px;
            margin-bottom: 0;
        }
        
        .back-button {
            display: inline-block;
            color: #28a745;
            text-decoration: none;
            font-size: 16px;
            background-color: #fff;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background-color: #f5f5f5;
        }
        
        .login-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .login-form {
            display: flex;
            flex-direction: column;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #28a745;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            border-color: #28a745;
            outline: none;
        }
        
        .submit-button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .submit-button:hover {
            background-color: #218838;
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            color: #666;
            font-size: 14px;
        }
        
        /* 优雅的通知系统 */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            max-width: 350px;
        }
        
        .toast {
            display: flex;
            align-items: center;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            margin-bottom: 15px;
            padding: 12px 20px;
            overflow: hidden;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        .toast.toast-success {
            border-left: 5px solid #28a745;
        }
        
        .toast.toast-error {
            border-left: 5px solid #dc3545;
        }
        
        .toast.toast-info {
            border-left: 5px solid #17a2b8;
        }
        
        .toast.toast-warning {
            border-left: 5px solid #ffc107;
        }
        
        .toast-icon {
            margin-right: 15px;
            font-size: 20px;
        }
        
        .toast-success .toast-icon {
            color: #28a745;
        }
        
        .toast-error .toast-icon {
            color: #dc3545;
        }
        
        .toast-info .toast-icon {
            color: #17a2b8;
        }
        
        .toast-warning .toast-icon {
            color: #ffc107;
        }
        
        .toast-content {
            flex: 1;
        }
        
        .toast-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .toast-message {
            font-size: 14px;
            color: #666;
        }
        
        .toast-close {
            color: #999;
            font-size: 18px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .toast-close:hover {
            color: #333;
        }
        
        .toast-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background-color: rgba(0, 0, 0, 0.1);
            width: 100%;
        }
        
        .toast-progress-bar {
            height: 3px;
            width: 100%;
        }
        
        .toast-success .toast-progress-bar {
            background-color: #28a745;
        }
        
        .toast-error .toast-progress-bar {
            background-color: #dc3545;
        }
        
        .toast-info .toast-progress-bar {
            background-color: #17a2b8;
        }
        
        .toast-warning .toast-progress-bar {
            background-color: #ffc107;
        }
        
        /* 模态对话框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background-color: #fff;
            margin: 15% auto;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
            position: relative;
            max-width: 420px;
            transform: scale(0.8);
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .modal.show .modal-content {
            transform: scale(1);
            opacity: 1;
        }
        
        .modal-title {
            color: #28a745;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
            font-size: 20px;
        }
        
        .modal-text {
            margin-bottom: 25px;
            text-align: center;
            color: #555;
            font-size: 16px;
        }
        
        .modal-btn {
            display: block;
            width: 100%;
            background-color: #28a745;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .modal-btn:hover {
            background-color: #218838;
        }
        
        .modal-icon {
            display: block;
            text-align: center;
            font-size: 48px;
            margin-bottom: 20px;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-container">
                <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="page-title">客服登录</h1>
            </div>
            <a href="login.html" class="back-button">返回用户登录</a>
        </div>
        
        <div class="login-container">
            <!-- 登录表单 -->
            <form class="login-form" id="csLoginForm">
                <div class="form-group">
                    <label for="username">客服账号</label>
                    <input type="text" id="username" name="username" placeholder="请输入客服账号" required>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" placeholder="请输入密码" required>
                </div>
                
                <button type="submit" class="submit-button" id="submitBtn">登录</button>
            </form>
            
            <!-- 错误信息容器 -->
            <div id="errorContainer" style="display: none; background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin-top: 10px; text-align: center;">
                <p id="errorMessage"></p>
            </div>
        </div>

        <footer>
            <p>&copy; 2023 金舟国际物流 - Jin Zhou International Logistics. All Rights Reserved.</p>
        </footer>
    </div>
    
    <!-- 模态弹窗 -->
    <div id="message-modal" class="modal">
        <div class="modal-content">
            <div class="modal-icon" id="modal-icon"><i class="fas fa-check-circle"></i></div>
            <h3 class="modal-title" id="modal-title"></h3>
            <p class="modal-text" id="modal-text"></p>
            <button class="modal-btn" id="modal-btn">确定</button>
        </div>
    </div>
    
    <!-- 优雅提示的容器 -->
    <div class="toast-container" id="toast-container"></div>

    <script>
        // API地址 - 后端服务地址
        const API_BASE_URL = 'http://localhost:8080';

        // 显示模态弹窗的函数
        function showModal(title, text, callback, type = 'success') {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-text').textContent = text;
            
            // 设置图标
            const modalIcon = document.getElementById('modal-icon');
            if (type === 'success') {
                modalIcon.innerHTML = '<i class="fas fa-check-circle" style="color: #28a745;"></i>';
            } else if (type === 'error') {
                modalIcon.innerHTML = '<i class="fas fa-times-circle" style="color: #dc3545;"></i>';
            } else if (type === 'info') {
                modalIcon.innerHTML = '<i class="fas fa-info-circle" style="color: #17a2b8;"></i>';
            } else if (type === 'warning') {
                modalIcon.innerHTML = '<i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>';
            }
            
            const modal = document.getElementById('message-modal');
            modal.style.display = 'block';
            
            // 添加显示类以触发动画
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
            
            document.getElementById('modal-btn').onclick = function() {
                modal.classList.remove('show');
                
                // 等待动画完成后隐藏
                setTimeout(() => {
                    modal.style.display = 'none';
                    if (callback) callback();
                }, 300);
            };
        }

        // 显示优雅的toast通知
        function showToast(message, type = 'info', title = '', duration = 5000) {
            const container = document.getElementById('toast-container');
            
            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            
            // 设置图标
            let iconClass = 'info-circle';
            if (type === 'success') iconClass = 'check-circle';
            if (type === 'error') iconClass = 'times-circle';
            if (type === 'warning') iconClass = 'exclamation-triangle';
            
            // 构建toast内容
            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="fas fa-${iconClass}"></i>
                </div>
                <div class="toast-content">
                    ${title ? `<div class="toast-title">${title}</div>` : ''}
                    <div class="toast-message">${message}</div>
                </div>
                <div class="toast-close">
                    <i class="fas fa-times"></i>
                </div>
                <div class="toast-progress">
                    <div class="toast-progress-bar"></div>
                </div>
            `;
            
            // 添加到容器
            container.appendChild(toast);
            
            // 触发动画
            setTimeout(() => {
                toast.classList.add('show');
                
                // 设置进度条动画
                const progressBar = toast.querySelector('.toast-progress-bar');
                progressBar.style.transition = `width ${duration}ms linear`;
                progressBar.style.width = '0';
            }, 10);
            
            // 设置关闭事件
            const closeBtn = toast.querySelector('.toast-close');
            closeBtn.addEventListener('click', () => {
                removeToast(toast);
            });
            
            // 自动关闭
            const closeTimeout = setTimeout(() => {
                removeToast(toast);
            }, duration);
            
            // 移除Toast的函数
            function removeToast(toast) {
                clearTimeout(closeTimeout);
                toast.classList.remove('show');
                
                // 动画结束后移除DOM
                setTimeout(() => {
                    if (container.contains(toast)) {
                        container.removeChild(toast);
                    }
                }, 300);
            }
        }

        // API请求函数
        async function callAPI(endpoint, data, method = 'POST') {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };
                
                if (method === 'POST') {
                    options.body = JSON.stringify(data);
                } else if (method === 'GET' && data) {
                    endpoint = `${endpoint}?${new URLSearchParams(data)}`;
                }
                
                const url = `${API_BASE_URL}/api/${endpoint}`;
                const response = await fetch(url, options);
                const result = await response.json();
                return result;
            } catch (error) {
                console.error('API请求失败:', error);
                return { success: false, message: '服务器连接失败，请检查后端服务是否运行' };
            }
        }

        // 客服登录功能
        const csLoginForm = document.getElementById('csLoginForm');
        if (csLoginForm) {
            csLoginForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value.trim();

                // 基本验证
                if (!username) {
                    showToast('请输入客服账号', 'warning', '验证失败');
                    return;
                }

                if (!password) {
                    showToast('请输入密码', 'warning', '验证失败');
                    return;
                }

                // 禁用登录按钮，防止重复提交
                const submitButton = document.getElementById('submitBtn');
                const originalBtnText = submitButton.textContent;
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';

                try {
                    // 调用后端API进行客服登录验证
                    const result = await callAPI('cs-login', { username, password });

                    if (result.success) {
                        // 确保登录的是客服账号
                        if (!result.admin.isCustomerService) {
                            showToast('您不是客服人员，无法登录', 'error', '登录失败');
                            document.getElementById('password').value = ''; // 清空密码字段
                            return;
                        }
                        
                        // 保存客服信息
                        const csData = {
                            username: result.admin.username,
                            id: result.admin.id,
                            name: result.admin.name,
                            email: result.admin.email,
                            phone: result.admin.phone,
                            isAdmin: false,
                            isCustomerService: true,
                            loginTime: new Date().toISOString()
                        };

                        sessionStorage.setItem('loggedInAdmin', JSON.stringify(csData));

                        // 显示成功消息并跳转
                        showModal('登录成功', '欢迎回来！正在跳转...', function() {
                            window.location.href = 'customer-service-dashboard.html';
                        }, 'success');
                    } else {
                        // 登录失败
                        document.getElementById('errorMessage').textContent = result.message || '账号或密码错误';
                        document.getElementById('errorContainer').style.display = 'block';
                        showToast(result.message || '账号或密码错误', 'error', '登录失败');
                        document.getElementById('password').value = ''; // 清空密码字段
                    }
                } catch (error) {
                    console.error('登录失败:', error);
                    showToast('登录过程中出现错误，请稍后重试', 'error', '系统错误');
                } finally {
                    // 恢复按钮状态
                    submitButton.disabled = false;
                    submitButton.textContent = originalBtnText;
                }
            });
        }
    </script>
</body>
</html> 