<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关键词管理 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .page-title {
            color: #0c4da2;
            font-size: 32px;
            margin-bottom: 0;
        }
        
        .back-button {
            display: inline-block;
            color: #0c4da2;
            text-decoration: none;
            font-size: 16px;
            background-color: #fff;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background-color: #f5f5f5;
        }

        .admin-info {
            display: flex;
            align-items: center;
        }
        
        .admin-avatar {
            width: 40px;
            height: 40px;
            background-color: #0c4da2;
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .logout-btn {
            color: #0c4da2;
            text-decoration: none;
            margin-left: 15px;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        
        .logout-btn:hover {
            background-color: #f0f0f0;
        }
        
        .main-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .card-title {
            color: #0c4da2;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .keyword-form {
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            border-color: #0c4da2;
            outline: none;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #0c4da2;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0a3d82;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .keywords-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .keywords-table th, 
        .keywords-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .keywords-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .keywords-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .keyword-actions {
            display: flex;
            gap: 10px;
        }
        
        .empty-message {
            text-align: center;
            color: #888;
            padding: 30px 0;
        }
        
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .toast {
            background-color: white;
            border-left: 4px solid #0c4da2;
            border-radius: 4px;
            padding: 15px 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            animation: slideIn 0.3s ease-out forwards;
            max-width: 300px;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        .toast-icon {
            margin-right: 10px;
            font-size: 18px;
        }
        
        .toast-content {
            flex-grow: 1;
        }
        
        .toast-title {
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .toast-message {
            font-size: 14px;
            color: #666;
        }
        
        .toast-success {
            border-left-color: #52c41a;
        }
        
        .toast-error {
            border-left-color: #ff4d4f;
        }
        
        .toast-warning {
            border-left-color: #faad14;
        }
        
        .toast-success .toast-icon {
            color: #52c41a;
        }
        
        .toast-error .toast-icon {
            color: #ff4d4f;
        }
        
        .toast-warning .toast-icon {
            color: #faad14;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }
            
            .header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .admin-info {
                margin-top: 15px;
            }
            
            .main-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo-container">
                <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="page-title">关键词管理</h1>
            </div>
            <div class="admin-info">
                <div class="admin-avatar" id="adminInitial">A</div>
                <span id="adminName">管理员</span>
                <a href="#" class="logout-btn" id="logoutBtn">
                    <i class="fas fa-sign-out-alt"></i> 退出
                </a>
                <a href="admin-dashboard.html" class="back-button">
                    <i class="fas fa-arrow-left"></i> 返回控制台
                </a>
            </div>
        </header>
        
        <div class="main-card">
            <h2 class="card-title">关键词列表</h2>
            
            <form id="keywordForm" class="keyword-form">
                <div class="form-group">
                    <label for="keywordInput">添加关键词</label>
                    <input type="text" id="keywordInput" name="keyword" class="form-control" placeholder="请输入关键词" required>
                </div>
                
                <button type="submit" class="btn btn-primary">添加关键词</button>
            </form>
            
            <div class="keywords-list">
                <div class="search-box" style="margin-bottom: 20px;">
                    <input type="text" id="searchKeyword" class="form-control" placeholder="搜索关键词..." style="max-width: 400px;">
                </div>
                <table class="keywords-table">
                    <thead>
                        <tr>
                            <th style="width: 60%">关键词</th>
                            <th style="width: 20%">添加时间</th>
                            <th style="width: 20%">操作</th>
                        </tr>
                    </thead>
                    <tbody id="keywordsTableBody">
                        <!-- 关键词列表将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 添加分页控制 -->
            <div class="pagination-container" style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center;">
                <div class="pagination-info">
                    第 <span id="currentPage">1</span> 页，共 <span id="totalPages">1</span> 页
                </div>
                <div class="pagination-controls" style="display: flex; align-items: center; gap: 10px;">
                    <button class="btn btn-secondary" id="prevPage" disabled style="padding: 6px 12px;">
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <div class="page-input" style="display: flex; align-items: center;">
                        <input type="number" id="pageNumberInput" min="1" style="width: 60px; padding: 6px 12px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" placeholder="页码">
                        <button class="btn btn-secondary" id="goToPage" style="margin-left: 5px; padding: 6px 12px;">
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                    <button class="btn btn-secondary" id="nextPage" disabled style="padding: 6px 12px;">
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <div class="page-size-selector" style="margin-left: 15px;">
                        每页显示:
                        <select id="pageSize" style="margin-left: 5px; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                            <option value="5">5</option>
                            <option value="10" selected>10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="toast-container" id="toastContainer"></div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 分页状态
            const paginationState = {
                currentPage: 1,
                pageSize: 10,
                totalItems: 0,
                totalPages: 1
            };
            
            // 检查管理员登录状态
            const checkAdminAuth = () => {
                const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');
                
                if (!adminData.isAdmin) {
                    // 未登录或不是管理员，重定向到登录页
                    window.location.href = 'admin-login.html';
                    return null;
                }
                
                return adminData;
            };
            
            // 初始化页面
            const initPage = () => {
                console.log('开始初始化页面...');
                const adminData = checkAdminAuth();
                console.log('管理员数据:', adminData);
                if (!adminData) {
                    console.log('没有管理员数据，页面将重定向');
                    return;
                }
                
                // 设置管理员信息
                const adminInitial = document.getElementById('adminInitial');
                const adminName = document.getElementById('adminName');
                
                if (adminData.username) {
                    adminName.textContent = adminData.username;
                    adminInitial.textContent = adminData.username.charAt(0).toUpperCase();
                }
                
                // 退出登录功能
                document.getElementById('logoutBtn').addEventListener('click', (e) => {
                    e.preventDefault();
                    sessionStorage.removeItem('loggedInAdmin');
                    sessionStorage.removeItem('welcomeShown');
                    window.location.href = 'admin-login.html';
                });
                
                // 添加关键词表单提交事件
                document.getElementById('keywordForm').addEventListener('submit', handleKeywordSubmit);
                
                // 添加搜索功能
                const searchInput = document.getElementById('searchKeyword');
                searchInput.addEventListener('input', function() {
                    paginationState.currentPage = 1; // 搜索时重置到第一页
                    loadKeywords(this.value.trim());
                });
                
                // 设置分页控件和事件
                setupPaginationEvents();
                
                // 加载关键词列表
                console.log('准备加载关键词列表...');
                loadKeywords();
            };
            
            // 处理添加关键词表单提交
            const handleKeywordSubmit = async (e) => {
                e.preventDefault();

                const keywordInput = document.getElementById('keywordInput');
                const keyword = keywordInput.value.trim();

                if (!keyword) {
                    showToast('请输入关键词', 'error');
                    return;
                }

                try {
                    // 发送请求到服务器添加关键词
                    const response = await fetch('/api/keywords', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ keyword })
                    });

                    const result = await response.json();

                    if (result.success) {
                        // 清空输入框
                        keywordInput.value = '';

                        // 重新加载关键词列表
                        loadKeywords();

                        // 显示成功提示
                        showToast('关键词添加成功', 'success');
                    } else {
                        showToast(result.message || '添加关键词失败', 'error');
                    }
                } catch (error) {
                    console.error('添加关键词失败:', error);
                    showToast('添加关键词失败，请稍后重试', 'error');
                }
            };
            
            // 加载关键词列表
            const loadKeywords = async (searchTerm = '') => {
                const tableBody = document.getElementById('keywordsTableBody');

                try {
                    console.log('开始加载关键词列表...');
                    // 从服务器获取关键词列表
                    const response = await fetch('/api/keywords');
                    console.log('API响应状态:', response.status);
                    const result = await response.json();
                    console.log('API响应结果:', result);

                    if (!result.success) {
                        showToast(result.message || '获取关键词列表失败', 'error');
                        return;
                    }

                    const keywords = result.keywords || [];

                    if (keywords.length === 0) {
                        tableBody.innerHTML = '<tr><td colspan="3" class="empty-message">暂无关键词</td></tr>';
                        updatePaginationControls(0, 1, 0);
                        return;
                    }

                    // 按添加时间降序排序
                    keywords.sort((a, b) => new Date(b.addedAt) - new Date(a.addedAt));

                    // 过滤关键词（如果有搜索条件）
                    const filteredKeywords = searchTerm
                        ? keywords.filter(k => k.text.toLowerCase().includes(searchTerm.toLowerCase()))
                        : keywords;

                    // 计算分页数据
                    const totalItems = filteredKeywords.length;
                    const totalPages = Math.ceil(totalItems / paginationState.pageSize) || 1;

                    // 确保当前页面在有效范围内
                    if (paginationState.currentPage > totalPages) {
                        paginationState.currentPage = totalPages;
                    }

                    // 计算当前页面显示的数据范围
                    const startIndex = (paginationState.currentPage - 1) * paginationState.pageSize;
                    const endIndex = Math.min(startIndex + paginationState.pageSize, totalItems);
                    const currentPageItems = filteredKeywords.slice(startIndex, endIndex);

                    // 清空现有内容
                    tableBody.innerHTML = '';

                    // 如果搜索结果为空
                    if (filteredKeywords.length === 0) {
                        tableBody.innerHTML = `<tr><td colspan="3" class="empty-message">没有找到匹配"${searchTerm}"的关键词</td></tr>`;
                        updatePaginationControls(0, 1, 0);
                        return;
                    }

                    // 添加关键词行
                    currentPageItems.forEach(keyword => {
                        const row = document.createElement('tr');

                        // 格式化日期
                        const addedAt = new Date(keyword.addedAt);
                        const formattedDate = `${addedAt.getFullYear()}-${String(addedAt.getMonth() + 1).padStart(2, '0')}-${String(addedAt.getDate()).padStart(2, '0')} ${String(addedAt.getHours()).padStart(2, '0')}:${String(addedAt.getMinutes()).padStart(2, '0')}`;

                        row.innerHTML = `
                            <td>${keyword.text}</td>
                            <td>${formattedDate}</td>
                            <td>
                                <div class="keyword-actions">
                                    <button class="btn btn-danger btn-delete" data-id="${keyword.id}">删除</button>
                                </div>
                            </td>
                        `;

                        tableBody.appendChild(row);
                    });

                    // 添加删除按钮事件
                    document.querySelectorAll('.btn-delete').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const keywordId = this.getAttribute('data-id');
                            deleteKeyword(keywordId);
                        });
                    });

                    // 更新分页控件
                    updatePaginationControls(paginationState.currentPage, totalPages, totalItems);
                } catch (error) {
                    console.error('获取关键词列表失败:', error);
                    showToast('获取关键词列表失败，请稍后重试', 'error');
                    tableBody.innerHTML = '<tr><td colspan="3" class="empty-message">加载失败</td></tr>';
                    updatePaginationControls(0, 1, 0);
                }
            };
            
            // 更新分页控件
            const updatePaginationControls = (currentPage, totalPages, totalItems) => {
                document.getElementById('currentPage').textContent = currentPage;
                document.getElementById('totalPages').textContent = totalPages;
                
                // 禁用/启用上一页、下一页按钮
                document.getElementById('prevPage').disabled = currentPage <= 1;
                document.getElementById('nextPage').disabled = currentPage >= totalPages;
                
                // 更新分页状态
                paginationState.currentPage = currentPage;
                paginationState.totalPages = totalPages;
                paginationState.totalItems = totalItems;
            };
            
            // 设置分页事件处理
            const setupPaginationEvents = () => {
                // 上一页按钮
                document.getElementById('prevPage').addEventListener('click', () => {
                    if (paginationState.currentPage > 1) {
                        paginationState.currentPage--;
                        loadKeywords(document.getElementById('searchKeyword').value.trim());
                    }
                });
                
                // 下一页按钮
                document.getElementById('nextPage').addEventListener('click', () => {
                    if (paginationState.currentPage < paginationState.totalPages) {
                        paginationState.currentPage++;
                        loadKeywords(document.getElementById('searchKeyword').value.trim());
                    }
                });
                
                // 跳转到指定页面
                document.getElementById('goToPage').addEventListener('click', () => {
                    const pageNumber = parseInt(document.getElementById('pageNumberInput').value);
                    if (pageNumber && pageNumber > 0 && pageNumber <= paginationState.totalPages) {
                        paginationState.currentPage = pageNumber;
                        loadKeywords(document.getElementById('searchKeyword').value.trim());
                        // 清空输入框
                        document.getElementById('pageNumberInput').value = '';
                    } else {
                        showToast('请输入有效的页码', 'warning');
                    }
                });
                
                // 回车键跳转到指定页面
                document.getElementById('pageNumberInput').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        document.getElementById('goToPage').click();
                    }
                });
                
                // 页面大小选择
                document.getElementById('pageSize').addEventListener('change', function() {
                    paginationState.pageSize = parseInt(this.value);
                    paginationState.currentPage = 1; // 重置到第一页
                    loadKeywords(document.getElementById('searchKeyword').value.trim());
                });
            };

            // 删除关键词
            const deleteKeyword = async (keywordId) => {
                try {
                    // 发送删除请求到服务器
                    const response = await fetch(`/api/keywords/${keywordId}`, {
                        method: 'DELETE'
                    });

                    const result = await response.json();

                    if (result.success) {
                        // 重新加载关键词列表
                        const searchTerm = document.getElementById('searchKeyword').value.trim();
                        loadKeywords(searchTerm);

                        // 显示成功提示
                        showToast(result.message || '关键词删除成功', 'success');
                    } else {
                        showToast(result.message || '删除关键词失败', 'error');
                    }
                } catch (error) {
                    console.error('删除关键词失败:', error);
                    showToast('删除关键词失败，请稍后重试', 'error');
                }
            };
            
            // 显示通知提示
            const showToast = (message, type = 'info', title = '') => {
                const toastContainer = document.getElementById('toastContainer');
                
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                
                let iconClass;
                switch(type) {
                    case 'success':
                        iconClass = 'fas fa-check-circle';
                        break;
                    case 'error':
                        iconClass = 'fas fa-exclamation-circle';
                        break;
                    case 'warning':
                        iconClass = 'fas fa-exclamation-triangle';
                        break;
                    default:
                        iconClass = 'fas fa-info-circle';
                }
                
                toast.innerHTML = `
                    <div class="toast-icon">
                        <i class="${iconClass}"></i>
                    </div>
                    <div class="toast-content">
                        ${title ? `<div class="toast-title">${title}</div>` : ''}
                        <div class="toast-message">${message}</div>
                    </div>
                `;
                
                toastContainer.appendChild(toast);
                
                // 3秒后移除通知
                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    
                    setTimeout(() => {
                        toast.remove();
                    }, 300);
                }, 3000);
            };
            
            // 初始化页面
            console.log('DOM加载完成，开始初始化...');
            initPage();
        });
    </script>
</body>
</html> 