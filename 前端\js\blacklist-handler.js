/**
 * 黑名单用户处理模块
 * 用于在所有需要登录的页面中检查用户账号状态，处理被拉黑用户
 */

// 统一的黑名单信息文本
const DEFAULT_BLACKLIST_MESSAGE = "你的账号因违规导致封号，如需解锁请联系邮箱********************进行申诉，具体详情参考《服务协议》和《隐私政策》";

// 检查用户是否被拉黑
function checkUserBlacklisted() {
    const userData = JSON.parse(sessionStorage.getItem('loggedInUser') || '{}');
    
    // 如果用户未登录，则不需要检查
    if (!userData.id || !userData.isLoggedIn) {
        return false;
    }
    
    // 检查用户账号状态
    fetch('/api/check-session', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ userId: userData.id })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success && data.blacklisted) {
            // 账号已被拉黑，强制登出
            sessionStorage.removeItem('loggedInUser');
            localStorage.removeItem('loggedInUser');
            
            // 显示被拉黑提示
            const message = data.message || DEFAULT_BLACKLIST_MESSAGE;
            showBlacklistedMessage(message);
        }
    })
    .catch(error => {
        console.error('检查账号状态失败:', error);
    });
}

// 检查邮箱是否被拉黑（Promise版本）
async function checkEmailBlacklisted(email) {
    try {
        const response = await fetch('/api/check-email-blacklist', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email })
        });
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('检查邮箱黑名单状态失败:', error);
        return { success: true, blacklisted: false }; // 出错时默认允许继续
    }
}

// 显示账号被拉黑的提示信息
function showBlacklistedMessage(message = DEFAULT_BLACKLIST_MESSAGE) {
    // 检查是否已经存在黑名单模态框，避免重复显示
    if (document.querySelector('.blacklist-modal')) {
        return;
    }
    
    // 创建模态框元素
    const modal = document.createElement('div');
    modal.className = 'blacklist-modal';
    
    modal.innerHTML = `
        <div class="blacklist-modal-content">
            <div class="blacklist-modal-header">
                <h3>⚠️ 账号已被限制</h3>
            </div>
            <div class="blacklist-modal-body">
                <p>${message}</p>
            </div>
            <div class="blacklist-modal-footer">
                <button id="blacklistOkBtn">我知道了</button>
            </div>
        </div>
    `;
    
    // 添加到页面
    document.body.appendChild(modal);
    
    // 添加关闭按钮事件 - 点击后会回到登录页
    document.getElementById('blacklistOkBtn').addEventListener('click', function() {
        modal.remove();
        window.location.href = 'login.html';
    });
    
    // 添加样式
    addBlacklistStyles();
}

// 添加黑名单模态框的样式
function addBlacklistStyles() {
    // 检查是否已经添加过样式
    if (document.getElementById('blacklist-modal-styles')) {
        return;
    }
    
    const style = document.createElement('style');
    style.id = 'blacklist-modal-styles';
    style.textContent = `
        .blacklist-modal {
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .blacklist-modal-content {
            background-color: white;
            border-radius: 8px;
            width: 90%;
            max-width: 450px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }
        .blacklist-modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            color: #dc3545;
        }
        .blacklist-modal-header h3 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .blacklist-modal-body {
            padding: 20px;
            line-height: 1.5;
        }
        .blacklist-modal-footer {
            padding: 15px 20px;
            display: flex;
            justify-content: flex-end;
            border-top: 1px solid #eee;
        }
        #blacklistOkBtn {
            background-color: #0c4da2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        #blacklistOkBtn:hover {
            background-color: #0a3f87;
        }
    `;
    document.head.appendChild(style);
}

// 自动初始化 - 页面加载时检查用户状态
document.addEventListener('DOMContentLoaded', function() {
    // 立即检查一次
    checkUserBlacklisted();
    
    // 定期检查 (每30秒)
    setInterval(checkUserBlacklisted, 30000);
});

// 导出函数供其他模块使用
window.BlacklistHandler = {
    checkUserBlacklisted,
    showBlacklistedMessage,
    checkEmailBlacklisted,
    DEFAULT_BLACKLIST_MESSAGE
}; 