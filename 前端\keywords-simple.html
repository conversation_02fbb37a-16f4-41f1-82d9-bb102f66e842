<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关键词管理 - 简化版</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
        }
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px;
            border-radius: 4px;
            color: white;
            z-index: 1000;
        }
        .toast-success { background-color: #28a745; }
        .toast-error { background-color: #dc3545; }
        .toast-warning { background-color: #ffc107; color: #212529; }

        /* 商品网格样式 */
        .products-section {
            margin-top: 30px;
            padding: 20px 0;
        }
        .products-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        .product-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            transition: box-shadow 0.3s ease;
            cursor: pointer;
        }
        .product-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .product-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            background: #f5f5f5;
        }
        .product-info {
            padding: 10px;
        }
        .product-title {
            font-size: 12px;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
            height: 34px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        .product-price {
            color: #ff4444;
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .product-sales {
            color: #999;
            font-size: 11px;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .products-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }
        @media (max-width: 900px) {
            .products-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        @media (max-width: 600px) {
            .products-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <h1>关键词管理 - 简化版</h1>
    
    <div class="form-group">
        <label>添加关键词</label>
        <input type="text" id="keywordInput" class="form-control" placeholder="请输入关键词">
    </div>
    <button onclick="addKeyword()" class="btn btn-primary">添加关键词</button>
    
    <div class="form-group" style="margin-top: 20px;">
        <label>搜索关键词</label>
        <input type="text" id="searchInput" class="form-control" placeholder="搜索关键词..." onkeyup="filterKeywords()">
    </div>
    
    <table>
        <thead>
            <tr>
                <th>关键词</th>
                <th>添加时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody id="keywordsTable">
            <!-- 关键词列表将在这里显示 -->
        </tbody>
    </table>

    <!-- 商品展示区域 -->
    <div class="products-section">
        <h2>推荐商品</h2>
        <div class="products-grid" id="productsGrid">
            <!-- 商品将通过JavaScript动态加载 -->
        </div>
    </div>

    <div id="toastContainer"></div>
    
    <script>
        let allKeywords = [];
        
        // 页面加载时获取关键词列表和商品列表
        window.onload = function() {
            loadKeywords();
            loadProducts();
        };
        
        // 添加关键词
        async function addKeyword() {
            const keyword = document.getElementById('keywordInput').value.trim();
            if (!keyword) {
                showToast('请输入关键词', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/keywords', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ keyword })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('keywordInput').value = '';
                    loadKeywords();
                    showToast('关键词添加成功', 'success');
                } else {
                    showToast(result.message || '添加关键词失败', 'error');
                }
            } catch (error) {
                console.error('添加关键词失败:', error);
                showToast('添加关键词失败，请稍后重试', 'error');
            }
        }
        
        // 加载关键词列表
        async function loadKeywords() {
            try {
                const response = await fetch('/api/keywords');
                const result = await response.json();
                
                if (result.success) {
                    allKeywords = result.keywords || [];
                    displayKeywords(allKeywords);
                } else {
                    showToast(result.message || '获取关键词列表失败', 'error');
                }
            } catch (error) {
                console.error('获取关键词列表失败:', error);
                showToast('获取关键词列表失败，请稍后重试', 'error');
            }
        }
        
        // 显示关键词列表
        function displayKeywords(keywords) {
            const tableBody = document.getElementById('keywordsTable');
            
            if (keywords.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="3" style="text-align: center;">暂无关键词</td></tr>';
                return;
            }
            
            // 按添加时间降序排序
            keywords.sort((a, b) => new Date(b.addedAt) - new Date(a.addedAt));
            
            tableBody.innerHTML = keywords.map(keyword => {
                const addedAt = new Date(keyword.addedAt);
                const formattedDate = addedAt.toLocaleString('zh-CN');
                
                return `
                    <tr>
                        <td>${keyword.text}</td>
                        <td>${formattedDate}</td>
                        <td>
                            <button class="btn btn-danger" onclick="deleteKeyword('${keyword.id}')">删除</button>
                        </td>
                    </tr>
                `;
            }).join('');
        }
        
        // 删除关键词
        async function deleteKeyword(keywordId) {
            if (!confirm('确定要删除这个关键词吗？')) {
                return;
            }
            
            try {
                const response = await fetch(`/api/keywords/${keywordId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    loadKeywords();
                    showToast(result.message || '关键词删除成功', 'success');
                } else {
                    showToast(result.message || '删除关键词失败', 'error');
                }
            } catch (error) {
                console.error('删除关键词失败:', error);
                showToast('删除关键词失败，请稍后重试', 'error');
            }
        }
        
        // 过滤关键词
        function filterKeywords() {
            const searchTerm = document.getElementById('searchInput').value.trim().toLowerCase();
            
            if (!searchTerm) {
                displayKeywords(allKeywords);
                return;
            }
            
            const filteredKeywords = allKeywords.filter(keyword => 
                keyword.text.toLowerCase().includes(searchTerm)
            );
            
            displayKeywords(filteredKeywords);
        }
        
        // 显示提示消息
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            
            document.getElementById('toastContainer').appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
        
        // 加载商品列表
        async function loadProducts() {
            try {
                // 模拟商品数据，实际项目中应该从API获取
                const mockProducts = [
                    {
                        id: 1,
                        title: "Matlab电气仿真静做Simulink电力电子电机控制故障配电",
                        price: "¥50",
                        sales: "已售1000+件",
                        image: "https://via.placeholder.com/200x150/4CAF50/white?text=Matlab"
                    },
                    {
                        id: 2,
                        title: "上海到美国加拿大新西兰加坡澳洲国际搬家移民留学海运",
                        price: "¥50",
                        sales: "已售16件",
                        image: "https://via.placeholder.com/200x150/2196F3/white?text=海运"
                    },
                    {
                        id: 3,
                        title: "上海搬家公司货运海运家具仕美国英国德国法国意大利双清",
                        price: "¥50",
                        sales: "已售10件",
                        image: "https://via.placeholder.com/200x150/FF9800/white?text=搬家"
                    },
                    {
                        id: 4,
                        title: "香港极地空间公寓小户型儿童房榻榻米上下床衣柜一体套",
                        price: "¥50",
                        sales: "已售500+件",
                        image: "https://via.placeholder.com/200x150/9C27B0/white?text=家具"
                    },
                    {
                        id: 5,
                        title: "香港集运到港澳私人际快递通营连香港进口什货物快递",
                        price: "¥10",
                        sales: "已售5万+件",
                        image: "https://via.placeholder.com/200x150/F44336/white?text=快递"
                    },
                    {
                        id: 6,
                        title: "专业电商设计服务包装设计产品拍摄详情页制作",
                        price: "¥80",
                        sales: "已售200+件",
                        image: "https://via.placeholder.com/200x150/607D8B/white?text=设计"
                    },
                    {
                        id: 7,
                        title: "跨境电商运营服务亚马逊店铺代运营产品上架优化",
                        price: "¥120",
                        sales: "已售50+件",
                        image: "https://via.placeholder.com/200x150/795548/white?text=电商"
                    },
                    {
                        id: 8,
                        title: "网站建设企业官网定制开发响应式网页设计",
                        price: "¥300",
                        sales: "已售30+件",
                        image: "https://via.placeholder.com/200x150/009688/white?text=网站"
                    },
                    {
                        id: 9,
                        title: "小程序开发微信小程序商城系统定制开发",
                        price: "¥500",
                        sales: "已售20+件",
                        image: "https://via.placeholder.com/200x150/3F51B5/white?text=小程序"
                    },
                    {
                        id: 10,
                        title: "APP开发移动应用定制开发iOS安卓双端",
                        price: "¥800",
                        sales: "已售10+件",
                        image: "https://via.placeholder.com/200x150/E91E63/white?text=APP"
                    }
                ];

                displayProducts(mockProducts);
            } catch (error) {
                console.error('加载商品失败:', error);
                showToast('加载商品失败，请稍后重试', 'error');
            }
        }

        // 显示商品列表
        function displayProducts(products) {
            const productsGrid = document.getElementById('productsGrid');

            if (products.length === 0) {
                productsGrid.innerHTML = '<p style="text-align: center; grid-column: 1 / -1;">暂无商品</p>';
                return;
            }

            productsGrid.innerHTML = products.map(product => `
                <div class="product-card" onclick="viewProduct(${product.id})">
                    <img src="${product.image}" alt="${product.title}" class="product-image"
                         onerror="this.src='https://via.placeholder.com/200x150/CCCCCC/666666?text=暂无图片'">
                    <div class="product-info">
                        <div class="product-title">${product.title}</div>
                        <div class="product-price">${product.price}</div>
                        <div class="product-sales">${product.sales}</div>
                    </div>
                </div>
            `).join('');
        }

        // 查看商品详情
        function viewProduct(productId) {
            showToast(`查看商品 ${productId} 的详情`, 'info');
            // 这里可以添加跳转到商品详情页的逻辑
        }

        // 回车键添加关键词
        document.getElementById('keywordInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addKeyword();
            }
        });
    </script>
</body>
</html>
