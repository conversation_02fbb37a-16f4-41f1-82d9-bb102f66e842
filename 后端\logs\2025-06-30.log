[2025-06-30T01:13:52.670Z] 服务器已启动，监听端口 8080
[2025-06-30T01:13:52.688Z] 计划下次日志清理时间: 2025-07-29T17:00:00.000Z
[2025-06-30T01:15:02.113Z] 用户登录成功: 22222
[2025-06-30T01:54:02.790Z] 用户登录成功: 22222
[2025-06-30T02:13:02.521Z] 用户登录成功: 22222
[2025-06-30T02:51:08.357Z] 用户登录成功: 22222
[2025-06-30T02:56:30.638Z] 用户登录成功: 22222
[2025-06-30T03:22:32.318Z] 登录失败: 密码错误 - 111
[2025-06-30T03:22:44.621Z] 登录失败: 密码错误 - 111
[2025-06-30T03:23:55.764Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T03:23:55.765Z] 为 <EMAIL> 生成验证码: 81****
[2025-06-30T03:23:55.766Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T03:23:56.445Z] 验证码发送成功: {"RequestId":"2D189F27-6C9F-5539-B59D-C0118DB61B53","EnvId":"600000202597661455"}
[2025-06-30T03:23:56.447Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-30T03:27:08.801Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T03:27:08.802Z] 为 <EMAIL> 生成验证码: 81****
[2025-06-30T03:27:08.803Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T03:27:09.177Z] 验证码发送成功: {"RequestId":"C373ED86-0654-5A62-BEEE-8CEE90A4E4E3","EnvId":"600000202597663860"}
[2025-06-30T03:27:09.177Z] 当前验证码Map中的邮箱: <EMAIL>, <EMAIL>
[2025-06-30T03:28:25.955Z] 用户注册成功: 1111, <EMAIL>
[2025-06-30T03:28:36.387Z] 用户登录成功: 1111
[2025-06-30T03:29:37.138Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T03:29:37.138Z] 为 <EMAIL> 生成验证码: 60****
[2025-06-30T03:29:37.139Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T03:29:37.583Z] 验证码发送成功: {"RequestId":"69393FD9-5B08-5138-8508-254A356D7135","EnvId":"600000202741643042"}
[2025-06-30T03:29:37.584Z] 当前验证码Map中的邮箱: <EMAIL>, <EMAIL>
[2025-06-30T03:30:09.575Z] 用户注册成功: 111111, <EMAIL>
[2025-06-30T03:30:25.421Z] 用户登录成功: 111111
[2025-06-30T03:33:56.449Z] 验证码过期自动删除: <EMAIL>
[2025-06-30T09:51:46.232Z] 登录失败: 用户名不存在 - 11111
[2025-06-30T09:51:52.461Z] 用户登录成功: 1111
[2025-06-30T09:52:29.553Z] 用户登录成功: 111111
[2025-06-30T09:53:57.036Z] 用户登录成功: 111111
[2025-06-30T09:55:55.420Z] 用户登录成功: 111111
[2025-06-30T10:03:54.548Z] 用户登录成功: 111111
[2025-06-30T10:10:05.145Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T10:10:05.146Z] 为 <EMAIL> 生成验证码: 29****
[2025-06-30T10:10:05.146Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T10:10:05.197Z] 发送验证码失败: getaddrinfo EAI_AGAIN dm.aliyuncs.comPOST https://dm.aliyuncs.com/ failed.
Error: getaddrinfo EAI_AGAIN dm.aliyuncs.com
    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)
[2025-06-30T10:10:08.651Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T10:10:08.652Z] 为 <EMAIL> 生成验证码: 13****
[2025-06-30T10:10:08.652Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T10:10:08.655Z] 发送验证码失败: getaddrinfo EAI_AGAIN dm.aliyuncs.comPOST https://dm.aliyuncs.com/ failed.
Error: getaddrinfo EAI_AGAIN dm.aliyuncs.com
    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)
[2025-06-30T10:10:13.250Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T10:10:13.251Z] 为 <EMAIL> 生成验证码: 75****
[2025-06-30T10:10:13.252Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T10:10:13.891Z] 验证码发送成功: {"RequestId":"DF258D64-29F2-5A1D-AC86-1F96B5E09CCF","EnvId":"600000202598081389"}
[2025-06-30T10:10:13.891Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-30T10:54:44.544Z] 服务器已启动，监听端口 8080
[2025-06-30T10:54:44.572Z] 计划下次日志清理时间: 2025-07-29T17:00:00.000Z
[2025-06-30T10:56:36.885Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T10:56:36.886Z] 为 <EMAIL> 生成验证码: 14****
[2025-06-30T10:56:36.886Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T10:56:37.530Z] 验证码发送成功: {"RequestId":"AA9668EC-67C8-52EF-B8E3-902769F3B282","EnvId":"600000202713289874"}
[2025-06-30T10:56:37.530Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-30T11:06:37.534Z] 验证码过期自动删除: <EMAIL>
[2025-06-30T11:10:09.458Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T11:10:09.459Z] 为 <EMAIL> 生成验证码: 21****
[2025-06-30T11:10:09.459Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T11:10:09.889Z] 验证码发送成功: {"RequestId":"FD80E38F-E639-5AD0-ABA5-8137C14FE77E","EnvId":"600000203610984879"}
[2025-06-30T11:10:09.890Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-30T11:11:04.748Z] 用户注册成功: 1111111, <EMAIL>
[2025-06-30T11:11:26.473Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T11:11:26.474Z] 为 <EMAIL> 生成验证码: 45****
[2025-06-30T11:11:26.474Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T11:11:26.843Z] 验证码发送成功: {"RequestId":"161B4E78-369E-5F62-8C21-A9EF54687202","EnvId":"600000203610985606"}
[2025-06-30T11:11:26.844Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-30T11:11:50.661Z] 接收到验证重置码请求: 邮箱=<EMAIL>, 验证码=45****
[2025-06-30T11:11:50.661Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-30T11:11:50.662Z] 找到验证码: <EMAIL>, 存储的验证码=45****, 提交的验证码=45****
[2025-06-30T11:11:50.662Z] 密码重置验证码验证成功: <EMAIL>, 用户ID=1751281864747
[2025-06-30T11:12:06.525Z] 接收到重置密码请求: 邮箱=<EMAIL>
[2025-06-30T11:12:06.526Z] 找到验证码: <EMAIL>, 存储的验证码=45****, 提交的验证码=45****
[2025-06-30T11:12:06.575Z] 用户成功重置密码: <EMAIL>, 用户ID=1751281864747
[2025-06-30T11:13:08.243Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T11:13:08.244Z] 为 <EMAIL> 生成验证码: 10****
[2025-06-30T11:13:08.244Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T11:13:08.520Z] 验证码发送成功: {"RequestId":"0D67889D-3257-504D-B776-E5FEB2C593E9","EnvId":"600000203582192113"}
[2025-06-30T11:13:08.521Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-30T11:14:55.579Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T11:14:55.580Z] 为 <EMAIL> 生成验证码: 85****
[2025-06-30T11:14:55.580Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T11:14:55.902Z] 验证码发送成功: {"RequestId":"A7D6FB08-3B7D-5489-BF6F-49763C7DC658","EnvId":"600000204135672438"}
[2025-06-30T11:14:55.902Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-30T11:15:27.987Z] 接收到验证重置码请求: 邮箱=<EMAIL>, 验证码=85****
[2025-06-30T11:15:27.988Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-30T11:15:27.988Z] 找到验证码: <EMAIL>, 存储的验证码=85****, 提交的验证码=85****
[2025-06-30T11:15:27.988Z] 密码重置验证码验证成功: <EMAIL>, 用户ID=1751281864747
[2025-06-30T11:16:05.099Z] 接收到重置密码请求: 邮箱=<EMAIL>
[2025-06-30T11:16:05.100Z] 找到验证码: <EMAIL>, 存储的验证码=85****, 提交的验证码=85****
[2025-06-30T11:16:05.152Z] 用户成功重置密码: <EMAIL>, 用户ID=1751281864747
[2025-06-30T11:26:13.490Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T11:26:13.491Z] 为 <EMAIL> 生成验证码: 32****
[2025-06-30T11:26:13.491Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T11:26:14.049Z] 验证码发送成功: {"RequestId":"3F3C5F5C-739C-5BDC-A1D7-75D016908FDF","EnvId":"600000202742105299"}
[2025-06-30T11:26:14.050Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-30T11:26:53.875Z] 注册失败: 验证码不匹配: <EMAIL>, 存储的验证码=32****, 提交的验证码=*
[2025-06-30T11:28:01.669Z] 登录失败: 用户名不存在 - 11
[2025-06-30T11:28:05.883Z] 登录失败: 用户名不存在 - 11
[2025-06-30T11:28:07.765Z] 登录失败: 用户名不存在 - 11
[2025-06-30T11:28:10.922Z] 登录失败: 用户名不存在 - 11
[2025-06-30T11:28:27.620Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T11:28:27.620Z] 为 <EMAIL> 生成验证码: 56****
[2025-06-30T11:28:27.621Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T11:28:27.885Z] 验证码发送成功: {"RequestId":"7BC7EA5D-BA58-55B5-85B0-F426D52940EA","EnvId":"600000203610995191"}
[2025-06-30T11:28:27.885Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-30T11:28:54.779Z] 接收到验证重置码请求: 邮箱=<EMAIL>, 验证码=56****
[2025-06-30T11:28:54.780Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-30T11:28:54.781Z] 找到验证码: <EMAIL>, 存储的验证码=56****, 提交的验证码=56****
[2025-06-30T11:28:54.781Z] 密码重置验证码验证成功: <EMAIL>, 用户ID=1751281864747
[2025-06-30T11:38:27.889Z] 验证码过期自动删除: <EMAIL>
[2025-06-30T11:59:23.596Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T11:59:23.596Z] 为 <EMAIL> 生成验证码: 62****
[2025-06-30T11:59:23.597Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T11:59:24.091Z] 验证码发送成功: {"RequestId":"0EF0AECF-C0F6-51BD-A9B9-0EDABBB3B522","EnvId":"600000206044897711"}
[2025-06-30T11:59:24.092Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-30T12:09:24.100Z] 验证码过期自动删除: <EMAIL>
[2025-06-30T12:52:34.419Z] 接收到验证重置码请求: 邮箱=11, 验证码=**
[2025-06-30T12:52:34.419Z] 邮箱格式不正确: 11
[2025-06-30T12:52:35.042Z] 接收到验证重置码请求: 邮箱=11, 验证码=**
[2025-06-30T12:52:35.043Z] 邮箱格式不正确: 11
[2025-06-30T13:18:23.144Z] 登录失败: 用户名不存在 - 133123
[2025-06-30T13:26:43.395Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T13:26:43.396Z] 为 <EMAIL> 生成验证码: 98****
[2025-06-30T13:26:43.396Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T13:26:43.840Z] 验证码发送成功: {"RequestId":"775977E1-CFF0-577C-97CE-52DFC117273A","EnvId":"600000202799786689"}
[2025-06-30T13:26:43.841Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-30T13:28:15.274Z] 接收到发送验证码请求: <EMAIL>
[2025-06-30T13:28:15.275Z] 为 <EMAIL> 生成验证码: 40****
[2025-06-30T13:28:15.275Z] 尝试发送验证码到: <EMAIL>
[2025-06-30T13:28:15.562Z] 验证码发送成功: {"RequestId":"3A1F17F8-CE55-5CE1-B1E1-C1F2107D3459","EnvId":"600000202742200127"}
[2025-06-30T13:28:15.562Z] 当前验证码Map中的邮箱: <EMAIL>
[2025-06-30T13:29:02.829Z] 登录失败: 密码错误 - 1111111
[2025-06-30T13:29:07.851Z] 登录失败: 密码错误 - 1111111
[2025-06-30T13:29:17.113Z] 登录失败: 用户名不存在 - 11111
[2025-06-30T13:29:35.989Z] 登录失败: 密码错误 - 111
[2025-06-30T13:32:46.986Z] 登录失败: 密码错误 - 111
[2025-06-30T13:32:53.143Z] 用户登录成功: 111111
[2025-06-30T13:38:15.569Z] 验证码过期自动删除: <EMAIL>
[2025-06-30T13:38:59.410Z] 登录失败: 用户名不存在 - 11
[2025-06-30T13:39:01.132Z] 登录失败: 用户名不存在 - 11
[2025-06-30T13:39:02.506Z] 登录失败: 用户名不存在 - 11
[2025-06-30T13:39:05.148Z] 登录失败: 用户名不存在 - 11
[2025-06-30T13:39:08.027Z] 登录失败: 用户名不存在 - 11
[2025-06-30T13:39:50.428Z] 登录失败: 用户名不存在 - test
[2025-06-30T13:39:53.778Z] 登录失败: 用户名不存在 - test
[2025-06-30T13:39:56.195Z] 登录失败: 用户名不存在 - test
[2025-06-30T13:39:57.874Z] 登录失败: 用户名不存在 - test
[2025-06-30T13:39:59.603Z] 登录失败: 用户名不存在 - test
[2025-06-30T13:40:26.982Z] 登录失败: 密码错误 - 111111
[2025-06-30T13:40:30.871Z] 登录失败: 密码错误 - 111111
[2025-06-30T13:40:33.166Z] 登录失败: 密码错误 - 111111
[2025-06-30T13:40:35.151Z] 登录失败: 密码错误 - 111111
[2025-06-30T13:40:36.552Z] 登录失败: 密码错误 - 111111
[2025-06-30T13:41:06.579Z] 登录失败: 用户名不存在 - 11
[2025-06-30T13:50:36.300Z] 登录失败: 密码错误 - 111
[2025-06-30T13:50:38.302Z] 登录失败: 密码错误 - 111
[2025-06-30T13:50:39.261Z] 登录失败: 密码错误 - 111
[2025-06-30T13:50:40.548Z] 登录失败: 密码错误 - 111
[2025-06-30T13:50:41.464Z] 登录失败: 密码错误 - 111
[2025-06-30T13:50:42.451Z] 登录失败: 密码错误 - 111
[2025-06-30T13:50:43.397Z] 登录失败: 密码错误 - 111
[2025-06-30T13:50:44.384Z] 登录失败: 密码错误 - 111
[2025-06-30T13:50:45.364Z] 登录失败: 密码错误 - 111
[2025-06-30T13:50:46.575Z] 登录失败: 密码错误 - 111
[2025-06-30T13:54:23.367Z] 登录失败: 密码错误 - 111
[2025-06-30T13:54:27.448Z] 登录失败: 用户名不存在 - 11
[2025-06-30T13:54:28.665Z] 登录失败: 用户名不存在 - 11
[2025-06-30T13:54:29.909Z] 登录失败: 用户名不存在 - 11
[2025-06-30T13:54:31.040Z] 登录失败: 用户名不存在 - 11
[2025-06-30T13:54:32.101Z] 登录失败: 用户名不存在 - 11
[2025-06-30T13:54:59.926Z] 接收到验证重置码请求: 邮箱=<EMAIL>, 验证码=**
[2025-06-30T13:54:59.927Z] 当前验证码Map中的邮箱: 
[2025-06-30T13:54:59.927Z] 未找到该邮箱的验证码: <EMAIL>
[2025-06-30T13:55:02.042Z] 接收到验证重置码请求: 邮箱=<EMAIL>, 验证码=11**
[2025-06-30T13:55:02.043Z] 当前验证码Map中的邮箱: 
[2025-06-30T13:55:02.043Z] 未找到该邮箱的验证码: <EMAIL>
[2025-06-30T13:55:03.525Z] 接收到验证重置码请求: 邮箱=<EMAIL>, 验证码=11****
[2025-06-30T13:55:03.525Z] 当前验证码Map中的邮箱: 
[2025-06-30T13:55:03.526Z] 未找到该邮箱的验证码: <EMAIL>
[2025-06-30T13:55:04.811Z] 接收到验证重置码请求: 邮箱=<EMAIL>, 验证码=11******
[2025-06-30T13:55:04.812Z] 当前验证码Map中的邮箱: 
[2025-06-30T13:55:04.813Z] 未找到该邮箱的验证码: <EMAIL>
[2025-06-30T13:55:06.148Z] 接收到验证重置码请求: 邮箱=<EMAIL>, 验证码=11********
[2025-06-30T13:55:06.148Z] 当前验证码Map中的邮箱: 
[2025-06-30T13:55:06.149Z] 未找到该邮箱的验证码: <EMAIL>
[2025-06-30T14:29:36.405Z] 登录失败: 密码错误 - 111
[2025-06-30T14:29:38.283Z] 登录失败: 密码错误 - 111
[2025-06-30T14:29:39.332Z] 登录失败: 密码错误 - 111
[2025-06-30T14:29:40.324Z] 登录失败: 密码错误 - 111
[2025-06-30T14:29:41.165Z] 登录失败: 密码错误 - 111
[2025-06-30T14:39:05.889Z] 登录失败: 用户名不存在 - 1
[2025-06-30T14:39:07.675Z] 登录失败: 用户名不存在 - 1
[2025-06-30T14:39:08.560Z] 登录失败: 用户名不存在 - 1
[2025-06-30T14:39:09.476Z] 登录失败: 用户名不存在 - 1
[2025-06-30T14:39:10.309Z] 登录失败: 用户名不存在 - 1
[2025-06-30T14:46:09.186Z] 登录失败: 密码错误 - 111
[2025-06-30T14:47:13.434Z] 登录失败: 用户名不存在 - 1
[2025-06-30T14:50:33.510Z] 登录失败: 用户名不存在 - 1
[2025-06-30T14:50:36.967Z] 登录失败: 用户名不存在 - 1
[2025-06-30T14:52:23.476Z] 登录失败: 用户名不存在 - 1
[2025-06-30T14:53:34.743Z] 登录失败: 用户名不存在 - 1
[2025-06-30T14:53:52.183Z] 登录失败: 用户名不存在 - 1
[2025-06-30T14:53:56.174Z] 登录失败: 密码错误 - 111
[2025-06-30T14:54:13.558Z] 登录失败: 用户名不存在 - 11
[2025-06-30T14:54:28.486Z] 登录失败: 用户名不存在 - 11
[2025-06-30T14:54:33.781Z] 登录失败: 用户名不存在 - 11
[2025-06-30T14:54:44.172Z] 登录失败: 用户名不存在 - 11
[2025-06-30T14:55:44.006Z] 登录失败: 用户名不存在 - 1
[2025-06-30T14:56:59.877Z] 登录失败: 用户名不存在 - q1
[2025-06-30T14:57:57.272Z] 服务器已启动，监听端口 8080
[2025-06-30T14:57:57.294Z] 计划下次日志清理时间: 2025-07-29T17:00:00.000Z
